/* src/components/auth/AuthButtons.module.css */
.container {
    display: flex;
    align-items: center;
    gap: 10px; /* Adjusted gap */
}

.userInfo {
    font-size: 0.9rem;
    color: #e0e0e0; /* Assuming dark navbar, light text (matches Navbar.module.css) */
    white-space: nowrap; /* Prevent wrapping of user email/name */
    display: flex; /* For aligning badge with text */
    align-items: center;
    gap: 6px; /* Space between name/email and badge */
}

.button {
    padding: 7px 14px; /* Slightly adjusted padding */
    border: 1px solid #f8f9fa; /* Light border, good contrast on dark navbar */
    background-color: transparent;
    color: #f8f9fa; /* Light text color */
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.85rem; /* Slightly smaller for navbar context */
    font-weight: 500;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, border-color 0.2s ease-in-out;
    white-space: nowrap;
}

.button:hover {
    background-color: #f8f9fa;
    color: #2c3e50; /* Dark text on hover, matching navbar bg */
    border-color: #f8f9fa;
}

.signInButton {
    /* Optional: slightly different styling for sign-in button */
}

.signInButton:hover {
   /* background-color: #357ae8; */
}

.loading {
    font-size: 0.9rem;
    color: #cccccc;
    padding: 7px 14px; /* Match button padding for consistent size while loading */
}

.premiumBadge {
    background-color: #ffc107; /* Gold/yellow color for premium */
    color: #333; /* Dark text for contrast on badge */
    padding: 3px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
    margin-left: 5px; /* Space it slightly from the name/email */
}
