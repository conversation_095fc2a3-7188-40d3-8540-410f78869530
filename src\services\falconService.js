/**
 * Falcon/DeepSeek-R1 Humanization Service
 * High-performance text humanization using advanced language models
 * Replaces pattern-based approach with LLM-based humanization for ≤10% AI detection
 */

import axios from 'axios';

// Model configurations for different providers
const MODEL_CONFIGS = {
    'deepseek-r1': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/deepseek-r1',
                apiKeyEnv: 'FIREWORKS_API_KEY'
            },
            {
                name: 'novita',
                endpoint: 'https://api.novita.ai/v3/openai/chat/completions',
                model: 'deepseek-r1',
                apiKeyEnv: 'NOVITA_API_KEY'
            },
            {
                name: 'openrouter',
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                model: 'deepseek/deepseek-r1',
                apiKeyEnv: 'OPENROUTER_API_KEY'
            }
        ]
    },
    'llama-3.1-8b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/llama-v3p1-8b-instruct',
                apiKeyEnv: 'FIREWORKS_API_KEY'
            },
            {
                name: 'groq',
                endpoint: 'https://api.groq.com/openai/v1/chat/completions',
                model: 'llama-3.1-8b-instant',
                apiKeyEnv: 'GROQ_API_KEY'
            }
        ]
    },
    'mistral-7b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/mistral-7b-instruct-4k',
                apiKeyEnv: 'FIREWORKS_API_KEY'
            }
        ]
    }
};

/**
 * Generate humanization prompt based on content analysis and target detection
 */
function generateHumanizationPrompt(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        contentType = 'general'
    } = options;

    const aggressivenessLevel = aggressiveness > 0.8 ? 'aggressive' : 
                               aggressiveness > 0.5 ? 'moderate' : 'conservative';

    const prompt = `You are an expert text humanizer. Your task is to rewrite the following text to make it sound more natural and human-like while avoiding AI detection patterns.

REQUIREMENTS:
- Target AI detection score: ≤${targetDetection}%
- Humanization level: ${aggressivenessLevel}
- Content type: ${contentType}
- Maintain tone: ${maintainTone ? 'Yes' : 'No'}

HUMANIZATION STRATEGIES:
1. Vary sentence structure and length naturally
2. Use contractions and informal language where appropriate
3. Add subtle human imperfections and natural flow
4. Replace formal/robotic phrases with conversational alternatives
5. Introduce natural hesitations and qualifiers
6. Break up overly structured patterns
7. Use more varied vocabulary and expressions

PRESERVE:
- Core meaning and message
- Factual accuracy
- Professional tone (if required)
- Paragraph structure
- Key terminology

TEXT TO HUMANIZE:
${text}

HUMANIZED VERSION:`;

    return prompt;
}

/**
 * Call LLM API with proper error handling and retries
 */
async function callLLMAPI(provider, prompt, options = {}) {
    const {
        maxTokens = 4000,
        temperature = 0.7,
        topP = 0.9,
        timeout = 30000
    } = options;

    const apiKey = process.env[provider.apiKeyEnv];
    if (!apiKey) {
        throw new Error(`API key not found for ${provider.name}: ${provider.apiKeyEnv}`);
    }

    const requestData = {
        model: provider.model,
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        max_tokens: maxTokens,
        temperature: temperature,
        top_p: topP,
        stream: false
    };

    const headers = {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
    };

    // Add provider-specific headers
    if (provider.name === 'openrouter') {
        headers['HTTP-Referer'] = process.env.NEXT_PUBLIC_APP_URL || 'https://ghostlayer.netlify.app';
        headers['X-Title'] = 'GhostLayer';
    }

    try {
        console.log(`Calling ${provider.name} API with model ${provider.model}...`);
        
        const response = await axios.post(provider.endpoint, requestData, {
            headers,
            timeout
        });

        if (response.data && response.data.choices && response.data.choices[0]) {
            const humanizedText = response.data.choices[0].message.content.trim();
            
            return {
                success: true,
                text: humanizedText,
                provider: provider.name,
                model: provider.model,
                usage: response.data.usage || {},
                processingTime: Date.now()
            };
        } else {
            throw new Error('Invalid response format from API');
        }

    } catch (error) {
        console.error(`${provider.name} API error:`, error.message);
        
        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error?.message || error.response.data?.message || 'Unknown API error';
            throw new Error(`${provider.name} API error (${status}): ${message}`);
        } else if (error.code === 'ECONNABORTED') {
            throw new Error(`${provider.name} API timeout`);
        } else {
            throw new Error(`${provider.name} API error: ${error.message}`);
        }
    }
}

/**
 * Main humanization function using advanced LLMs
 */
export async function humanizeWithAdvancedLLM(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        preferredModel = 'deepseek-r1',
        maxRetries = 2
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string'
        };
    }

    const startTime = Date.now();
    
    // Analyze content type for better prompting
    const contentType = analyzeContentType(text);
    
    // Generate optimized prompt
    const prompt = generateHumanizationPrompt(text, {
        aggressiveness,
        maintainTone,
        targetDetection,
        contentType
    });

    // Try models in order of preference
    const modelOrder = [preferredModel, 'llama-3.1-8b', 'mistral-7b'];
    
    for (const modelName of modelOrder) {
        const modelConfig = MODEL_CONFIGS[modelName];
        if (!modelConfig) continue;

        // Try each provider for this model
        for (const provider of modelConfig.providers) {
            // Check if API key is available
            if (!process.env[provider.apiKeyEnv]) {
                console.log(`Skipping ${provider.name}: API key not configured`);
                continue;
            }

            let retries = 0;
            while (retries <= maxRetries) {
                try {
                    const result = await callLLMAPI(provider, prompt, {
                        maxTokens: Math.min(4000, text.length * 2),
                        temperature: aggressiveness * 0.8 + 0.2, // Scale temperature with aggressiveness
                        topP: 0.9
                    });

                    const totalTime = Date.now() - startTime;
                    
                    console.log(`Successfully humanized with ${provider.name}/${modelName} in ${totalTime}ms`);
                    
                    return {
                        ...result,
                        originalText: text,
                        processingTime: totalTime,
                        method: 'llm-advanced',
                        modelName,
                        options: { aggressiveness, maintainTone, targetDetection }
                    };

                } catch (error) {
                    retries++;
                    console.warn(`${provider.name} attempt ${retries}/${maxRetries + 1} failed:`, error.message);
                    
                    if (retries <= maxRetries) {
                        // Wait before retry
                        await new Promise(resolve => setTimeout(resolve, 1000 * retries));
                    }
                }
            }
        }
    }

    // If all LLM attempts failed
    const totalTime = Date.now() - startTime;
    return {
        success: false,
        error: 'All advanced LLM providers failed',
        originalText: text,
        processingTime: totalTime,
        method: 'failed',
        fallbackRecommended: true
    };
}

/**
 * Simple content type analysis
 */
function analyzeContentType(text) {
    const formalIndicators = /\b(therefore|furthermore|consequently|moreover|nevertheless)\b/gi;
    const technicalIndicators = /\b(API|algorithm|implementation|configuration|optimization)\b/gi;
    const academicIndicators = /\b(research|study|analysis|methodology|findings)\b/gi;
    
    if (academicIndicators.test(text)) return 'academic';
    if (technicalIndicators.test(text)) return 'technical';
    if (formalIndicators.test(text)) return 'formal';
    return 'general';
}

/**
 * Check if advanced LLM service is available
 */
export function isAdvancedLLMAvailable() {
    // Check if at least one API key is configured
    const availableKeys = [
        'FIREWORKS_API_KEY',
        'NOVITA_API_KEY', 
        'OPENROUTER_API_KEY',
        'GROQ_API_KEY'
    ].filter(key => process.env[key]);
    
    return availableKeys.length > 0;
}

/**
 * Get status of available providers
 */
export function getProviderStatus() {
    const status = {};
    
    Object.entries(MODEL_CONFIGS).forEach(([modelName, config]) => {
        status[modelName] = config.providers.map(provider => ({
            name: provider.name,
            available: !!process.env[provider.apiKeyEnv],
            model: provider.model
        }));
    });
    
    return status;
}
