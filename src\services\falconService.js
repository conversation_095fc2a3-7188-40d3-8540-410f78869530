/**
 * Falcon/DeepSeek-R1 Humanization Service
 * High-performance text humanization using advanced language models
 * Replaces pattern-based approach with LLM-based humanization for ≤10% AI detection
 */

import axios from 'axios';
import { validateWithRealTimeDetection, isRealTimeDetectionAvailable } from './aiDetectionService.js';

// Model configurations for different providers - Enhanced with Falcon models
const MODEL_CONFIGS = {
    // Primary Falcon models for optimal humanization
    'falcon-3-7b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon3-7B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon3-7B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            },
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/falcon-3-7b-instruct',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 2
            }
        ]
    },
    'falcon-h1-7b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon-H1-7B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon-H1-7B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            }
        ]
    },
    'falcon-3-10b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon3-10B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon3-10B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            }
        ]
    },
    'falcon-180b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/falcon-180B-chat/v1/chat/completions',
                model: 'tiiuae/falcon-180B-chat',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 3 // Lower priority due to cost
            }
        ]
    },
    // Fallback models (existing)
    'deepseek-r1': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/deepseek-r1',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 4
            },
            {
                name: 'novita',
                endpoint: 'https://api.novita.ai/v3/openai/chat/completions',
                model: 'deepseek-r1',
                apiKeyEnv: 'NOVITA_API_KEY',
                priority: 5
            },
            {
                name: 'openrouter',
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                model: 'deepseek/deepseek-r1',
                apiKeyEnv: 'OPENROUTER_API_KEY',
                priority: 6
            }
        ]
    },
    'llama-3.1-8b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/llama-v3p1-8b-instruct',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 7
            },
            {
                name: 'groq',
                endpoint: 'https://api.groq.com/openai/v1/chat/completions',
                model: 'llama-3.1-8b-instant',
                apiKeyEnv: 'GROQ_API_KEY',
                priority: 8
            }
        ]
    },
    'mistral-7b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/mistral-7b-instruct-4k',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 9
            }
        ]
    }
};

/**
 * Generate ultra-sophisticated humanization prompt optimized for ≤10% AI detection
 */
function generateHumanizationPrompt(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        contentType = 'general',
        modelType = 'falcon'
    } = options;

    const aggressivenessLevel = aggressiveness > 0.8 ? 'maximum' :
                               aggressiveness > 0.6 ? 'high' :
                               aggressiveness > 0.4 ? 'moderate' : 'conservative';

    // Ultra-sophisticated prompt with advanced AI detection evasion techniques
    const prompt = `You are a master text humanization expert specializing in creating undetectable human-like content. Your mission is to completely transform the following text to achieve MAXIMUM human authenticity with ≤${targetDetection}% AI detection probability.

🎯 MISSION CRITICAL OBJECTIVES:
- AI Detection Target: ≤${targetDetection}% (MANDATORY - failure is not acceptable)
- Humanization Level: ${aggressivenessLevel} intensity
- Content Classification: ${contentType}
- Tone Strategy: ${maintainTone ? 'Preserve original professional tone with human warmth' : 'Natural conversational authenticity'}
- Structure Integrity: Maintain paragraph organization and logical flow
- Factual Precision: Zero tolerance for information distortion

🧠 ADVANCED NEURAL HUMANIZATION PROTOCOLS:

1. COGNITIVE SENTENCE ARCHITECTURE:
   - Implement 3-7-12 sentence rhythm (short-medium-long patterns)
   - Insert natural cognitive pauses and thought progressions
   - Use asymmetrical sentence structures that mirror human thinking
   - Embed subtle logical leaps and intuitive connections

2. LEXICAL SOPHISTICATION ENGINE:
   - Deploy contextual synonym clusters (avoid repetition within 50-word windows)
   - Integrate domain-specific vernacular naturally
   - Use semantic field variation (formal→casual→technical transitions)
   - Implement register shifting based on content complexity

3. HUMAN IMPERFECTION INJECTION (≤3% frequency):
   - Strategic hesitation markers: "I think", "perhaps", "it seems"
   - Mild redundancy for emphasis: "really quite", "pretty much"
   - Natural qualifiers: "sort of", "kind of", "more or less"
   - Contextual uncertainty: "if I'm not mistaken", "as far as I know"

4. SYNTACTIC DISRUPTION PATTERNS:
   - Break parallel structures with intentional asymmetry
   - Use elliptical constructions and implied subjects
   - Implement conversational fragments and incomplete thoughts
   - Deploy strategic comma splices and run-on sentences

5. COGNITIVE FLOW OPTIMIZATION:
   - Replace mechanical transitions with thought-based connectors
   - Use experiential language: "In my experience", "What I've noticed"
   - Implement perspective shifts: "From another angle", "Looking at it differently"
   - Add metacognitive commentary: "This makes me think", "It's interesting that"

6. AUTHENTICITY AMPLIFIERS:
   - Inject personal observations and relatable analogies
   - Use temporal markers: "these days", "lately", "recently"
   - Include cultural references and shared experiences
   - Deploy emotional undertones appropriate to content

🚫 AI DETECTION ELIMINATION TARGETS:
- Formulaic openings/closings ("In conclusion", "To summarize")
- Mechanical enumeration ("Firstly", "Secondly", "Finally")
- Robotic qualifiers ("It is important to note", "It should be mentioned")
- Artificial enthusiasm and corporate speak
- Predictable paragraph structures and topic sentences
- Overuse of passive voice and nominalizations
- Repetitive sentence lengths and rhythms

✅ PRESERVATION MANDATES:
- Core message integrity and factual accuracy
- Technical terminology and specialized language
- Logical argument structure and evidence
- Professional credibility and authority
- Original paragraph boundaries and formatting
- Key data points and statistical information

📝 CONTENT TO HUMANIZE:
${text}

🎭 HUMANIZED OUTPUT (Target: ≤${targetDetection}% AI Detection):`;

    return prompt;
}

/**
 * Enhanced API call function with Falcon-specific optimizations
 */
async function callLLMAPI(provider, prompt, options = {}) {
    const {
        maxTokens = 4000,
        temperature = 0.7,
        topP = 0.9,
        timeout = 45000, // Increased timeout for Falcon models
        modelType = 'falcon'
    } = options;

    const apiKey = process.env[provider.apiKeyEnv];
    if (!apiKey) {
        throw new Error(`API key not found for ${provider.name}: ${provider.apiKeyEnv}`);
    }

    // Optimize parameters for Falcon models with fine-tuned settings
    const optimizedParams = optimizeParametersForModel(provider.model, {
        temperature,
        topP,
        maxTokens,
        modelType,
        targetDetection: options.targetDetection || 10
    });

    const requestData = {
        model: provider.model,
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        max_tokens: optimizedParams.maxTokens,
        temperature: optimizedParams.temperature,
        top_p: optimizedParams.topP,
        stream: false
    };

    // Add fine-tuned model-specific parameters for ≤10% AI detection
    if (modelType === 'falcon') {
        requestData.repetition_penalty = optimizedParams.repetitionPenalty || 1.15;
        requestData.presence_penalty = optimizedParams.presencePenalty || 0.3;
        requestData.frequency_penalty = optimizedParams.frequencyPenalty || 0.4;
        requestData.do_sample = true;
        requestData.pad_token_id = 50256; // Ensure proper tokenization
    } else {
        // Apply optimized parameters for non-Falcon models
        if (optimizedParams.repetitionPenalty) {
            requestData.repetition_penalty = optimizedParams.repetitionPenalty;
        }
        if (optimizedParams.presencePenalty) {
            requestData.presence_penalty = optimizedParams.presencePenalty;
        }
        if (optimizedParams.frequencyPenalty) {
            requestData.frequency_penalty = optimizedParams.frequencyPenalty;
        }
    }

    const headers = {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
    };

    // Add provider-specific headers and configurations
    if (provider.name === 'openrouter') {
        headers['HTTP-Referer'] = process.env.NEXT_PUBLIC_APP_URL || 'https://ghostlayer.netlify.app';
        headers['X-Title'] = 'GhostLayer';
    } else if (provider.name === 'huggingface') {
        headers['X-Use-Cache'] = 'false'; // Ensure fresh responses
        headers['X-Wait-For-Model'] = 'true'; // Wait for model to load
    }

    try {
        console.log(`Calling ${provider.name} API with Falcon model ${provider.model}...`);

        const startTime = Date.now();
        const response = await axios.post(provider.endpoint, requestData, {
            headers,
            timeout
        });
        const responseTime = Date.now() - startTime;

        if (response.data && response.data.choices && response.data.choices[0]) {
            const humanizedText = response.data.choices[0].message.content.trim();

            // Validate Falcon model response quality
            if (!validateFalconResponse(humanizedText, prompt)) {
                throw new Error('Falcon model response failed quality validation');
            }

            return {
                success: true,
                text: humanizedText,
                provider: provider.name,
                model: provider.model,
                usage: response.data.usage || {},
                processingTime: responseTime,
                modelType: modelType,
                optimizedParams: optimizedParams
            };
        } else {
            throw new Error('Invalid response format from API');
        }

    } catch (error) {
        console.error(`${provider.name} API error:`, error.message);

        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error?.message || error.response.data?.message || 'Unknown API error';

            // Handle Falcon-specific errors
            if (status === 503 && provider.name === 'huggingface') {
                throw new Error(`${provider.name} model loading (${status}): ${message}. Please retry in a few moments.`);
            }

            throw new Error(`${provider.name} API error (${status}): ${message}`);
        } else if (error.code === 'ECONNABORTED') {
            throw new Error(`${provider.name} API timeout - Falcon models may need more time`);
        } else {
            throw new Error(`${provider.name} API error: ${error.message}`);
        }
    }
}

/**
 * Fine-tuned parameters for specific Falcon models optimized for ≤10% AI detection
 */
function optimizeParametersForModel(modelName, params) {
    const { temperature, topP, maxTokens, modelType, targetDetection = 10 } = params;

    // Falcon 3-7B: Balanced performance with optimized creativity
    if (modelName.includes('Falcon3-7B') || modelName.includes('falcon-3-7b')) {
        return {
            temperature: targetDetection <= 10 ? 0.85 : Math.min(temperature * 0.9, 0.8),
            topP: 0.88, // Slightly higher for more diverse vocabulary
            maxTokens: Math.min(maxTokens, 3800),
            repetitionPenalty: 1.15, // Increased to reduce AI-like repetition
            presencePenalty: 0.3, // Encourage topic diversity
            frequencyPenalty: 0.4 // Reduce word repetition
        };
    }

    // Falcon-H1-7B: Hybrid architecture optimized for efficiency and naturalness
    if (modelName.includes('Falcon-H1-7B') || modelName.includes('falcon-h1-7b')) {
        return {
            temperature: targetDetection <= 10 ? 0.82 : Math.min(temperature * 0.85, 0.75),
            topP: 0.86, // Focused but creative sampling
            maxTokens: Math.min(maxTokens, 3600),
            repetitionPenalty: 1.18, // Higher penalty for hybrid model
            presencePenalty: 0.25,
            frequencyPenalty: 0.35
        };
    }

    // Falcon 3-10B: High-performance model for demanding tasks
    if (modelName.includes('Falcon3-10B') || modelName.includes('falcon-3-10b')) {
        return {
            temperature: targetDetection <= 10 ? 0.88 : Math.min(temperature * 0.95, 0.85),
            topP: 0.90, // Higher creativity for larger model
            maxTokens: Math.min(maxTokens, 4000),
            repetitionPenalty: 1.12, // Lower penalty as larger model handles diversity better
            presencePenalty: 0.35,
            frequencyPenalty: 0.45
        };
    }

    // Falcon 180B: Premium model with maximum capabilities
    if (modelName.includes('falcon-180B') || modelName.includes('falcon-180b')) {
        return {
            temperature: targetDetection <= 10 ? 0.90 : Math.min(temperature * 1.0, 0.9),
            topP: 0.92, // Maximum creativity for largest model
            maxTokens: Math.min(maxTokens, 4500),
            repetitionPenalty: 1.08, // Minimal penalty for most capable model
            presencePenalty: 0.4,
            frequencyPenalty: 0.5
        };
    }

    // DeepSeek optimizations (existing fallback)
    if (modelName.includes('deepseek')) {
        return {
            temperature: targetDetection <= 10 ? temperature * 1.15 : temperature * 1.1,
            topP: topP,
            maxTokens: maxTokens,
            repetitionPenalty: 1.1,
            presencePenalty: 0.2,
            frequencyPenalty: 0.3
        };
    }

    // Llama optimizations
    if (modelName.includes('llama') || modelName.includes('Llama')) {
        return {
            temperature: targetDetection <= 10 ? temperature * 1.1 : temperature,
            topP: Math.min(topP, 0.9),
            maxTokens: maxTokens,
            repetitionPenalty: 1.05,
            presencePenalty: 0.15,
            frequencyPenalty: 0.25
        };
    }

    // Default parameters for unknown models
    return {
        temperature,
        topP,
        maxTokens,
        repetitionPenalty: 1.1,
        presencePenalty: 0.2,
        frequencyPenalty: 0.3
    };
}

/**
 * Validate Falcon model response quality
 */
function validateFalconResponse(response, originalPrompt) {
    if (!response || response.length < 10) {
        return false;
    }

    // Check for common Falcon model issues
    if (response.includes('I cannot') || response.includes('I apologize')) {
        return false;
    }

    // Check for repetitive patterns (common in Falcon models)
    const sentences = response.split(/[.!?]+/);
    if (sentences.length > 3) {
        const uniqueSentences = new Set(sentences.map(s => s.trim().toLowerCase()));
        if (uniqueSentences.size / sentences.length < 0.7) {
            return false; // Too repetitive
        }
    }

    return true;
}

/**
 * Main humanization function using advanced LLMs
 */
export async function humanizeWithAdvancedLLM(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        preferredModel = 'deepseek-r1',
        maxRetries = 2
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string'
        };
    }

    const startTime = Date.now();
    
    // Analyze content type for better prompting
    const contentType = analyzeContentType(text);
    
    // Generate optimized prompt
    const prompt = generateHumanizationPrompt(text, {
        aggressiveness,
        maintainTone,
        targetDetection,
        contentType
    });

    // Enhanced intelligent model selection with content analysis
    const modelOrder = getOptimalModelOrder(preferredModel, targetDetection, aggressiveness, text);

    for (const modelName of modelOrder) {
        const modelConfig = MODEL_CONFIGS[modelName];
        if (!modelConfig) continue;

        // Sort providers by priority
        const sortedProviders = modelConfig.providers.sort((a, b) => (a.priority || 999) - (b.priority || 999));

        // Try each provider for this model
        for (const provider of sortedProviders) {
            // Check if API key is available
            if (!process.env[provider.apiKeyEnv]) {
                console.log(`Skipping ${provider.name}: API key not configured`);
                continue;
            }

            let retries = 0;
            while (retries <= maxRetries) {
                try {
                    const modelType = modelName.includes('falcon') ? 'falcon' : 'other';

                    const result = await callLLMAPI(provider, prompt, {
                        maxTokens: Math.min(4000, text.length * 2.5), // Increased for Falcon models
                        temperature: calculateOptimalTemperature(aggressiveness, modelType),
                        topP: modelType === 'falcon' ? 0.85 : 0.9,
                        modelType: modelType,
                        targetDetection: targetDetection
                    });

                    const totalTime = Date.now() - startTime;

                    console.log(`Successfully humanized with ${provider.name}/${modelName} in ${totalTime}ms`);

                    // Multi-pass processing for ≤10% AI detection targets
                    let finalResult = result;
                    if (targetDetection <= 10 && result.success) {
                        console.log(`Applying multi-pass refinement for ≤${targetDetection}% detection target...`);
                        finalResult = await applyMultiPassRefinement(result, {
                            originalText: text,
                            targetDetection,
                            aggressiveness,
                            modelName,
                            provider
                        });
                    }

                    // Real-time AI detection validation for ≤10% targets
                    let detectionValidation = null;
                    if (targetDetection <= 10 && isRealTimeDetectionAvailable()) {
                        console.log('Performing real-time AI detection validation...');
                        detectionValidation = await validateWithRealTimeDetection(finalResult.text, {
                            targetDetection,
                            preferredAPI: 'gptzero',
                            fallbackAPIs: ['originality', 'sapling']
                        });

                        if (detectionValidation.success && !detectionValidation.meetsTarget) {
                            console.log(`Real-time detection: ${detectionValidation.score.toFixed(1)}% > ${targetDetection}%`);

                            // Auto-retry with higher aggressiveness if detection score is too high
                            if (detectionValidation.recommendation.shouldRetry && retries === 0) {
                                console.log('Auto-retrying with increased aggressiveness...');
                                const newAggressiveness = Math.min(aggressiveness + detectionValidation.recommendation.suggestedAggressiveness, 1.0);

                                // Recursive retry with higher aggressiveness
                                return await humanizeWithAdvancedLLM(text, {
                                    aggressiveness: newAggressiveness,
                                    maintainTone,
                                    targetDetection,
                                    preferredModel: modelName,
                                    maxRetries: 0 // Prevent infinite recursion
                                });
                            }
                        }
                    }

                    // Fallback to heuristic validation if real-time detection unavailable
                    const validationResult = detectionValidation || await validateDetectionTarget(finalResult.text, targetDetection);
                    const meetsTarget = detectionValidation ? detectionValidation.meetsTarget : validationResult;

                    if (meetsTarget) {
                        return {
                            ...finalResult,
                            originalText: text,
                            processingTime: totalTime + (finalResult.refinementTime || 0),
                            method: finalResult.multiPass ? 'llm-advanced-falcon-multipass' : 'llm-advanced-falcon',
                            modelName,
                            detectionTarget: targetDetection,
                            detectionValidation: detectionValidation,
                            options: { aggressiveness, maintainTone, targetDetection }
                        };
                    } else {
                        const score = detectionValidation ? detectionValidation.score : 'unknown';
                        console.warn(`${modelName} result detection score ${score}% exceeds ≤${targetDetection}% target, trying next model`);
                        break; // Try next model instead of retrying same model
                    }

                } catch (error) {
                    retries++;
                    console.warn(`${provider.name} attempt ${retries}/${maxRetries + 1} failed:`, error.message);

                    if (retries <= maxRetries) {
                        // Progressive backoff with longer delays for Falcon models
                        const delay = modelType === 'falcon' ? 2000 * retries : 1000 * retries;
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }
            }
        }
    }

    // If all LLM attempts failed
    const totalTime = Date.now() - startTime;
    return {
        success: false,
        error: 'All advanced LLM providers failed',
        originalText: text,
        processingTime: totalTime,
        method: 'failed',
        fallbackRecommended: true
    };
}

/**
 * Enhanced content type analysis for better Falcon model prompting
 */
function analyzeContentType(text) {
    const formalIndicators = /\b(therefore|furthermore|consequently|moreover|nevertheless|thus|hence)\b/gi;
    const technicalIndicators = /\b(API|algorithm|implementation|configuration|optimization|framework|architecture|deployment)\b/gi;
    const academicIndicators = /\b(research|study|analysis|methodology|findings|hypothesis|conclusion|evidence)\b/gi;
    const businessIndicators = /\b(strategy|revenue|market|customer|business|sales|profit|ROI)\b/gi;
    const creativeIndicators = /\b(story|narrative|creative|artistic|design|aesthetic|inspiration)\b/gi;

    if (academicIndicators.test(text)) return 'academic';
    if (technicalIndicators.test(text)) return 'technical';
    if (businessIndicators.test(text)) return 'business';
    if (creativeIndicators.test(text)) return 'creative';
    if (formalIndicators.test(text)) return 'formal';
    return 'general';
}

/**
 * Enhanced intelligent model selection based on content analysis and target detection
 */
function getOptimalModelOrder(preferredModel, targetDetection, aggressiveness, text = '') {
    // Analyze content for AI patterns to determine optimal model selection
    const contentAnalysis = analyzeContentComplexity(text);

    // For ≤10% AI detection target, use sophisticated model selection
    if (targetDetection <= 10) {
        console.log(`Content analysis: AI risk=${contentAnalysis.aiRisk}, complexity=${contentAnalysis.complexity}, length=${contentAnalysis.length}`);

        // High AI risk content needs most powerful models
        if (contentAnalysis.aiRisk >= 7 || aggressiveness >= 0.8) {
            console.log('High AI risk detected, using maximum power models');
            return ['falcon-180b', 'falcon-3-10b', 'falcon-h1-7b', 'falcon-3-7b', preferredModel, 'deepseek-r1'];
        }

        // Medium AI risk with complex content
        if (contentAnalysis.aiRisk >= 4 || contentAnalysis.complexity >= 6 || aggressiveness >= 0.6) {
            console.log('Medium-high AI risk, using powerful models');
            return ['falcon-3-10b', 'falcon-h1-7b', 'falcon-3-7b', 'falcon-180b', preferredModel, 'deepseek-r1'];
        }

        // Standard ≤10% detection with moderate content
        if (aggressiveness >= 0.4) {
            console.log('Standard ≤10% detection, using balanced models');
            return ['falcon-3-7b', 'falcon-h1-7b', 'falcon-3-10b', preferredModel, 'deepseek-r1', 'llama-3.1-8b'];
        }

        // Low aggressiveness but still ≤10% target
        console.log('Low aggressiveness ≤10% target, using efficient models');
        return ['falcon-h1-7b', 'falcon-3-7b', 'falcon-3-10b', preferredModel, 'deepseek-r1'];
    }

    // For higher detection targets (>10%), use standard model order
    if (targetDetection <= 20) {
        return ['falcon-3-7b', preferredModel, 'falcon-h1-7b', 'deepseek-r1', 'llama-3.1-8b', 'mistral-7b'];
    }

    // For relaxed detection targets (>20%), prioritize speed over sophistication
    return [preferredModel, 'falcon-3-7b', 'deepseek-r1', 'llama-3.1-8b', 'mistral-7b'];
}

/**
 * Analyze content complexity and AI risk patterns
 */
function analyzeContentComplexity(text) {
    if (!text || typeof text !== 'string') {
        return { aiRisk: 0, complexity: 0, length: 0 };
    }

    let aiRisk = 0;
    let complexity = 0;

    // AI risk pattern analysis
    const highRiskPatterns = [
        /\b(furthermore|moreover|consequently|therefore|thus|hence|additionally|similarly)\b/gi,
        /\b(it is important to note|it should be noted|it is worth mentioning|it must be emphasized)\b/gi,
        /\b(in conclusion|to summarize|in summary|to conclude|finally|lastly)\b/gi,
        /\b(comprehensive|extensive|significant|substantial|considerable|numerous|various)\b/gi
    ];

    const mediumRiskPatterns = [
        /\b(clearly|obviously|certainly|definitely|undoubtedly|unquestionably)\b/gi,
        /\b(is|are|was|were|been|being)\s+\w+ed\b/gi,
        /\b(implementation|optimization|utilization|maximization|minimization)\b/gi
    ];

    // Count high-risk patterns (weight: 2)
    highRiskPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        aiRisk += matches.length * 2;
    });

    // Count medium-risk patterns (weight: 1)
    mediumRiskPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        aiRisk += matches.length;
    });

    // Complexity analysis
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    const words = text.split(/\s+/).length;
    const uniqueWords = new Set(text.toLowerCase().split(/\s+/)).size;
    const lexicalDiversity = uniqueWords / words;

    // Calculate complexity score (0-10)
    if (avgSentenceLength > 35) complexity += 2;
    if (avgSentenceLength > 50) complexity += 2;
    if (words > 500) complexity += 1;
    if (words > 1000) complexity += 2;
    if (lexicalDiversity < 0.4) complexity += 2; // Low diversity = more complex to humanize
    if (lexicalDiversity < 0.3) complexity += 1;

    // Technical content indicators
    const technicalPatterns = [
        /\b(API|algorithm|implementation|configuration|optimization|framework|architecture)\b/gi,
        /\b(methodology|analysis|evaluation|assessment|validation|verification)\b/gi
    ];

    technicalPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        complexity += Math.min(matches.length, 3); // Cap technical complexity contribution
    });

    return {
        aiRisk: Math.min(aiRisk, 10), // Cap at 10
        complexity: Math.min(complexity, 10), // Cap at 10
        length: text.length,
        avgSentenceLength: avgSentenceLength.toFixed(1),
        lexicalDiversity: lexicalDiversity.toFixed(3),
        wordCount: words
    };
}

/**
 * Calculate optimal temperature based on aggressiveness and model type
 */
function calculateOptimalTemperature(aggressiveness, modelType) {
    const baseTemp = aggressiveness * 0.8 + 0.2;

    if (modelType === 'falcon') {
        // Falcon models work better with slightly lower temperature
        return Math.min(baseTemp * 0.9, 0.8);
    }

    return baseTemp;
}

/**
 * Multi-pass refinement for enhanced humanization targeting ≤10% AI detection
 */
async function applyMultiPassRefinement(initialResult, options) {
    const { originalText, targetDetection, aggressiveness, modelName, provider } = options;
    const startTime = Date.now();

    try {
        // Analyze the initial result for remaining AI patterns
        const aiAnalysis = analyzeRemainingAIPatterns(initialResult.text);

        if (aiAnalysis.riskScore <= 2) {
            console.log('Initial result already meets high quality standards, skipping refinement');
            return initialResult;
        }

        console.log(`AI risk score: ${aiAnalysis.riskScore}/10, applying targeted refinement...`);

        // Generate refinement prompt targeting specific issues
        const refinementPrompt = generateRefinementPrompt(initialResult.text, aiAnalysis, {
            targetDetection,
            aggressiveness: Math.min(aggressiveness + 0.2, 1.0), // Increase aggressiveness for refinement
            originalText
        });

        // Apply secondary refinement pass
        const refinementResult = await callLLMAPI(provider, refinementPrompt, {
            maxTokens: Math.min(3500, initialResult.text.length * 2),
            temperature: 0.9, // Higher temperature for more creativity in refinement
            topP: 0.92,
            modelType: 'falcon',
            targetDetection: targetDetection
        });

        if (refinementResult.success) {
            const refinementTime = Date.now() - startTime;
            console.log(`Multi-pass refinement completed in ${refinementTime}ms`);

            return {
                ...refinementResult,
                multiPass: true,
                refinementTime: refinementTime,
                aiAnalysis: aiAnalysis,
                originalResult: initialResult.text
            };
        } else {
            console.warn('Refinement pass failed, returning initial result');
            return initialResult;
        }

    } catch (error) {
        console.error('Multi-pass refinement error:', error.message);
        return initialResult; // Return original result if refinement fails
    }
}

/**
 * Analyze text for remaining AI detection patterns
 */
function analyzeRemainingAIPatterns(text) {
    const patterns = {
        formalTransitions: /\b(furthermore|moreover|consequently|therefore|thus|hence|additionally|similarly)\b/gi,
        roboticPhrases: /\b(it is important to note|it should be noted|it is worth mentioning|it must be emphasized)\b/gi,
        mechanicalConclusions: /\b(in conclusion|to summarize|in summary|to conclude|finally)\b/gi,
        overqualification: /\b(comprehensive|extensive|significant|substantial|considerable|numerous|various)\b/gi,
        passiveOveruse: /\b(is|are|was|were|been|being)\s+\w+ed\b/gi,
        repetitiveStructure: /^[A-Z][^.!?]*[.!?]\s+[A-Z][^.!?]*[.!?]\s+[A-Z][^.!?]*[.!?]/gm
    };

    let riskScore = 0;
    const issues = [];

    Object.entries(patterns).forEach(([category, pattern]) => {
        const matches = text.match(pattern) || [];
        if (matches.length > 0) {
            riskScore += matches.length;
            issues.push({
                category,
                count: matches.length,
                examples: matches.slice(0, 3)
            });
        }
    });

    // Additional checks
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;

    if (avgSentenceLength > 25) {
        riskScore += 2;
        issues.push({ category: 'longSentences', count: 1, examples: ['Average sentence length too high'] });
    }

    return {
        riskScore: Math.min(riskScore, 10),
        issues,
        recommendations: generateRefinementRecommendations(issues)
    };
}

/**
 * Generate targeted refinement prompt based on AI pattern analysis
 */
function generateRefinementPrompt(text, aiAnalysis, options) {
    const { targetDetection, aggressiveness, originalText } = options;

    const issueDescriptions = aiAnalysis.issues.map(issue =>
        `- ${issue.category}: ${issue.count} instances (e.g., "${issue.examples[0]}")`
    ).join('\n');

    return `You are performing a CRITICAL REFINEMENT PASS to eliminate remaining AI detection patterns. The text below has already been humanized but still contains ${aiAnalysis.riskScore}/10 AI risk factors.

🎯 REFINEMENT MISSION: Achieve ≤${targetDetection}% AI detection by targeting these specific issues:

${issueDescriptions}

🔧 TARGETED REFINEMENT STRATEGIES:
1. ELIMINATE FORMAL TRANSITIONS: Replace with natural thought connectors
2. BREAK REPETITIVE PATTERNS: Vary sentence structures dramatically
3. INJECT HUMAN SPONTANEITY: Add natural digressions and personal touches
4. REDUCE PASSIVE VOICE: Convert to active, engaging language
5. ADD CONVERSATIONAL ELEMENTS: Include natural speech patterns
6. VARY SENTENCE RHYTHM: Mix short punchy sentences with flowing longer ones

🚫 CRITICAL ELIMINATION TARGETS:
- All instances of "furthermore", "moreover", "consequently"
- Robotic phrases like "it is important to note"
- Mechanical conclusions and summaries
- Overuse of qualifying adjectives
- Predictable sentence patterns

✅ PRESERVE COMPLETELY:
- Core message and factual content
- Technical accuracy and data
- Professional credibility
- Logical flow and structure

TEXT TO REFINE:
${text}

ULTRA-HUMANIZED REFINEMENT (≤${targetDetection}% AI detection):`;
}

/**
 * Generate specific recommendations for refinement
 */
function generateRefinementRecommendations(issues) {
    const recommendations = [];

    issues.forEach(issue => {
        switch (issue.category) {
            case 'formalTransitions':
                recommendations.push('Replace formal transitions with conversational connectors');
                break;
            case 'roboticPhrases':
                recommendations.push('Eliminate robotic qualifying phrases');
                break;
            case 'mechanicalConclusions':
                recommendations.push('Use natural ending patterns instead of formal conclusions');
                break;
            case 'overqualification':
                recommendations.push('Reduce excessive qualifying adjectives');
                break;
            case 'passiveOveruse':
                recommendations.push('Convert passive voice to active constructions');
                break;
            case 'repetitiveStructure':
                recommendations.push('Vary sentence structures and lengths');
                break;
            case 'longSentences':
                recommendations.push('Break up overly long sentences');
                break;
        }
    });

    return recommendations;
}

/**
 * Enhanced validation with more sophisticated AI pattern detection
 */
async function validateDetectionTarget(text, targetDetection) {
    // Enhanced heuristic validation - in production, this would call an AI detection API

    const aiPatterns = [
        { pattern: /\b(furthermore|moreover|consequently|therefore|thus|hence)\b/gi, weight: 3 },
        { pattern: /\b(it is important to note|it should be noted|it is worth mentioning)\b/gi, weight: 4 },
        { pattern: /\b(in conclusion|to summarize|in summary)\b/gi, weight: 3 },
        { pattern: /\b(comprehensive|extensive|significant|substantial|considerable)\b/gi, weight: 2 },
        { pattern: /\b(numerous|various|multiple|several)\s+\w+/gi, weight: 1 },
        { pattern: /\b(is|are|was|were)\s+\w+ed\b/gi, weight: 1 }
    ];

    let weightedScore = 0;
    let totalMatches = 0;

    aiPatterns.forEach(({ pattern, weight }) => {
        const matches = text.match(pattern) || [];
        weightedScore += matches.length * weight;
        totalMatches += matches.length;
    });

    // Calculate estimated detection percentage
    const textLength = text.length;
    const density = (weightedScore / textLength) * 1000; // Patterns per 1000 characters
    const estimatedDetection = Math.min(density * 8, 95); // Adjusted multiplier

    console.log(`Detection validation: ${totalMatches} patterns, density: ${density.toFixed(2)}, estimated: ${estimatedDetection.toFixed(1)}%`);

    return estimatedDetection <= targetDetection;
}

/**
 * Check if advanced LLM service is available
 */
export function isAdvancedLLMAvailable() {
    // Check if at least one API key is configured
    const availableKeys = [
        'FIREWORKS_API_KEY',
        'NOVITA_API_KEY', 
        'OPENROUTER_API_KEY',
        'GROQ_API_KEY'
    ].filter(key => process.env[key]);
    
    return availableKeys.length > 0;
}

/**
 * Get status of available providers
 */
export function getProviderStatus() {
    const status = {};
    
    Object.entries(MODEL_CONFIGS).forEach(([modelName, config]) => {
        status[modelName] = config.providers.map(provider => ({
            name: provider.name,
            available: !!process.env[provider.apiKeyEnv],
            model: provider.model
        }));
    });
    
    return status;
}
