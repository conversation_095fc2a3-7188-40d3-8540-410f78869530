/**
 * Falcon/DeepSeek-R1 Humanization Service
 * High-performance text humanization using advanced language models
 * Replaces pattern-based approach with LLM-based humanization for ≤10% AI detection
 */

import axios from 'axios';

// Model configurations for different providers - Enhanced with Falcon models
const MODEL_CONFIGS = {
    // Primary Falcon models for optimal humanization
    'falcon-3-7b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon3-7B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon3-7B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            },
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/falcon-3-7b-instruct',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 2
            }
        ]
    },
    'falcon-h1-7b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon-H1-7B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon-H1-7B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            }
        ]
    },
    'falcon-3-10b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon3-10B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon3-10B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            }
        ]
    },
    'falcon-180b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/falcon-180B-chat/v1/chat/completions',
                model: 'tiiuae/falcon-180B-chat',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 3 // Lower priority due to cost
            }
        ]
    },
    // Fallback models (existing)
    'deepseek-r1': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/deepseek-r1',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 4
            },
            {
                name: 'novita',
                endpoint: 'https://api.novita.ai/v3/openai/chat/completions',
                model: 'deepseek-r1',
                apiKeyEnv: 'NOVITA_API_KEY',
                priority: 5
            },
            {
                name: 'openrouter',
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                model: 'deepseek/deepseek-r1',
                apiKeyEnv: 'OPENROUTER_API_KEY',
                priority: 6
            }
        ]
    },
    'llama-3.1-8b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/llama-v3p1-8b-instruct',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 7
            },
            {
                name: 'groq',
                endpoint: 'https://api.groq.com/openai/v1/chat/completions',
                model: 'llama-3.1-8b-instant',
                apiKeyEnv: 'GROQ_API_KEY',
                priority: 8
            }
        ]
    },
    'mistral-7b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/mistral-7b-instruct-4k',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 9
            }
        ]
    }
};

/**
 * Generate enhanced humanization prompt optimized for Falcon models and ≤10% AI detection
 */
function generateHumanizationPrompt(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        contentType = 'general',
        modelType = 'falcon'
    } = options;

    const aggressivenessLevel = aggressiveness > 0.8 ? 'aggressive' :
                               aggressiveness > 0.5 ? 'moderate' : 'conservative';

    // Enhanced prompt specifically designed for Falcon models and better AI detection avoidance
    const prompt = `You are an expert text humanization specialist with deep knowledge of natural human writing patterns. Your task is to transform the following AI-generated or formal text into naturally flowing, human-written content that achieves ≤${targetDetection}% AI detection while maintaining quality and meaning.

CRITICAL REQUIREMENTS:
- Target AI detection score: ≤${targetDetection}% (STRICT requirement)
- Humanization intensity: ${aggressivenessLevel}
- Content type: ${contentType}
- Tone preservation: ${maintainTone ? 'Maintain original tone and style' : 'Use natural, conversational tone'}
- Preserve exact paragraph structure and formatting
- Maintain factual accuracy and core message

ADVANCED HUMANIZATION TECHNIQUES:
1. SENTENCE VARIATION: Mix short, medium, and long sentences naturally
2. NATURAL FLOW: Add subtle transitions, connectors, and human-like reasoning
3. VOCABULARY DIVERSITY: Replace repetitive words with contextual synonyms
4. HUMAN IMPERFECTIONS: Include minor inconsistencies, natural hesitations (≤5% frequency)
5. CONVERSATIONAL ELEMENTS: Use contractions, informal phrases where appropriate
6. STRUCTURAL DISRUPTION: Break predictable AI patterns and formal structures
7. CONTEXTUAL AWARENESS: Adapt language to content type and audience
8. AUTHENTIC VOICE: Inject personality while maintaining professionalism

AVOID THESE AI PATTERNS:
- Repetitive sentence structures
- Overly formal or robotic language
- Predictable transitions ("Furthermore", "Moreover", "In conclusion")
- Perfect grammar without natural variations
- Mechanical listing patterns
- Artificial enthusiasm or politeness

PRESERVE ABSOLUTELY:
- All factual information and data
- Technical terminology and proper nouns
- Original paragraph breaks and structure
- Key concepts and main arguments
- Professional credibility

ORIGINAL TEXT:
${text}

HUMANIZED VERSION (≤${targetDetection}% AI detection):`;

    return prompt;
}

/**
 * Enhanced API call function with Falcon-specific optimizations
 */
async function callLLMAPI(provider, prompt, options = {}) {
    const {
        maxTokens = 4000,
        temperature = 0.7,
        topP = 0.9,
        timeout = 45000, // Increased timeout for Falcon models
        modelType = 'falcon'
    } = options;

    const apiKey = process.env[provider.apiKeyEnv];
    if (!apiKey) {
        throw new Error(`API key not found for ${provider.name}: ${provider.apiKeyEnv}`);
    }

    // Optimize parameters for Falcon models
    const optimizedParams = optimizeParametersForModel(provider.model, {
        temperature,
        topP,
        maxTokens,
        modelType
    });

    const requestData = {
        model: provider.model,
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        max_tokens: optimizedParams.maxTokens,
        temperature: optimizedParams.temperature,
        top_p: optimizedParams.topP,
        stream: false
    };

    // Add model-specific parameters
    if (modelType === 'falcon') {
        requestData.repetition_penalty = 1.1; // Reduce repetition in Falcon models
        requestData.do_sample = true;
    }

    const headers = {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
    };

    // Add provider-specific headers and configurations
    if (provider.name === 'openrouter') {
        headers['HTTP-Referer'] = process.env.NEXT_PUBLIC_APP_URL || 'https://ghostlayer.netlify.app';
        headers['X-Title'] = 'GhostLayer';
    } else if (provider.name === 'huggingface') {
        headers['X-Use-Cache'] = 'false'; // Ensure fresh responses
        headers['X-Wait-For-Model'] = 'true'; // Wait for model to load
    }

    try {
        console.log(`Calling ${provider.name} API with Falcon model ${provider.model}...`);

        const startTime = Date.now();
        const response = await axios.post(provider.endpoint, requestData, {
            headers,
            timeout
        });
        const responseTime = Date.now() - startTime;

        if (response.data && response.data.choices && response.data.choices[0]) {
            const humanizedText = response.data.choices[0].message.content.trim();

            // Validate Falcon model response quality
            if (!validateFalconResponse(humanizedText, prompt)) {
                throw new Error('Falcon model response failed quality validation');
            }

            return {
                success: true,
                text: humanizedText,
                provider: provider.name,
                model: provider.model,
                usage: response.data.usage || {},
                processingTime: responseTime,
                modelType: modelType,
                optimizedParams: optimizedParams
            };
        } else {
            throw new Error('Invalid response format from API');
        }

    } catch (error) {
        console.error(`${provider.name} API error:`, error.message);

        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error?.message || error.response.data?.message || 'Unknown API error';

            // Handle Falcon-specific errors
            if (status === 503 && provider.name === 'huggingface') {
                throw new Error(`${provider.name} model loading (${status}): ${message}. Please retry in a few moments.`);
            }

            throw new Error(`${provider.name} API error (${status}): ${message}`);
        } else if (error.code === 'ECONNABORTED') {
            throw new Error(`${provider.name} API timeout - Falcon models may need more time`);
        } else {
            throw new Error(`${provider.name} API error: ${error.message}`);
        }
    }
}

/**
 * Optimize parameters for specific Falcon models
 */
function optimizeParametersForModel(modelName, params) {
    const { temperature, topP, maxTokens, modelType } = params;

    // Falcon-specific optimizations
    if (modelName.includes('falcon') || modelName.includes('Falcon')) {
        return {
            temperature: Math.min(temperature * 0.9, 0.8), // Slightly lower temperature for Falcon
            topP: Math.min(topP, 0.85), // More focused sampling
            maxTokens: Math.min(maxTokens, 3500) // Conservative token limit
        };
    }

    // DeepSeek optimizations
    if (modelName.includes('deepseek')) {
        return {
            temperature: temperature * 1.1, // Slightly higher for creativity
            topP: topP,
            maxTokens: maxTokens
        };
    }

    // Default parameters
    return { temperature, topP, maxTokens };
}

/**
 * Validate Falcon model response quality
 */
function validateFalconResponse(response, originalPrompt) {
    if (!response || response.length < 10) {
        return false;
    }

    // Check for common Falcon model issues
    if (response.includes('I cannot') || response.includes('I apologize')) {
        return false;
    }

    // Check for repetitive patterns (common in Falcon models)
    const sentences = response.split(/[.!?]+/);
    if (sentences.length > 3) {
        const uniqueSentences = new Set(sentences.map(s => s.trim().toLowerCase()));
        if (uniqueSentences.size / sentences.length < 0.7) {
            return false; // Too repetitive
        }
    }

    return true;
}

/**
 * Main humanization function using advanced LLMs
 */
export async function humanizeWithAdvancedLLM(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        preferredModel = 'deepseek-r1',
        maxRetries = 2
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string'
        };
    }

    const startTime = Date.now();
    
    // Analyze content type for better prompting
    const contentType = analyzeContentType(text);
    
    // Generate optimized prompt
    const prompt = generateHumanizationPrompt(text, {
        aggressiveness,
        maintainTone,
        targetDetection,
        contentType
    });

    // Enhanced model selection with Falcon prioritization
    const modelOrder = getOptimalModelOrder(preferredModel, targetDetection, aggressiveness);

    for (const modelName of modelOrder) {
        const modelConfig = MODEL_CONFIGS[modelName];
        if (!modelConfig) continue;

        // Sort providers by priority
        const sortedProviders = modelConfig.providers.sort((a, b) => (a.priority || 999) - (b.priority || 999));

        // Try each provider for this model
        for (const provider of sortedProviders) {
            // Check if API key is available
            if (!process.env[provider.apiKeyEnv]) {
                console.log(`Skipping ${provider.name}: API key not configured`);
                continue;
            }

            let retries = 0;
            while (retries <= maxRetries) {
                try {
                    const modelType = modelName.includes('falcon') ? 'falcon' : 'other';

                    const result = await callLLMAPI(provider, prompt, {
                        maxTokens: Math.min(4000, text.length * 2.5), // Increased for Falcon models
                        temperature: calculateOptimalTemperature(aggressiveness, modelType),
                        topP: modelType === 'falcon' ? 0.85 : 0.9,
                        modelType: modelType
                    });

                    const totalTime = Date.now() - startTime;

                    console.log(`Successfully humanized with ${provider.name}/${modelName} in ${totalTime}ms`);

                    // Validate result meets target detection requirements
                    if (await validateDetectionTarget(result.text, targetDetection)) {
                        return {
                            ...result,
                            originalText: text,
                            processingTime: totalTime,
                            method: 'llm-advanced-falcon',
                            modelName,
                            detectionTarget: targetDetection,
                            options: { aggressiveness, maintainTone, targetDetection }
                        };
                    } else {
                        console.warn(`${modelName} result may not meet ≤${targetDetection}% detection target, trying next model`);
                        break; // Try next model instead of retrying same model
                    }

                } catch (error) {
                    retries++;
                    console.warn(`${provider.name} attempt ${retries}/${maxRetries + 1} failed:`, error.message);

                    if (retries <= maxRetries) {
                        // Progressive backoff with longer delays for Falcon models
                        const delay = modelType === 'falcon' ? 2000 * retries : 1000 * retries;
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }
            }
        }
    }

    // If all LLM attempts failed
    const totalTime = Date.now() - startTime;
    return {
        success: false,
        error: 'All advanced LLM providers failed',
        originalText: text,
        processingTime: totalTime,
        method: 'failed',
        fallbackRecommended: true
    };
}

/**
 * Enhanced content type analysis for better Falcon model prompting
 */
function analyzeContentType(text) {
    const formalIndicators = /\b(therefore|furthermore|consequently|moreover|nevertheless|thus|hence)\b/gi;
    const technicalIndicators = /\b(API|algorithm|implementation|configuration|optimization|framework|architecture|deployment)\b/gi;
    const academicIndicators = /\b(research|study|analysis|methodology|findings|hypothesis|conclusion|evidence)\b/gi;
    const businessIndicators = /\b(strategy|revenue|market|customer|business|sales|profit|ROI)\b/gi;
    const creativeIndicators = /\b(story|narrative|creative|artistic|design|aesthetic|inspiration)\b/gi;

    if (academicIndicators.test(text)) return 'academic';
    if (technicalIndicators.test(text)) return 'technical';
    if (businessIndicators.test(text)) return 'business';
    if (creativeIndicators.test(text)) return 'creative';
    if (formalIndicators.test(text)) return 'formal';
    return 'general';
}

/**
 * Get optimal model order based on target detection and aggressiveness
 */
function getOptimalModelOrder(preferredModel, targetDetection, aggressiveness) {
    // For ≤10% AI detection target, prioritize Falcon models
    if (targetDetection <= 10) {
        if (aggressiveness >= 0.8) {
            // High aggressiveness: use most powerful models
            return ['falcon-3-10b', 'falcon-h1-7b', 'falcon-3-7b', 'falcon-180b', preferredModel, 'deepseek-r1', 'llama-3.1-8b'];
        } else if (aggressiveness >= 0.5) {
            // Medium aggressiveness: balanced approach
            return ['falcon-3-7b', 'falcon-h1-7b', 'falcon-3-10b', preferredModel, 'deepseek-r1', 'llama-3.1-8b'];
        } else {
            // Low aggressiveness: efficient models
            return ['falcon-h1-7b', 'falcon-3-7b', preferredModel, 'deepseek-r1', 'llama-3.1-8b'];
        }
    } else {
        // For higher detection targets, use existing model order
        return [preferredModel, 'falcon-3-7b', 'deepseek-r1', 'llama-3.1-8b', 'mistral-7b'];
    }
}

/**
 * Calculate optimal temperature based on aggressiveness and model type
 */
function calculateOptimalTemperature(aggressiveness, modelType) {
    const baseTemp = aggressiveness * 0.8 + 0.2;

    if (modelType === 'falcon') {
        // Falcon models work better with slightly lower temperature
        return Math.min(baseTemp * 0.9, 0.8);
    }

    return baseTemp;
}

/**
 * Validate if result meets detection target (placeholder - would integrate with actual detection API)
 */
async function validateDetectionTarget(text, targetDetection) {
    // Placeholder validation - in production, this would call an AI detection API
    // For now, we'll do basic heuristic checks

    // Check for common AI patterns that increase detection
    const aiPatterns = [
        /\b(furthermore|moreover|consequently|therefore|thus|hence)\b/gi,
        /\b(it is important to note|it should be noted|it is worth mentioning)\b/gi,
        /\b(in conclusion|to summarize|in summary)\b/gi,
        /\b(comprehensive|extensive|significant|substantial|considerable)\b/gi
    ];

    let patternCount = 0;
    aiPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) patternCount += matches.length;
    });

    // Simple heuristic: fewer AI patterns = lower detection likelihood
    const estimatedDetection = Math.min(patternCount * 5, 95); // Cap at 95%

    return estimatedDetection <= targetDetection;
}

/**
 * Check if advanced LLM service is available
 */
export function isAdvancedLLMAvailable() {
    // Check if at least one API key is configured
    const availableKeys = [
        'FIREWORKS_API_KEY',
        'NOVITA_API_KEY', 
        'OPENROUTER_API_KEY',
        'GROQ_API_KEY'
    ].filter(key => process.env[key]);
    
    return availableKeys.length > 0;
}

/**
 * Get status of available providers
 */
export function getProviderStatus() {
    const status = {};
    
    Object.entries(MODEL_CONFIGS).forEach(([modelName, config]) => {
        status[modelName] = config.providers.map(provider => ({
            name: provider.name,
            available: !!process.env[provider.apiKeyEnv],
            model: provider.model
        }));
    });
    
    return status;
}
