import React from 'react';
import Link from 'next/link';
import styles from '../../styles/InternalLinks.module.css';

const InternalLinks = ({ currentPage = "home" }) => {
  const linkStructure = {
    home: [
      {
        href: "/features",
        title: "Explore GhostLayer Features",
        description: "Discover advanced AI text humanization capabilities and bypass techniques",
        keywords: "AI text humanizer features, bypass AI detection tools"
      },
      {
        href: "/about",
        title: "About GhostLayer Technology",
        description: "Learn how our AI humanization algorithms work to make text undetectable",
        keywords: "AI humanization technology, how GhostLayer works"
      },
      {
        href: "/pricing",
        title: "GhostLayer Pricing Plans",
        description: "Choose the perfect plan for your AI text humanization needs",
        keywords: "AI text humanizer pricing, GhostLayer plans"
      }
    ],
    features: [
      {
        href: "/",
        title: "Try GhostLayer Free",
        description: "Start humanizing your AI text with our free online tool",
        keywords: "free AI text humanizer, try GhostLayer"
      },
      {
        href: "/about",
        title: "How GhostLayer Works",
        description: "Understanding the science behind AI detection bypass",
        keywords: "AI detection bypass technology"
      },
      {
        href: "/pricing",
        title: "Upgrade for More Features",
        description: "Unlock advanced humanization modes and higher word limits",
        keywords: "premium AI text humanizer"
      }
    ],
    about: [
      {
        href: "/",
        title: "Start Using GhostLayer",
        description: "Transform your AI text into undetectable human-like content",
        keywords: "AI text humanization tool"
      },
      {
        href: "/features",
        title: "GhostLayer Features",
        description: "Explore all the ways GhostLayer can help bypass AI detection",
        keywords: "AI detection bypass features"
      },
      {
        href: "/pricing",
        title: "Choose Your Plan",
        description: "Find the right GhostLayer subscription for your needs",
        keywords: "AI humanizer subscription plans"
      }
    ],
    pricing: [
      {
        href: "/",
        title: "Try Before You Buy",
        description: "Test GhostLayer's AI humanization with our free tier",
        keywords: "free AI text humanizer trial"
      },
      {
        href: "/features",
        title: "See All Features",
        description: "Compare features across different GhostLayer plans",
        keywords: "AI humanizer feature comparison"
      },
      {
        href: "/about",
        title: "Why Choose GhostLayer",
        description: "Learn why GhostLayer is the most effective AI text humanizer",
        keywords: "best AI text humanizer"
      }
    ]
  };

  const currentLinks = linkStructure[currentPage] || linkStructure.home;

  const relatedTopics = [
    {
      title: "AI Detection Tools",
      links: [
        { text: "GPTZero Bypass", anchor: "#gptzero-bypass" },
        { text: "Turnitin AI Detection", anchor: "#turnitin-bypass" },
        { text: "Originality.ai Bypass", anchor: "#originality-bypass" },
        { text: "Winston AI Detection", anchor: "#winston-bypass" }
      ]
    },
    {
      title: "Humanization Techniques",
      links: [
        { text: "Conservative Mode", anchor: "#conservative-humanization" },
        { text: "Balanced Processing", anchor: "#balanced-humanization" },
        { text: "Aggressive Bypass", anchor: "#aggressive-humanization" },
        { text: "Style Preservation", anchor: "#style-preservation" }
      ]
    },
    {
      title: "Use Cases",
      links: [
        { text: "Academic Writing", anchor: "#academic-use" },
        { text: "Content Creation", anchor: "#content-creation" },
        { text: "Business Communication", anchor: "#business-writing" },
        { text: "Creative Writing", anchor: "#creative-writing" }
      ]
    }
  ];

  return (
    <div className={styles.internalLinks}>
      {/* Main Navigation Links */}
      <section className={styles.mainLinks}>
        <h3 className={styles.sectionTitle}>Explore More</h3>
        <div className={styles.linkGrid}>
          {currentLinks.map((link, index) => (
            <Link key={index} href={link.href} className={styles.mainLink}>
              <div className={styles.linkContent}>
                <h4 className={styles.linkTitle}>{link.title}</h4>
                <p className={styles.linkDescription}>{link.description}</p>
                <div className={styles.linkKeywords}>{link.keywords}</div>
                <span className={styles.linkArrow}>→</span>
              </div>
            </Link>
          ))}
        </div>
      </section>

      {/* Related Topics for Internal Linking */}
      <section className={styles.relatedTopics}>
        <h3 className={styles.sectionTitle}>Related Topics</h3>
        <div className={styles.topicsGrid}>
          {relatedTopics.map((topic, topicIndex) => (
            <div key={topicIndex} className={styles.topicGroup}>
              <h4 className={styles.topicTitle}>{topic.title}</h4>
              <ul className={styles.topicLinks}>
                {topic.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <a 
                      href={link.anchor} 
                      className={styles.topicLink}
                      onClick={(e) => {
                        e.preventDefault();
                        const element = document.querySelector(link.anchor);
                        if (element) {
                          element.scrollIntoView({ behavior: 'smooth' });
                        }
                      }}
                    >
                      {link.text}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </section>

      {/* Breadcrumb Navigation */}
      <section className={styles.breadcrumbs}>
        <nav className={styles.breadcrumbNav}>
          <Link href="/" className={styles.breadcrumbLink}>
            Home
          </Link>
          <span className={styles.breadcrumbSeparator}>/</span>
          {currentPage !== "home" && (
            <>
              <span className={styles.breadcrumbCurrent}>
                {currentPage.charAt(0).toUpperCase() + currentPage.slice(1)}
              </span>
            </>
          )}
        </nav>
      </section>

      {/* Footer Links for SEO */}
      <section className={styles.footerLinks}>
        <div className={styles.footerGrid}>
          <div className={styles.footerColumn}>
            <h5 className={styles.footerTitle}>AI Text Humanization</h5>
            <ul className={styles.footerList}>
              <li><Link href="/#how-to-humanize" className={styles.footerLink}>How to Humanize AI Text</Link></li>
              <li><Link href="/#bypass-detection" className={styles.footerLink}>Bypass AI Detection</Link></li>
              <li><Link href="/#undetectable-ai" className={styles.footerLink}>Make AI Text Undetectable</Link></li>
              <li><Link href="/#ai-humanizer-free" className={styles.footerLink}>Free AI Humanizer</Link></li>
            </ul>
          </div>
          <div className={styles.footerColumn}>
            <h5 className={styles.footerTitle}>AI Detection Tools</h5>
            <ul className={styles.footerList}>
              <li><Link href="/#gptzero-bypass" className={styles.footerLink}>GPTZero Bypass</Link></li>
              <li><Link href="/#turnitin-ai" className={styles.footerLink}>Turnitin AI Detection</Link></li>
              <li><Link href="/#originality-ai" className={styles.footerLink}>Originality.ai Bypass</Link></li>
              <li><Link href="/#winston-ai" className={styles.footerLink}>Winston AI Bypass</Link></li>
            </ul>
          </div>
          <div className={styles.footerColumn}>
            <h5 className={styles.footerTitle}>Resources</h5>
            <ul className={styles.footerList}>
              <li><Link href="/features" className={styles.footerLink}>Features</Link></li>
              <li><Link href="/about" className={styles.footerLink}>About</Link></li>
              <li><Link href="/pricing" className={styles.footerLink}>Pricing</Link></li>
              <li><Link href="/#faq" className={styles.footerLink}>FAQ</Link></li>
            </ul>
          </div>
        </div>
      </section>
    </div>
  );
};

export default InternalLinks;
