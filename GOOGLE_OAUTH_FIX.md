# 🔐 **GOOGLE OAUTH COMPLETE SETUP GUIDE**

## ⚠️ **CRITICAL ISSUE**
**Error**: "Access blocked: Authorization Error - The OAuth client was not found"

**Root Cause**: Missing or incorrect Google OAuth configuration

---

## 🎯 **COMPLETE SOLUTION - 4 PHASES**

### **📋 PHASE 1: GOOGLE CLOUD CONSOLE SETUP**

#### **1.1 Create Google Cloud Project**
```bash
# 🌐 Visit: https://console.cloud.google.com/
# ➡️ Action: Create new project

Project Details:
  - Name: "GhostLayer-Production"
  - Project ID: ghostlayer-prod-[random]
  - Organization: (leave default)
```

#### **1.2 Enable Required APIs**
```bash
# 📍 Navigate: APIs & Services > Library
# ✅ Enable these APIs (CRITICAL):

1. Google+ API (Legacy but required for NextAuth)
2. Google Identity Services API
3. People API (recommended)
4. Gmail API (if email features needed)
```

#### **1.3 Configure OAuth Consent Screen**
```bash
# 📍 Navigate: APIs & Services > OAuth consent screen

User Type: ✅ External (unless Google Workspace account)

App Information:
  - App name: "GhostLayer AI Text Humanizer"
  - User support email: <EMAIL>
  - App logo: (upload 120x120px logo - optional)
  - App domain: http://localhost:3000 (dev) / https://yourdomain.com (prod)
  - Privacy Policy URL: https://yourdomain.com/privacy (required for production)
  - Terms of Service URL: https://yourdomain.com/terms (required for production)
  - Developer contact: <EMAIL>

Scopes (Click "Add or Remove Scopes"):
  ✅ email
  ✅ profile
  ✅ openid

Test Users (Add these emails for development):
  ✅ <EMAIL>
  ✅ <EMAIL> (any additional test emails)
```

#### **1.4 Create OAuth 2.0 Credentials**
```bash
# 📍 Navigate: APIs & Services > Credentials
# 🔧 Click: "Create Credentials" > "OAuth 2.0 Client IDs"

Application type: ✅ Web application
Name: "GhostLayer-WebClient-Development"

Authorized JavaScript origins:
  ✅ http://localhost:3000
  ✅ http://localhost:3001
  ✅ https://yourdomain.com (for production)

Authorized redirect URIs (EXACT MATCH REQUIRED):
  ✅ http://localhost:3000/api/auth/callback/google
  ✅ http://localhost:3001/api/auth/callback/google
  ✅ https://yourdomain.com/api/auth/callback/google (for production)

# 🔑 CRITICAL: Copy and save these immediately:
Client ID: [COPY THIS - starts with numbers ending in .apps.googleusercontent.com]
Client Secret: [COPY THIS - random string]
```

---

### **⚙️ PHASE 2: ENVIRONMENT CONFIGURATION**

#### **2.1 Update .env.local File**
```env
# 🔐 AUTHENTICATION (Replace with your actual values)
NEXTAUTH_SECRET=your_generated_32_char_secret_here_replace_this
NEXTAUTH_URL=http://localhost:3000

# 🔑 GOOGLE OAUTH (From Phase 1 - Step 1.4)
GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your_actual_client_secret_here

# 🗄️ DATABASE (Development)
DATABASE_URL="file:./prisma/dev.db"

# 🌐 APP CONFIGURATION
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="GhostLayer"
NODE_ENV=development
```

#### **2.2 Generate Secure NEXTAUTH_SECRET**
```bash
# 🔧 Method 1: OpenSSL (Recommended)
openssl rand -base64 32

# 🔧 Method 2: Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"

# 🔧 Method 3: Online Generator
# Visit: https://generate-secret.vercel.app/32

# 📝 Example output: "Kp2s8v+e1HI7W3zT9mN4xR7qL5jF8aB2cD6gH1kM3nP="
# ⚠️ Use YOUR generated secret, not this example!
```

---

### **🗄️ PHASE 3: DATABASE INITIALIZATION**

#### **3.1 Initialize Prisma Database**
```bash
# 🔧 Generate Prisma Client
npx prisma generate

# 🔧 Create Database Tables
npx prisma db push

# ✅ Verify Database (Optional)
npx prisma studio
# Opens web interface at http://localhost:5555
```

#### **3.2 Verify Database Schema**
```bash
# 📊 Check if tables were created:
# - User
# - Account
# - Session
# - VerificationToken
# - Subscription
```

---

### **🧪 PHASE 4: TESTING & VERIFICATION**

#### **4.1 Start Development Server**
```bash
# 🚀 Start the application
npm run dev

# ✅ Should see:
# - Ready - started server on 0.0.0.0:3000
# - No compilation errors
# - Database connected successfully
```

#### **4.2 Test OAuth Flow**
```bash
# 🌐 Open browser: http://localhost:3000
# 🔘 Click: "Sign in with Google" button
# ➡️ Should redirect to: accounts.google.com/oauth/authorize
# ✅ After authorization: redirect back to your app
# 👤 User should be logged in with session created
```

## Common Issues & Solutions

### Issue: "redirect_uri_mismatch"
**Solution**: Ensure the redirect URI in Google Console exactly matches:
```
http://localhost:3001/api/auth/callback/google
```

### Issue: "invalid_client"
**Solution**: Double-check CLIENT_ID and CLIENT_SECRET in `.env.local`

### Issue: "access_denied"
**Solution**: Make sure OAuth consent screen is properly configured

### Issue: Database errors
**Solution**: Run `npx prisma db push` to create tables

## Verification Checklist

- [ ] Google Cloud project created
- [ ] Google+ API enabled
- [ ] OAuth consent screen configured
- [ ] OAuth 2.0 credentials created with correct redirect URI
- [ ] `.env.local` file created with all required variables
- [ ] NEXTAUTH_SECRET generated (32+ characters)
- [ ] Database initialized with Prisma
- [ ] Development server restarted
- [ ] OAuth flow tested successfully

## Production Deployment Notes

For production, update:
1. **Redirect URI**: `https://yourdomain.com/api/auth/callback/google`
2. **NEXTAUTH_URL**: `https://yourdomain.com`
3. **Database**: Use PostgreSQL instead of SQLite
4. **OAuth Consent**: Submit for verification if needed
