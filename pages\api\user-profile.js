// src/pages/api/user-profile.js
import { getServerSession } from "next-auth/next";
import { authOptions } from "./auth/[...nextauth]"; // Adjust path if your authOptions is elsewhere
import prisma from '../../src/lib/prisma'; // Import shared Prisma instance

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    const session = await getServerSession(req, res, authOptions);

    if (!session || !session.user || !session.user.id) {
        return res.status(401).json({ error: "Unauthorized", message: "You must be signed in to access this resource." });
    }

    try {
        const userProfile = await prisma.user.findUnique({
            where: {
                id: session.user.id, // session.user.id should be the database ID from the User model
            },
            // Select specific fields to return to avoid exposing sensitive data accidentally
            // and to be explicit about what the client needs.
            select: {
                id: true,
                name: true,
                email: true,
                image: true,
                subscriptionTier: true,
                usageCredits: true,
                createdAt: true,
                updatedAt: true
                // Exclude fields like stripeCustomerId unless specifically needed by the client here
            }
        });

        if (!userProfile) {
            // This case should ideally not happen if a session exists with a valid user.id
            // that was populated from the database during session callback.
            console.error(`User not found in DB with ID: ${session.user.id}`);
            return res.status(404).json({ error: "User profile not found in database." });
        }

        // Return the selected user profile fields
        res.status(200).json(userProfile);

    } catch (error) {
        console.error("Error fetching user profile from database:", error);
        res.status(500).json({ error: "Internal server error", message: "Could not fetch user profile." });
    } finally {
        // In a serverless environment, Prisma client doesn't need to be explicitly disconnected usually.
        // However, if you were running in a long-running server, you might consider:
        // await prisma.$disconnect();
        // But for Next.js API routes, this is generally not necessary and handled by Prisma's connection management.
    }
}
