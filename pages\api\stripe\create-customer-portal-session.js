// src/pages/api/stripe/create-customer-portal-session.js
import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth]"; // Adjust path relative to `pages/api/stripe`
import stripe from "../../../src/lib/stripe";       // Your initialized Stripe instance
import prisma from "../../../src/lib/prisma";       // Your initialized Prisma instance

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', 'POST');
        return res.status(405).end('Method Not Allowed');
    }

    try {
        const session = await getServerSession(req, res, authOptions);
        if (!session || !session.user || !session.user.id) {
            return res.status(401).json({ error: "Unauthorized", message: "You must be signed in to manage your subscription." });
        }

        const userId = session.user.id;

        // Fetch only the stripeCustomerId for the user
        const appUser = await prisma.user.findUnique({
            where: { id: userId },
            select: { stripeCustomerId: true },
        });

        if (!appUser) {
            // This should ideally not happen if session.user.id is valid and from our database
            console.error(`User with ID ${userId} found in session but not in database for portal session.`);
            return res.status(404).json({ error: "User not found", message: "Authenticated user not found in our database." });
        }

        if (!appUser.stripeCustomerId) {
            console.log(`User ${userId} does not have a Stripe Customer ID. Cannot create portal session.`);
            return res.status(400).json({
                error: "No Subscription Found",
                message: "No subscription information found for this user. If you believe this is an error, please contact support."
                // It's possible a user exists but has never initiated any Stripe interaction.
            });
        }

        const stripeCustomerId = appUser.stripeCustomerId;

        // Determine the base URL for constructing the return_url
        // Ensure NEXTAUTH_URL is set correctly in your environment variables
        const appBaseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';

        // Define the return URL where users will be sent after managing their subscription in the portal
        // This should be a page in your application, e.g., user profile or settings page.
        const returnUrl = `${appBaseUrl}/profile`; // Example: redirect to /profile
                                                 // You might want to make this more specific, e.g., /settings/billing

        console.log(`Creating Stripe Customer Portal session for customer ID: ${stripeCustomerId} with return_url: ${returnUrl}`);

        // Create a Stripe Billing Portal Session
        // For more details: https://stripe.com/docs/api/customer_portal/sessions/create
        const portalSession = await stripe.billingPortal.sessions.create({
            customer: stripeCustomerId,
            return_url: returnUrl,
            // You can also configure what parts of the portal are accessible:
            // configuration: 'YOUR_STRIPE_PORTAL_CONFIGURATION_ID' // Optional: if you have specific portal configurations
        });

        // Return the URL of the portal session to the client.
        // The client will then redirect the user to this URL.
        res.status(200).json({ url: portalSession.url });

    } catch (error) {
        console.error("Error creating Stripe Customer Portal session:", error.message, error);
        res.status(500).json({
            error: "Internal Server Error",
            message: error.message || "Could not create Stripe Customer Portal session due to an unexpected error."
        });
    }
}
