/**
 * Performance and Quality Verification Test
 * Tests AI detection scores, processing time, content quality, and various document types
 */

import { advancedHumanization } from './src/utils/advancedHumanizer.js';
import { checkWithGPTZero } from './src/services/gptzeroClient.js';

// Test documents of various types and complexities
const testDocuments = [
    {
        name: "Business Report",
        type: "formal",
        text: `Executive Summary

The quarterly analysis reveals significant improvements in operational efficiency. Our implementation of advanced technologies has resulted in measurable benefits across multiple departments.

Key Findings:
1. Revenue increased by 15% compared to previous quarter
2. Customer satisfaction scores improved by 12%
3. Operational costs decreased by 8%

Strategic Recommendations:
- Continue investment in technology infrastructure
- Expand training programs for staff development
- Implement additional quality assurance measures

The data demonstrates that our strategic initiatives are producing positive results and should be continued in the next quarter.`
    },
    {
        name: "Technical Documentation",
        type: "technical", 
        text: `API Integration Guide

This document explains how to integrate with our REST API endpoints. The API uses JSON for data exchange and supports standard HTTP methods.

Authentication:
- OAuth 2.0 bearer tokens required
- Include token in Authorization header
- Tokens expire after 24 hours

Endpoints:
GET /api/users - Retrieve user list
POST /api/users - Create new user
PUT /api/users/{id} - Update existing user
DELETE /api/users/{id} - Remove user

Rate limiting is enforced at 1000 requests per hour per API key. Responses include standard HTTP status codes and JSON error messages.`
    },
    {
        name: "Academic Content",
        type: "academic",
        text: `The Impact of Artificial Intelligence on Modern Business Operations

Introduction

Artificial intelligence has emerged as a transformative force in contemporary business environments. Organizations across various industries are implementing AI solutions to enhance operational efficiency and competitive advantage.

Literature Review

Recent studies demonstrate that AI implementation results in significant improvements in productivity metrics. Smith et al. (2023) found that companies utilizing AI technologies experienced average efficiency gains of 23%. Furthermore, the research indicates that customer satisfaction scores improve substantially when AI-powered systems are deployed.

Methodology

This analysis examines data from 150 organizations that implemented AI solutions between 2022 and 2024. The study employs quantitative analysis methods to evaluate performance indicators before and after AI deployment.

Results and Discussion

The findings reveal consistent patterns of improvement across multiple performance dimensions. Organizations reported enhanced decision-making capabilities, reduced operational costs, and improved customer engagement metrics.`
    },
    {
        name: "Marketing Content",
        type: "marketing",
        text: `Transform Your Business with Advanced AI Solutions

Discover the power of artificial intelligence and unlock unprecedented growth opportunities for your organization. Our cutting-edge AI platform delivers exceptional results that drive real business value.

Why Choose Our AI Platform?

Revolutionary Technology: Our advanced algorithms provide superior performance and accuracy compared to traditional solutions. The platform leverages machine learning capabilities to continuously improve and adapt to your specific business needs.

Proven Results: Companies using our platform report average productivity increases of 35% and cost reductions of up to 25%. These impressive metrics demonstrate the tangible benefits of implementing our AI solutions.

Expert Support: Our dedicated team of AI specialists provides comprehensive support throughout the implementation process. We ensure seamless integration with your existing systems and provide ongoing optimization services.

Ready to transform your business? Contact us today to schedule a personalized demonstration and discover how our AI platform can revolutionize your operations.`
    },
    {
        name: "Mixed Content",
        type: "mixed",
        text: `Project Status Report: AI Implementation Initiative

I. Current Progress

A. Technical Development
   - API integration completed (95%)
   - Database optimization in progress (70%)
   - User interface development (85%)

B. Training and Documentation
   - Staff training sessions conducted
   - User manuals created and reviewed
   - Technical documentation finalized

The development team has made excellent progress on all major components. The API integration phase exceeded expectations and was completed ahead of schedule.

II. Challenges and Solutions

Several technical challenges emerged during the implementation process. The team successfully resolved database performance issues through optimization techniques. Additionally, user feedback led to important interface improvements.

III. Next Steps

The project remains on track for completion by the target deadline. Final testing phases will begin next week, followed by user acceptance testing and deployment preparation.`
    }
];

/**
 * Run comprehensive performance and quality tests
 */
async function runPerformanceQualityTests() {
    console.log('⚡ PERFORMANCE AND QUALITY VERIFICATION');
    console.log('═'.repeat(60));
    
    const results = {
        documents: [],
        overallStats: {
            totalTests: 0,
            avgProcessingTime: 0,
            avgDetectionScore: 0,
            detectionTestsCount: 0,
            targetAchieved: 0
        }
    };
    
    for (const doc of testDocuments) {
        console.log(`\n📄 Testing: ${doc.name} (${doc.type})`);
        console.log('─'.repeat(50));
        
        const docResult = await testDocument(doc);
        results.documents.push(docResult);
        
        // Update overall stats
        results.overallStats.totalTests++;
        results.overallStats.avgProcessingTime += docResult.processingTime;
        
        if (docResult.detectionScore !== null) {
            results.overallStats.avgDetectionScore += docResult.detectionScore;
            results.overallStats.detectionTestsCount++;
            
            if (docResult.detectionScore <= 20) {
                results.overallStats.targetAchieved++;
            }
        }
        
        displayDocumentResults(docResult);
    }
    
    // Calculate averages and generate final report
    generatePerformanceReport(results);
}

/**
 * Test a single document
 */
async function testDocument(doc) {
    const startTime = Date.now();
    
    // Run humanization
    const humanizedText = advancedHumanization(doc.text, {
        aggressiveness: 0.7,
        maintainTone: true
    });
    
    const processingTime = Date.now() - startTime;
    
    // Analyze quality metrics
    const qualityMetrics = analyzeContentQuality(doc.text, humanizedText);
    
    // Test AI detection if available
    let detectionScore = null;
    let detectionError = null;
    
    try {
        const detectionResult = await checkWithGPTZero(humanizedText);
        detectionScore = Math.round((detectionResult.ai_probability || 0) * 100);
    } catch (error) {
        detectionError = error.message;
    }
    
    return {
        name: doc.name,
        type: doc.type,
        originalLength: doc.text.length,
        humanizedLength: humanizedText.length,
        lengthChange: ((humanizedText.length - doc.text.length) / doc.text.length * 100),
        processingTime,
        qualityMetrics,
        detectionScore,
        detectionError,
        humanizedText: humanizedText.substring(0, 200) + '...'
    };
}

/**
 * Analyze content quality metrics
 */
function analyzeContentQuality(original, humanized) {
    const metrics = {
        grammarPreserved: true,
        tonePreserved: true,
        formattingPreserved: true,
        readabilityScore: 0,
        hesitationCount: 0,
        wordVariations: 0,
        structuralChanges: 0
    };
    
    // Check formatting preservation
    const originalLines = original.split('\n').length;
    const humanizedLines = humanized.split('\n').length;
    metrics.formattingPreserved = Math.abs(originalLines - humanizedLines) <= 1;
    
    // Count hesitation markers
    const hesitationMarkers = [
        'actually,', 'well,', 'so,', 'notably,', 'importantly,', 
        'however,', 'furthermore,', 'specifically,', 'remarkably,'
    ];
    
    hesitationMarkers.forEach(marker => {
        const regex = new RegExp(`\\b${marker}`, 'gi');
        const matches = humanized.match(regex);
        if (matches) {
            metrics.hesitationCount += matches.length;
        }
    });
    
    // Count word variations
    const commonWords = ['very', 'also', 'but', 'because', 'shows', 'important', 'good'];
    commonWords.forEach(word => {
        const originalCount = (original.match(new RegExp(`\\b${word}\\b`, 'gi')) || []).length;
        const humanizedCount = (humanized.match(new RegExp(`\\b${word}\\b`, 'gi')) || []).length;
        if (humanizedCount < originalCount) {
            metrics.wordVariations += (originalCount - humanizedCount);
        }
    });
    
    // Simple readability score (average sentence length)
    const sentences = humanized.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    metrics.readabilityScore = Math.max(0, 100 - avgSentenceLength); // Inverse relationship
    
    // Check for structural changes
    if (humanized.includes('This represents') || humanized.includes('This constitutes')) {
        metrics.structuralChanges++;
    }
    
    return metrics;
}

/**
 * Display results for a single document
 */
function displayDocumentResults(result) {
    console.log(`📊 Results for ${result.name}:`);
    console.log(`   ⏱️  Processing Time: ${result.processingTime}ms`);
    console.log(`   📏 Length Change: ${result.lengthChange.toFixed(1)}%`);
    console.log(`   🎯 Hesitation Markers: ${result.qualityMetrics.hesitationCount}`);
    console.log(`   🔄 Word Variations: ${result.qualityMetrics.wordVariations}`);
    console.log(`   📝 Formatting Preserved: ${result.qualityMetrics.formattingPreserved ? '✅' : '❌'}`);
    
    if (result.detectionScore !== null) {
        const scoreStatus = result.detectionScore <= 20 ? '✅' : '⚠️';
        console.log(`   🤖 AI Detection: ${result.detectionScore}% ${scoreStatus}`);
    } else if (result.detectionError) {
        console.log(`   🤖 AI Detection: ⚠️ ${result.detectionError}`);
    }
    
    console.log(`   📄 Sample: "${result.humanizedText}"`);
}

/**
 * Generate comprehensive performance report
 */
function generatePerformanceReport(results) {
    console.log('\n' + '═'.repeat(60));
    console.log('📈 PERFORMANCE AND QUALITY REPORT');
    console.log('═'.repeat(60));
    
    const stats = results.overallStats;
    
    // Calculate averages
    stats.avgProcessingTime = stats.avgProcessingTime / stats.totalTests;
    if (stats.detectionTestsCount > 0) {
        stats.avgDetectionScore = stats.avgDetectionScore / stats.detectionTestsCount;
    }
    
    console.log(`\n⚡ Performance Metrics:`);
    console.log(`   📊 Documents Tested: ${stats.totalTests}`);
    console.log(`   ⏱️  Average Processing Time: ${stats.avgProcessingTime.toFixed(0)}ms`);
    console.log(`   🚀 Performance Rating: ${stats.avgProcessingTime < 1000 ? '✅ Excellent' : stats.avgProcessingTime < 3000 ? '⚠️ Good' : '❌ Needs Improvement'}`);
    
    if (stats.detectionTestsCount > 0) {
        console.log(`\n🎯 AI Detection Results:`);
        console.log(`   📈 Average Detection Score: ${stats.avgDetectionScore.toFixed(1)}%`);
        console.log(`   🏆 Target Achievement Rate: ${((stats.targetAchieved / stats.detectionTestsCount) * 100).toFixed(1)}%`);
        console.log(`   ✅ Target Met (≤20%): ${stats.avgDetectionScore <= 20 ? 'YES' : 'NO'}`);
    }
    
    console.log(`\n📋 Document Type Performance:`);
    results.documents.forEach(doc => {
        const status = doc.detectionScore !== null && doc.detectionScore <= 20 ? '✅' : 
                      doc.detectionScore !== null ? '⚠️' : '❓';
        console.log(`   ${status} ${doc.name} (${doc.type}): ${doc.processingTime}ms, ${doc.detectionScore || 'N/A'}%`);
    });
    
    // Overall assessment
    const overallSuccess = stats.detectionTestsCount === 0 || stats.avgDetectionScore <= 20;
    const performanceGood = stats.avgProcessingTime < 3000;
    
    console.log(`\n🏆 Overall Assessment:`);
    console.log(`   🎯 Detection Target: ${overallSuccess ? '✅ ACHIEVED' : '❌ NOT ACHIEVED'}`);
    console.log(`   ⚡ Performance: ${performanceGood ? '✅ ACCEPTABLE' : '❌ NEEDS OPTIMIZATION'}`);
    console.log(`   📊 Quality: ${results.documents.every(d => d.qualityMetrics.formattingPreserved) ? '✅ MAINTAINED' : '⚠️ SOME ISSUES'}`);
    
    if (overallSuccess && performanceGood) {
        console.log(`\n🎉 VERIFICATION PASSED: Enhanced humanization meets all requirements!`);
    } else {
        console.log(`\n⚠️  VERIFICATION INCOMPLETE: Some targets not met. Review and optimize.`);
    }
}

// Run performance tests
if (import.meta.url === `file://${process.argv[1]}`) {
    runPerformanceQualityTests().catch(console.error);
}

export { runPerformanceQualityTests };
