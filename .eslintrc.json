{"extends": ["next/core-web-vitals", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react", "react-hooks"], "rules": {"@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-empty-function": "warn", "react/react-in-jsx-scope": "off", "react/prop-types": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "prefer-const": "error", "no-var": "error", "no-console": "warn", "no-debugger": "error", "import/no-anonymous-default-export": "warn"}, "env": {"browser": true, "node": true, "es6": true}, "settings": {"react": {"version": "detect"}}}