/**
 * Advanced AI Detection Bypass Utilities
 * Specifically designed to reduce AI detection scores
 */

/**
 * Analyzes text for AI detection patterns and applies targeted fixes
 */
export function bypassAIDetection(text) {
    let result = text;

    // Step 1: Break up AI-typical sentence patterns
    result = breakAIPatterns(result);
    
    // Step 2: Add human writing inconsistencies
    result = addHumanInconsistencies(result);
    
    // Step 3: Inject natural human errors and corrections
    result = addNaturalErrors(result);
    
    // Step 4: Vary writing style within the text
    result = varyWritingStyle(result);
    
    // Step 5: Add emotional and personal elements
    result = addPersonalTouch(result);

    return result;
}

/**
 * Breaks up AI-typical sentence patterns that detectors recognize
 */
function breakAIPatterns(text) {
    let result = text;

    // AI often uses consistent sentence lengths - vary them dramatically
    const sentences = result.split(/(?<=[.!?])\s+/);
    const variedSentences = [];

    for (let i = 0; i < sentences.length; i++) {
        let sentence = sentences[i];
        
        // Break very long sentences (AI tendency)
        if (sentence.length > 100 && sentence.includes(',')) {
            const parts = sentence.split(',');
            if (parts.length >= 2) {
                const firstPart = parts[0].trim() + '.';
                const secondPart = parts.slice(1).join(',').trim();
                variedSentences.push(firstPart);
                sentence = secondPart.charAt(0).toUpperCase() + secondPart.slice(1);
            }
        }
        
        // Combine very short sentences (human tendency)
        if (sentence.length < 15 && i < sentences.length - 1 && sentences[i + 1].length < 20) {
            const nextSentence = sentences[i + 1];
            sentence = sentence.replace(/\.$/, '') + ', and ' + nextSentence.charAt(0).toLowerCase() + nextSentence.slice(1);
            i++; // Skip next sentence as we combined it
        }
        
        variedSentences.push(sentence);
    }

    return variedSentences.join(' ');
}

/**
 * Adds human writing inconsistencies that AI rarely produces
 */
function addHumanInconsistencies(text) {
    let result = text;

    // Add inconsistent punctuation spacing (humans do this)
    if (Math.random() > 0.8) {
        result = result.replace(/\s+,/g, ','); // Remove space before comma sometimes
        result = result.replace(/,([^\s])/g, ', $1'); // Add space after comma
    }

    // Inconsistent capitalization after colons
    result = result.replace(/:\s*([a-z])/g, (match, letter) => {
        if (Math.random() > 0.6) {
            return ': ' + letter.toUpperCase();
        }
        return match;
    });

    // Add occasional double spaces (human typing error)
    if (Math.random() > 0.9) {
        const sentences = result.split('. ');
        const randomIndex = Math.floor(Math.random() * sentences.length);
        if (randomIndex < sentences.length - 1) {
            sentences[randomIndex] += '.  '; // Double space
            result = sentences.join('');
        }
    }

    return result;
}

/**
 * Adds natural human errors and self-corrections
 */
function addNaturalErrors(text) {
    let result = text;

    // Add self-corrections (very human)
    const sentences = result.split(/(?<=[.!?])\s+/);
    const correctedSentences = sentences.map((sentence, index) => {
        if (Math.random() > 0.92 && sentence.length > 30) {
            const corrections = [
                ' - well, actually, ',
                ' (or rather, ',
                ' - I mean, ',
                ' - sorry, let me rephrase that - ',
                ' (if that makes sense) '
            ];
            const correction = corrections[Math.floor(Math.random() * corrections.length)];
            const insertPoint = Math.floor(sentence.length * 0.6);
            return sentence.slice(0, insertPoint) + correction + sentence.slice(insertPoint);
        }
        return sentence;
    });

    result = correctedSentences.join(' ');

    // Add occasional redundant phrases (humans repeat themselves)
    if (Math.random() > 0.85) {
        const redundancies = [
            ', as I mentioned',
            ', like I said',
            ', again',
            ', once more',
            ', to repeat'
        ];
        const redundancy = redundancies[Math.floor(Math.random() * redundancies.length)];
        result = result.replace(/\. ([A-Z])/g, (match, letter) => {
            if (Math.random() > 0.9) {
                return redundancy + '. ' + letter;
            }
            return match;
        });
    }

    return result;
}

/**
 * Varies writing style within the text (humans aren't consistent)
 */
function varyWritingStyle(text) {
    let result = text;
    const sentences = result.split(/(?<=[.!?])\s+/);
    
    const styledSentences = sentences.map((sentence, index) => {
        // Randomly make some sentences more formal, others more casual
        if (Math.random() > 0.8) {
            // Make casual
            sentence = sentence.replace(/\bhowever\b/gi, 'but');
            sentence = sentence.replace(/\btherefore\b/gi, 'so');
            sentence = sentence.replace(/\bmoreover\b/gi, 'plus');
            sentence = sentence.replace(/\bfurthermore\b/gi, 'and');
        } else if (Math.random() > 0.9) {
            // Make more formal
            sentence = sentence.replace(/\bbut\b/gi, 'however');
            sentence = sentence.replace(/\bso\b/gi, 'therefore');
            sentence = sentence.replace(/\bplus\b/gi, 'moreover');
        }

        // Add occasional questions (humans question themselves)
        if (Math.random() > 0.95 && !sentence.includes('?')) {
            const questions = [
                'Right?',
                'You know what I mean?',
                'Does that make sense?',
                'Am I making sense here?',
                'Follow me?'
            ];
            const question = questions[Math.floor(Math.random() * questions.length)];
            sentence = sentence.replace(/\.$/, '. ' + question);
        }

        return sentence;
    });

    return styledSentences.join(' ');
}

/**
 * Adds emotional and personal elements that AI rarely includes
 */
function addPersonalTouch(text) {
    let result = text;

    // Add personal experiences and opinions
    const personalPhrases = [
        'From my experience, ',
        'I\'ve found that ',
        'In my view, ',
        'Personally, I think ',
        'Based on what I\'ve seen, ',
        'To be honest, ',
        'If you ask me, ',
        'The way I see it, '
    ];

    const sentences = result.split(/(?<=[.!?])\s+/);
    const personalizedSentences = sentences.map((sentence, index) => {
        if (Math.random() > 0.88 && sentence.length > 25 && index > 0) {
            const phrase = personalPhrases[Math.floor(Math.random() * personalPhrases.length)];
            return phrase + sentence.charAt(0).toLowerCase() + sentence.slice(1);
        }
        return sentence;
    });

    result = personalizedSentences.join(' ');

    // Add emotional reactions
    const emotions = [
        ' (which is pretty cool)',
        ' (that\'s interesting)',
        ' (surprisingly)',
        ' (honestly)',
        ' (thankfully)',
        ' (unfortunately)',
        ' (luckily)'
    ];

    result = result.replace(/\. ([A-Z])/g, (match, letter) => {
        if (Math.random() > 0.92) {
            const emotion = emotions[Math.floor(Math.random() * emotions.length)];
            return emotion + '. ' + letter;
        }
        return match;
    });

    // Add uncertainty markers (humans are often uncertain)
    const uncertainties = [
        'I think ',
        'It seems like ',
        'Probably ',
        'Maybe ',
        'Perhaps ',
        'Likely ',
        'Possibly '
    ];

    result = result.replace(/\b(This|That|It)\s+(is|was|will be|would be)/gi, (match) => {
        if (Math.random() > 0.85) {
            const uncertainty = uncertainties[Math.floor(Math.random() * uncertainties.length)];
            return uncertainty + match.toLowerCase();
        }
        return match;
    });

    return result;
}

/**
 * Specific patterns that trigger AI detection - remove or modify them
 */
export function removeAITriggers(text) {
    let result = text;

    // Remove AI-typical opening phrases
    const aiOpenings = [
        /^In conclusion,?\s*/i,
        /^To summarize,?\s*/i,
        /^In summary,?\s*/i,
        /^Overall,?\s*/i,
        /^It is important to note that\s*/i,
        /^It should be noted that\s*/i,
        /^It is worth mentioning that\s*/i
    ];

    aiOpenings.forEach(pattern => {
        result = result.replace(pattern, '');
    });

    // Replace AI-typical transition phrases
    const aiTransitions = {
        'It is important to note that': 'Worth mentioning -',
        'It should be noted that': 'Keep in mind that',
        'It is worth mentioning that': 'By the way,',
        'Furthermore, it is essential to': 'Also, you need to',
        'Additionally, it is crucial to': 'Plus, it\'s important to',
        'Moreover, one must consider': 'Also, think about'
    };

    Object.entries(aiTransitions).forEach(([ai, human]) => {
        const regex = new RegExp(ai, 'gi');
        result = result.replace(regex, human);
    });

    return result;
}
