.comparisonSection {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: #1e293b;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.2rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.tableContainer {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 4rem;
}

.comparisonTable {
  width: 100%;
  border-collapse: collapse;
}

.featureHeader {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  padding: 1.5rem 1rem;
  text-align: left;
  font-weight: 700;
  font-size: 1rem;
  width: 25%;
}

.competitorHeader {
  background: #f1f5f9;
  padding: 1.5rem 1rem;
  text-align: center;
  font-weight: 600;
  border-left: 1px solid #e2e8f0;
}

.competitorHeader.ourColumn {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  position: relative;
}

.competitorInfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.logo {
  font-size: 1.5rem;
}

.name {
  font-weight: 700;
  font-size: 1rem;
}

.badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.featureRow:nth-child(even) {
  background: #f8fafc;
}

.featureCell {
  padding: 1rem;
  font-weight: 600;
  color: #1e293b;
  border-right: 1px solid #e2e8f0;
  background: #f1f5f9;
}

.valueCell {
  padding: 1rem;
  text-align: center;
  color: #475569;
  border-left: 1px solid #e2e8f0;
  position: relative;
}

.valueCell.ourValue {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  color: #6366f1;
  font-weight: 600;
}

.highlight {
  display: block;
  font-size: 0.75rem;
  color: #f59e0b;
  font-weight: 700;
  margin-top: 0.25rem;
}

.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.stat {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.stat:hover {
  transform: translateY(-5px);
}

.statNumber {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.statNote {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.4;
}

.cta {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  padding: 3rem 2rem;
  border-radius: 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>') repeat;
}

.ctaTitle {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
}

.ctaText {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

.ctaButton {
  background: white;
  color: #6366f1;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: #f8fafc;
}

@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }
  
  .tableContainer {
    overflow-x: auto;
  }
  
  .comparisonTable {
    min-width: 600px;
  }
  
  .featureHeader,
  .competitorHeader,
  .featureCell,
  .valueCell {
    padding: 0.75rem 0.5rem;
    font-size: 0.9rem;
  }
  
  .stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .stat {
    padding: 1.5rem;
  }
  
  .statNumber {
    font-size: 2rem;
  }
  
  .cta {
    padding: 2rem 1rem;
  }
  
  .ctaTitle {
    font-size: 1.5rem;
  }
}
