import React from 'react';

class ViralErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Viral component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // Return a minimal fallback that doesn't break the page
      return (
        <div style={{ 
          padding: '10px', 
          textAlign: 'center',
          color: '#666',
          fontSize: '14px'
        }}>
          {/* Silently fail for viral components - they're not critical */}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ViralErrorBoundary;
