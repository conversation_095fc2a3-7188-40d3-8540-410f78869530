# Outline: Privacy Policy

**IMPORTANT: This is a general outline and NOT legal advice. You MUST consult with a legal professional to draft your final Privacy Policy. This document is for informational and planning purposes only and needs to be tailored to your specific data practices and applicable legal requirements (e.g., GDPR, CCPA, CalOPPA, etc.) based on your users' locations and your data processing activities.**

## 1. Introduction
*   **Purpose:** Explain the purpose of the Privacy Policy (to inform users about how their personal data is collected, used, shared, and protected).
*   **Our Commitment:** State your commitment to protecting user privacy and handling data responsibly.
*   **Scope:** Define what services this policy applies to (e.g., [Your Application Name] website, application, and related services).
*   **Data Controller:** Identify the legal entity responsible for the data processing (e.g., "[Your Company Name/Your Name if Sole Proprietor], located at [Your Address/Region]").
*   **Effective Date:** Date the policy was last updated.
*   **Contact for Privacy Concerns:** Provide an email address or contact method for privacy-related inquiries.

## 2. Information We Collect

Clearly list the types of personal information you collect. Be specific.

*   **A. Information You Provide Directly:**
    *   **Account Registration Information:**
        *   When users sign up (e.g., via Google Sign-In through NextAuth.js): Name, email address, profile picture (as provided by the OAuth provider).
        *   If direct sign-up were implemented: Username, password (hashed), email.
    *   **Payment Information (for Premium Users):**
        *   Clarify that payments are processed by a third-party payment processor (e.g., Stripe).
        *   State that you **do not directly collect or store full credit card numbers.**
        *   Specify what payment-related information you might have access to or store from the payment processor (e.g., Stripe Customer ID, subscription status, last 4 digits of card, card expiry date, billing address if provided to Stripe). Refer to Stripe's privacy policy.
    *   **Input Text:** The text content that users submit to the Service for processing (e.g., paraphrasing, humanization).
    *   **Communications with Us:** Any information users provide when they contact customer support, send emails, or fill out forms on the site (e.g., feedback, inquiries).
*   **B. Information Collected Automatically (Usage Data & Technical Data):**
    *   **Log Data (Server Logs):**
        *   Standard web server logs: IP address (consider anonymization or justification for collection), browser type and version, operating system, referring/exit pages, date/time stamps of access, pages visited on your site.
    *   **Cookies and Similar Tracking Technologies:**
        *   Explain what cookies are and why you use them.
        *   **Essential Cookies:** For core functionality like session management (e.g., NextAuth.js session cookies), security, and user preferences.
        *   **Analytics Cookies (If used):** If you use analytics services (e.g., Google Analytics, Vercel Analytics, Plausible), state this. Explain what data is collected (e.g., aggregated usage patterns, page views, user demographics – if anonymized).
        *   **Advertising Cookies (If used):** If you integrate third-party ad networks that use cookies for personalized advertising, this must be clearly stated, and consent mechanisms (e.g., cookie banner/CMP) might be required depending on user jurisdiction.
        *   **Managing Cookies:** Provide information on how users can control or opt-out of non-essential cookies (e.g., through browser settings, or a cookie consent tool).
    *   **Usage Data (Application Analytics):**
        *   How users interact with the Service (e.g., features used, frequency of use, buttons clicked, errors encountered within the app). This data should ideally be anonymized or aggregated to improve the service.
        *   If processing user input text for service improvement (e.g., model training), this needs very clear disclosure and opt-in/out mechanisms (see "How We Use Your Information").

## 3. How We Use Your Information

Be specific about the purposes for which you collect and use personal information.

*   **To Provide, Operate, and Maintain the Service:**
    *   Process the input text as per user requests (e.g., paraphrase, modify style).
    *   Create and manage user accounts.
    *   Authenticate users and manage sessions.
    *   Provide customer support and respond to inquiries.
*   **To Process Payments (for Premium Services):**
    *   Facilitate transactions through your payment processor (e.g., Stripe) for premium subscriptions.
    *   Manage subscription statuses.
*   **To Improve and Develop the Service:**
    *   Analyze usage data and trends to understand how users interact with the Service, identify popular features, and pinpoint areas for improvement.
    *   For troubleshooting, debugging, and ensuring stability.
    *   **Important - Use of Input Text for Model Improvement:** If you intend to use users' input text (even anonymized) to train or improve your AI models (like the PEGASUS service or other "own algorithms"), this must be **very clearly stated**. You should explain:
        *   What data is used (e.g., input text, generated output).
        *   Whether it's anonymized/de-identified.
        *   The purpose (e.g., "to improve the accuracy and quality of our paraphrasing algorithms").
        *   Provide an **opt-out mechanism** or make it an **opt-in** feature, especially for identifiable or sensitive text. This is a critical transparency point. For an MVP, it's often safer to state you *do not* use user input for model training unless you have robust systems for consent and anonymization.
*   **To Communicate with You:**
    *   Send administrative information (e.g., service updates, changes to terms/policies, security alerts).
    *   Respond to your support requests or feedback.
    *   **Marketing Communications (Opt-in):** If you plan to send promotional emails or newsletters, state this and ensure you have an opt-in mechanism and an easy way to unsubscribe.
*   **For Legal Compliance and Safety:**
    *   Enforce our Terms of Service.
    *   Protect the security and integrity of our Service and users.
    *   Comply with legal obligations, court orders, or government requests.
    *   Prevent fraud and abuse.

## 4. How We Share Your Information (Disclosure to Third Parties)

List categories of third parties with whom you might share personal information.

*   **Service Providers (Data Processors):**
    *   Third-party vendors, consultants, and other service providers who perform services on our behalf and need access to user information to do that work.
    *   Examples:
        *   Payment Processor (e.g., Stripe - for handling payments).
        *   Cloud Hosting Provider (e.g., Vercel, AWS, Google Cloud - for hosting the application and database).
        *   Analytics Providers (e.g., Google Analytics - if used).
        *   Email Delivery Services (e.g., SendGrid, Mailgun - for sending transactional or marketing emails).
        *   Customer Support Platforms (e.g., Zendesk, Intercom - if used).
    *   Specify that these providers are bound by contractual obligations to keep information confidential and use it only for the purposes for which it was disclosed.
*   **AI Model Providers (If using third-party AI APIs for certain features):**
    *   If, in the future, certain text modification features rely on external AI APIs (e.g., from OpenAI, Anthropic, AI21 Labs), you must state that text input for those specific features may be sent to these providers for processing.
    *   Link to their respective privacy policies.
    *   Clarify that for self-hosted models (like the current PEGASUS service example), the text is processed on your own controlled infrastructure (as configured in your deployment).
*   **Legal Requirements:**
    *   If required to do so by law or in response to valid requests by public authorities (e.g., a court or a government agency).
    *   To protect our rights, property, or safety, or that of our users or the public.
*   **Business Transfers:**
    *   In connection with, or during negotiations of, any merger, sale of company assets, financing, or acquisition of all or a portion of our business by another company. Users would typically be notified.
*   **Aggregated or Anonymized Data:**
    *   We may share aggregated or anonymized information that does not directly identify individuals for research, statistical analysis, or public reporting.
*   **With Your Explicit Consent:**
    *   We may share your information with other third parties when we have your explicit consent to do so.

## 5. Data Retention
*   Explain your policy on how long different types of personal data are retained.
*   **User Account Data:** Retained as long as the user's account is active, and for a reasonable period thereafter to comply with legal obligations, resolve disputes, or enforce agreements (e.g., [specify period, like 1 year post-deletion for audit logs]).
*   **Input Text and Processed Output:**
    *   **Crucial Point:** Be very clear about this.
    *   **Option A (No Storage - Simplest for MVP):** "We do not store your input text or the processed output text on our servers beyond what is necessary to provide you with the Service during your active session. Once you close your session or the processing is complete, the text is discarded." (This simplifies privacy but means no history feature).
    *   **Option B (Temporary Storage for Processing):** "Input text is stored temporarily on our servers solely for the purpose of processing and is deleted shortly after the processed output is returned to you (e.g., within 24 hours)."
    *   **Option C (User History - Premium Feature):** "If you are a Premium user and the 'Processing History' feature is enabled, your input text snippets and their corresponding outputs will be stored and linked to your account until you choose to delete them or terminate your account, to provide you with this feature."
*   **Log Data (Server/Analytics):** Retained for a standard period necessary for security, analysis, and troubleshooting (e.g., 30-90 days, or as determined by your analytics provider's policy).
*   **Subscription Data (from Stripe):** Data related to subscriptions (e.g., `Subscription` table entries) will be retained as long as necessary for financial records, compliance, and managing the subscription lifecycle, even if a user account is deleted (though personal identifiers might be anonymized where appropriate).
*   **Account Deletion:** Explain what happens to data when a user requests account deletion.

## 6. Data Security
*   Describe the security measures you implement to protect user data (e.g., HTTPS/TLS encryption for data in transit, encryption at rest where applicable, access controls, secure server infrastructure from reputable cloud providers).
*   Acknowledge that no method of transmission over the Internet or electronic storage is 100% secure, and you cannot guarantee absolute security.
*   Mention user responsibility for securing their own account credentials.

## 7. Your Data Protection Rights (GDPR, CCPA, etc.)

This section is highly dependent on the jurisdictions your users are in and the specific data protection laws that apply to your service (e.g., GDPR for European users, CCPA/CPRA for California residents). **Legal counsel is essential here.**

*   **General Rights (often include):**
    *   **Right of Access:** Users can request copies of their personal data.
    *   **Right to Rectification:** Users can request correction of inaccurate or incomplete data.
    *   **Right to Erasure (Right to be Forgotten):** Users can request deletion of their personal data under certain conditions.
    *   **Right to Restrict Processing:** Users can request the restriction of processing their personal data under certain conditions.
    *   **Right to Object to Processing:** Users can object to the processing of their personal data under certain conditions (e.g., for direct marketing).
    *   **Right to Data Portability:** Users can request their data in a structured, commonly used, and machine-readable format to transmit to another controller.
    *   **Right to Withdraw Consent:** If processing is based on consent, users can withdraw it at any time.
*   **How to Exercise Rights:** Provide clear instructions on how users can submit requests to exercise their rights (e.g., via a dedicated email address like privacy@[yourapp].com, or through account settings if available).
*   **Verification:** Explain that you may need to verify the identity of the requester before processing such requests.
*   **Specific Jurisdictional Information:** Include separate subsections or addenda if you need to provide specific information for users in regions like the European Economic Area (EEA), United Kingdom, California, etc., detailing their specific rights and how you comply.

## 8. International Data Transfers (If Applicable)
*   If your service is global and you transfer personal data across borders (e.g., your hosting provider has servers in different countries, or you use third-party services based elsewhere):
    *   Explain where data may be processed and stored.
    *   Describe the safeguards in place for such transfers if data is moved outside a region with strong data protection laws (e.g., from the EEA to the US – mention mechanisms like Standard Contractual Clauses (SCCs), Adequacy Decisions, or the EU-U.S. Data Privacy Framework if applicable and you are certified).

## 9. Children's Privacy
*   State that the Service is not directed to individuals under a certain age (e.g., 13, 16, or 18, depending on your service and applicable laws like COPPA in the US).
*   Confirm that you do not knowingly collect personal information from children under this age without parental consent.
*   If you become aware that a child has provided personal data without consent, describe steps to delete it.

## 10. Cookies and Tracking Technologies (May be a separate Cookie Policy)
*   **Detailed Explanation:** Provide more detail on the types of cookies used (e.g., essential/strictly necessary, performance/analytics, functionality, targeting/advertising – if any).
*   **First-party vs. Third-party Cookies:** Explain the difference.
*   **Purpose of Each Cookie Type:**
*   **Duration:** How long cookies are stored.
*   **Managing Cookies:**
    *   How users can control cookie settings through their browser.
    *   If you use a cookie consent banner or management tool (often required by GDPR/ePrivacy Directive), explain how it works and how users can change their preferences.
*   **Other Tracking Technologies:** Mention if you use web beacons, pixels, local storage, etc., and for what purposes.

## 11. Changes to This Privacy Policy
*   State that you reserve the right to update or change the Privacy Policy at any time.
*   Explain how users will be notified of material changes (e.g., by posting the new policy on the site with an updated effective date, by email to registered users for significant changes).
*   Encourage users to review the Privacy Policy periodically.
*   Continued use of the Service after changes are posted will constitute acceptance of those changes.

## 12. Contact Us
*   Provide clear contact information for users who have questions or concerns about the Privacy Policy or their personal data.
*   Example: "If you have any questions about this Privacy Policy, please contact us at: privacy@[yourappdomain].com" or provide a link to a contact form.
```
