// src/services/paraphraseService.js

/**
 * Enhanced paraphrasing service with multiple AI providers for speed and reliability
 */

// Configuration for different AI providers
const AI_PROVIDERS = {
    OPENAI: {
        url: 'https://api.openai.com/v1/chat/completions',
        model: 'gpt-3.5-turbo',
        timeout: 10000
    },
    ANTHROPIC: {
        url: 'https://api.anthropic.com/v1/messages',
        model: 'claude-3-haiku-20240307',
        timeout: 10000
    },
    GROQ: {
        url: 'https://api.groq.com/openai/v1/chat/completions',
        model: 'llama3-8b-8192',
        timeout: 8000
    }
};

/**
 * Fast paraphrasing using OpenAI GPT-3.5-turbo
 */
async function paraphraseWithOpenAI(text) {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
        throw new Error('OpenAI API key not configured');
    }

    const response = await fetch(AI_PROVIDERS.OPENAI.url, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            model: AI_PROVIDERS.OPENAI.model,
            messages: [{
                role: 'system',
                content: `You are a professional editor making AI text sound more natural and human-like while maintaining quality and readability.

KEY REQUIREMENTS:
1. Replace formal AI words (utilize→use, leverage→use, facilitate→help)
2. Use natural contractions (don't, can't, it's, you're)
3. Vary sentence lengths naturally
4. Use simpler, more conversational language
5. Maintain proper grammar and clarity
6. Keep the original meaning intact
7. Make it sound like natural human writing
8. Avoid overly casual or silly language

Focus on subtle improvements that make the text feel more natural without compromising professionalism.`
            }, {
                role: 'user',
                content: `Rewrite this text to sound more natural and human-like while maintaining quality:\n\n${text}`
            }],
            max_tokens: Math.min(4000, text.length * 2),
            temperature: 0.7,
            presence_penalty: 0.1,
            frequency_penalty: 0.1
        }),
        signal: AbortSignal.timeout(AI_PROVIDERS.OPENAI.timeout)
    });

    if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content?.trim() || text;
}

/**
 * Fast paraphrasing using Groq (very fast inference)
 */
async function paraphraseWithGroq(text) {
    const apiKey = process.env.GROQ_API_KEY;
    if (!apiKey) {
        throw new Error('Groq API key not configured');
    }

    const response = await fetch(AI_PROVIDERS.GROQ.url, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            model: AI_PROVIDERS.GROQ.model,
            messages: [{
                role: 'system',
                content: `You are a professional writer making AI text sound more natural. Focus on:
- Replace formal words with simpler alternatives
- Use natural contractions (don't, can't, it's)
- Vary sentence structure naturally
- Use conversational but professional language
- Maintain clarity and proper grammar
- Keep the original meaning
- Make it sound like natural human writing
- Avoid overly casual or unprofessional language`
            }, {
                role: 'user',
                content: `Rewrite this text naturally while keeping it professional:\n\n${text}`
            }],
            max_tokens: Math.min(2000, text.length * 2),
            temperature: 0.6,
            top_p: 0.9
        }),
        signal: AbortSignal.timeout(AI_PROVIDERS.GROQ.timeout)
    });

    if (!response.ok) {
        throw new Error(`Groq API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content?.trim() || text;
}

/**
 * Advanced humanization algorithm designed to bypass AI detection
 */
function advancedHumanization(text) {
    let result = text;

    // Step 1: Replace AI-typical words with human alternatives
    const aiWordReplacements = {
        'utilize': ['use', 'employ', 'apply'],
        'leverage': ['use', 'take advantage of', 'make use of'],
        'delve': ['explore', 'look into', 'examine'],
        'facilitate': ['help', 'make easier', 'enable'],
        'optimize': ['improve', 'make better', 'enhance'],
        'implement': ['put in place', 'carry out', 'do'],
        'comprehensive': ['complete', 'thorough', 'full'],
        'innovative': ['new', 'creative', 'fresh'],
        'robust': ['strong', 'solid', 'reliable'],
        'seamless': ['smooth', 'easy', 'effortless'],
        'cutting-edge': ['latest', 'modern', 'new'],
        'state-of-the-art': ['latest', 'modern', 'advanced'],
        'paradigm': ['model', 'approach', 'way'],
        'synergy': ['teamwork', 'cooperation', 'working together'],
        'holistic': ['complete', 'whole', 'overall']
    };

    Object.entries(aiWordReplacements).forEach(([aiWord, humanWords]) => {
        const regex = new RegExp(`\\b${aiWord}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            const replacement = humanWords[Math.floor(Math.random() * humanWords.length)];
            return match === match.toLowerCase() ? replacement :
                   match === match.toUpperCase() ? replacement.toUpperCase() :
                   replacement.charAt(0).toUpperCase() + replacement.slice(1);
        });
    });

    // Step 2: Add human hesitations and personal touches (preserve paragraphs)
    const paragraphs = result.split(/\n\s*\n/);
    const processedParagraphs = paragraphs.map(paragraph => {
        if (!paragraph.trim()) return paragraph;

        const sentences = paragraph.split(/(?<=[.!?])\s+/);
        const humanizedSentences = sentences.map((sentence, index) => {
            if (Math.random() > 0.7 && sentence.length > 20) {
                const hesitations = ['Well, ', 'Actually, ', 'You know, ', 'I think ', 'It seems like ', 'Perhaps ', 'Maybe '];
                const hesitation = hesitations[Math.floor(Math.random() * hesitations.length)];
                sentence = hesitation + sentence.charAt(0).toLowerCase() + sentence.slice(1);
            }

            // Add personal opinions randomly
            if (Math.random() > 0.8 && sentence.length > 30) {
                const opinions = [', in my opinion', ', I believe', ', from what I can see', ', it appears'];
                const opinion = opinions[Math.floor(Math.random() * opinions.length)];
                sentence = sentence.replace(/\.$/, opinion + '.');
            }

            return sentence;
        });

        return humanizedSentences.join(' ');
    });

    result = processedParagraphs.join('\n\n');

    // Step 3: Add contractions for natural flow
    const contractions = {
        'do not': "don't", 'does not': "doesn't", 'did not': "didn't",
        'will not': "won't", 'would not': "wouldn't", 'could not': "couldn't",
        'should not': "shouldn't", 'cannot': "can't", 'is not': "isn't",
        'are not': "aren't", 'was not': "wasn't", 'were not': "weren't",
        'have not': "haven't", 'has not': "hasn't", 'had not': "hadn't",
        'it is': "it's", 'that is': "that's", 'there is': "there's",
        'you are': "you're", 'we are': "we're", 'they are': "they're"
    };

    Object.entries(contractions).forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.3) { // 70% chance to contract
                return match === match.toLowerCase() ? contracted :
                       match === match.toUpperCase() ? contracted.toUpperCase() :
                       contracted.charAt(0).toUpperCase() + contracted.slice(1);
            }
            return match;
        });
    });

    // Step 4: Vary sentence lengths dramatically (preserve paragraphs)
    const paragraphs2 = result.split(/\n\s*\n/);
    const processedParagraphs2 = paragraphs2.map(paragraph => {
        if (!paragraph.trim()) return paragraph;

        const finalSentences = paragraph.split(/(?<=[.!?])\s+/);
        const variedSentences = [];

        for (let i = 0; i < finalSentences.length; i++) {
            let sentence = finalSentences[i];

            // Randomly split long sentences
            if (sentence.length > 80 && Math.random() > 0.6) {
                const commaIndex = sentence.indexOf(',', 30);
                if (commaIndex > 0) {
                    const part1 = sentence.substring(0, commaIndex) + '.';
                    const part2 = sentence.substring(commaIndex + 1).trim();
                    variedSentences.push(part1);
                    sentence = part2.charAt(0).toUpperCase() + part2.slice(1);
                }
            }

            // Add short interjections
            if (Math.random() > 0.85 && i > 0) {
                const interjections = ['Right.', 'Exactly.', 'True.', 'Fair enough.', 'Makes sense.'];
                variedSentences.push(interjections[Math.floor(Math.random() * interjections.length)]);
            }

            variedSentences.push(sentence);
        }

        return variedSentences.join(' ');
    });

    result = processedParagraphs2.join('\n\n');

    // Step 5: Add natural redundancies and filler phrases
    result = result.replace(/\. ([A-Z])/g, (match, letter) => {
        if (Math.random() > 0.8) {
            const fillers = ['So, ', 'Now, ', 'Anyway, ', 'Look, ', 'Listen, ', 'Here\'s the thing - '];
            const filler = fillers[Math.floor(Math.random() * fillers.length)];
            return `. ${filler}${letter.toLowerCase()}`;
        }
        return match;
    });

    // Step 6: Add minor grammatical imperfections that humans make
    if (Math.random() > 0.7) {
        result = result.replace(/\band\s+([a-z])/g, (match, letter) => {
            if (Math.random() > 0.8) {
                return `and, ${letter}`; // Add comma before 'and' sometimes
            }
            return match;
        });
    }

    return result;
}

/**
 * Main paraphrasing function with multiple providers and fallbacks
 */
export async function paraphraseWithPegasus(textToParaphrase) {
    if (!textToParaphrase || typeof textToParaphrase !== 'string' || !textToParaphrase.trim()) {
        return { error: true, message: 'Input text is required and must be a non-empty string.' };
    }

    // For very short texts, use enhanced balanced local humanization
    if (textToParaphrase.length < 50) {
        const { balancedHumanization } = require('../utils/balancedHumanizer');
        return {
            paraphrased_text: balancedHumanization(textToParaphrase, null, 0, {
                useAdvanced: true,
                aggressiveness: 0.5 // Lower for short texts
            }),
            error: false,
            provider: 'local-enhanced'
        };
    }

    // Try providers in order of speed: Groq -> OpenAI -> Local
    const providers = [
        { name: 'groq', func: paraphraseWithGroq },
        { name: 'openai', func: paraphraseWithOpenAI }
    ];

    for (const provider of providers) {
        try {
            console.log(`Attempting paraphrasing with ${provider.name}...`);
            const startTime = Date.now();

            const result = await provider.func(textToParaphrase);
            const processingTime = Date.now() - startTime;

            console.log(`${provider.name} paraphrasing completed in ${processingTime}ms`);

            if (result && result.length > 10) {
                // Apply enhanced balanced humanization to AI-generated results
                const { balancedHumanization } = require('../utils/balancedHumanizer');
                const humanizedResult = balancedHumanization(result, null, 0, {
                    useAdvanced: true,
                    aggressiveness: 0.7, // Higher for AI-generated content
                    maintainTone: true
                });
                return {
                    paraphrased_text: humanizedResult,
                    error: false,
                    provider: provider.name + '-enhanced',
                    processingTime
                };
            }
        } catch (error) {
            console.warn(`${provider.name} paraphrasing failed:`, error.message);
            continue;
        }
    }

    // Fallback to enhanced balanced local humanization
    console.log('Using enhanced local humanization as fallback...');
    const { balancedHumanization } = require('../utils/balancedHumanizer');
    const result = balancedHumanization(textToParaphrase, null, 0, {
        useAdvanced: true,
        aggressiveness: 0.8, // Higher aggressiveness for fallback to ensure effectiveness
        maintainTone: true
    });

    return {
        paraphrased_text: result,
        error: false,
        provider: 'local-enhanced-fallback'
    };
}
