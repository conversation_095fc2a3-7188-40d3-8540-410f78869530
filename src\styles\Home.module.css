.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 50%, var(--primary-100) 100%);
  padding: var(--space-20) 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e0f2fe" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.heroContent {
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.heroTitle {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: var(--space-6);
  color: var(--secondary-900);
}

.highlight {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--primary-400), var(--primary-600));
  border-radius: 2px;
  opacity: 0.3;
}

.heroSubtitle {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  color: var(--secondary-600);
  margin-bottom: var(--space-12);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.signInPrompt {
    background-color: #fff3cd; /* Light yellow background */
    border: 1px solid #ffeeba; /* Yellow border */
    color: #856404; /* Darker yellow text */
    padding: 15px;
    margin-top: 20px;
    margin-bottom: 20px;
    border-radius: 6px;
    text-align: center;
    font-size: 1rem;
}

.signInPrompt a {
    color: #0056b3; /* Standard link blue, or match your theme */
    font-weight: bold;
    text-decoration: underline;
}

.signInPrompt a:hover {
    color: #003d80;
}

/* New styles for status box on index.js */
.statusBox {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 15px;
    margin-top: 20px;
    margin-bottom: 25px;
    border-radius: 6px;
    text-align: center;
    font-size: 1rem;
}

.statusBox p {
    margin: 0.5rem 0;
}

.premiumStatus {
    color: #28a745; /* Green */
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
    background-color: #e6ffe6;
}

.freeStatus {
    color: #fd7e14; /* Orange */
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 4px;
    background-color: #fff8e1;
}

.upgradePrompt {
    margin-top: 0.75rem !important;
    font-size: 0.95rem;
}

.upgradePrompt a {
    color: #007bff;
    text-decoration: none;
    font-weight: 600;
}

.upgradePrompt a:hover {
    text-decoration: underline;
    color: #0056b3;
}

/* Hero Stats */
.heroStats {
  display: flex;
  justify-content: center;
  gap: var(--space-8);
  flex-wrap: wrap;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
}

.statNumber {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-600);
}

.statLabel {
  font-size: 0.875rem;
  color: var(--secondary-500);
  font-weight: 500;
}

/* Processing Section */
.processingSection {
  padding: var(--space-16) 0;
  background: white;
}

/* Error Message */
.errorMessage {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  background: var(--error-50);
  color: var(--error-600);
  padding: var(--space-4) var(--space-5);
  border-radius: var(--radius-lg);
  border: 1px solid var(--error-200);
  margin: var(--space-6) auto;
  max-width: 600px;
  font-weight: 500;
}

.errorMessage svg {
  flex-shrink: 0;
}

/* Stats Display */
.statsDisplay {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  padding: var(--space-6);
  margin: var(--space-8) auto;
  max-width: 600px;
  border: 1px solid var(--secondary-200);
}

.statsDisplay h3 {
  text-align: center;
  margin-bottom: var(--space-4);
  color: var(--secondary-900);
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-4);
}

.statItem {
  text-align: center;
  padding: var(--space-3);
  background: var(--secondary-50);
  border-radius: var(--radius-md);
}

.statValue {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-600);
  margin-bottom: var(--space-1);
}

.statItem .statLabel {
  font-size: 0.875rem;
  color: var(--secondary-600);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroStats {
    gap: var(--space-4);
  }

  .stat {
    min-width: 100px;
  }

  .statNumber {
    font-size: 1.5rem;
  }

  .processingSection {
    padding: var(--space-12) 0;
  }

  .statsGrid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
}
