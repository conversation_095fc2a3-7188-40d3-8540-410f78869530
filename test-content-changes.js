// Test actual content changes rather than position changes
const testText = `Artificial intelligence has revolutionized numerous industries and continues to demonstrate significant potential for future applications. Furthermore, it is important to note that machine learning algorithms can effectively analyze vast amounts of data to identify patterns and make predictions. Additionally, these systems utilize sophisticated neural networks to process information in ways that mimic human cognitive processes.

Moreover, the implementation of AI technologies has resulted in substantial improvements in efficiency and accuracy across various sectors. It is worth mentioning that organizations are increasingly leveraging these capabilities to optimize their operations and enhance decision-making processes. Consequently, the integration of artificial intelligence represents a transformative shift in how businesses approach complex challenges.

In conclusion, the continued advancement of AI systems will undoubtedly lead to even more innovative solutions and applications. It should be noted that this technological evolution requires careful consideration of ethical implications and responsible development practices. Therefore, stakeholders must collaborate to ensure that artificial intelligence benefits society as a whole.`;

async function testContentChanges() {
    console.log('🧪 Testing Content Changes (Not Position Changes)...\n');
    
    try {
        const response = await fetch('http://localhost:3003/api/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: testText
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        console.log('📝 Original text:');
        console.log(testText);
        console.log('\n' + '='.repeat(80) + '\n');
        
        console.log('🔄 Humanized text:');
        console.log(data.modifiedText);
        console.log('\n' + '='.repeat(80) + '\n');
        
        // Extract unique words from both texts
        const originalWords = new Set(testText.toLowerCase().match(/\b[a-z]+\b/g) || []);
        const humanizedWords = new Set(data.modifiedText.toLowerCase().match(/\b[a-z]+\b/g) || []);
        
        // Find words that were removed (replaced)
        const removedWords = [...originalWords].filter(word => !humanizedWords.has(word));
        
        // Find words that were added (new words)
        const addedWords = [...humanizedWords].filter(word => !originalWords.has(word));
        
        console.log('📊 Vocabulary Analysis:');
        console.log(`Original unique words: ${originalWords.size}`);
        console.log(`Humanized unique words: ${humanizedWords.size}`);
        console.log(`Words removed: ${removedWords.length}`);
        console.log(`Words added: ${addedWords.length}`);
        
        console.log('\n🔍 Specific Word Changes:');
        console.log('Words removed from original:');
        removedWords.slice(0, 15).forEach(word => console.log(`  - "${word}"`));
        if (removedWords.length > 15) console.log(`  ... and ${removedWords.length - 15} more`);
        
        console.log('\nWords added in humanized:');
        addedWords.slice(0, 15).forEach(word => console.log(`  + "${word}"`));
        if (addedWords.length > 15) console.log(`  ... and ${addedWords.length - 15} more`);
        
        // Check specific AI patterns
        const aiPatterns = {
            'revolutionized': 'changed',
            'numerous': 'many',
            'significant': 'important',
            'furthermore': 'also',
            'additionally': 'also',
            'moreover': 'plus',
            'utilize': 'use',
            'sophisticated': 'advanced',
            'effectively': 'well',
            'substantial': 'large',
            'various': 'different',
            'consequently': 'so',
            'therefore': 'so',
            'undoubtedly': 'clearly',
            'innovative': 'new',
            'demonstrate': 'show'
        };
        
        console.log('\n🤖 AI Pattern Replacement Check:');
        let patternsReplaced = 0;
        let patternsTotal = 0;
        
        for (const [aiWord, humanWord] of Object.entries(aiPatterns)) {
            const originalHas = testText.toLowerCase().includes(aiWord);
            const humanizedHas = data.modifiedText.toLowerCase().includes(aiWord);
            const humanizedHasReplacement = data.modifiedText.toLowerCase().includes(humanWord);
            
            if (originalHas) {
                patternsTotal++;
                if (!humanizedHas && humanizedHasReplacement) {
                    console.log(`✅ "${aiWord}" → "${humanWord}"`);
                    patternsReplaced++;
                } else if (!humanizedHas) {
                    console.log(`⚠️  "${aiWord}" → (removed but no clear replacement)`);
                    patternsReplaced++;
                } else {
                    console.log(`❌ "${aiWord}" → (unchanged)`);
                }
            }
        }
        
        console.log(`\nPattern replacement rate: ${patternsReplaced}/${patternsTotal} (${((patternsReplaced/patternsTotal)*100).toFixed(1)}%)`);
        
        // Overall assessment
        const vocabularyChangeRate = (removedWords.length / originalWords.size) * 100;
        console.log('\n🎯 Quality Assessment:');
        console.log(`Vocabulary change rate: ${vocabularyChangeRate.toFixed(1)}%`);
        
        if (vocabularyChangeRate < 10) {
            console.log('❌ POOR: Less than 10% vocabulary change');
        } else if (vocabularyChangeRate < 25) {
            console.log('⚠️  FAIR: 10-25% vocabulary change');
        } else if (vocabularyChangeRate < 40) {
            console.log('✅ GOOD: 25-40% vocabulary change');
        } else {
            console.log('🎉 EXCELLENT: 40%+ vocabulary change');
        }
        
        // Check readability
        const originalSentences = testText.split(/[.!?]+/).length - 1;
        const humanizedSentences = data.modifiedText.split(/[.!?]+/).length - 1;
        console.log(`Sentence count: ${originalSentences} → ${humanizedSentences} ${originalSentences === humanizedSentences ? '✅' : '⚠️'}`);
        
        // Check paragraph preservation
        const originalParagraphs = testText.split(/\n\s*\n/).length;
        const humanizedParagraphs = data.modifiedText.split(/\n\s*\n/).length;
        console.log(`Paragraph count: ${originalParagraphs} → ${humanizedParagraphs} ${originalParagraphs === humanizedParagraphs ? '✅' : '❌'}`);
        
    } catch (error) {
        console.error('❌ Error testing humanization:', error.message);
    }
}

testContentChanges();
