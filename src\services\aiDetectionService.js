/**
 * AI Detection Service
 * Integrates with multiple AI detection APIs for real-time validation
 * Supports GPTZero, Originality.ai, and other detection services
 */

import axios from 'axios';

// AI Detection API configurations
const DETECTION_APIS = {
    gptzero: {
        name: 'GPT<PERSON><PERSON>',
        endpoint: 'https://api.gptzero.me/v2/predict/text',
        apiKeyEnv: 'GPTZERO_API_KEY',
        headers: (apiKey) => ({
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        }),
        requestFormat: (text) => ({
            document: text
        }),
        parseResponse: (response) => ({
            score: response.data.documents[0]?.completely_generated_prob * 100 || 0,
            confidence: response.data.documents[0]?.average_generated_prob || 0,
            details: response.data.documents[0]
        })
    },
    
    originality: {
        name: 'Originality.ai',
        endpoint: 'https://api.originality.ai/api/v1/scan/ai',
        apiKeyEnv: 'ORIGINALITY_API_KEY',
        headers: (apiKey) => ({
            'X-OAI-API-KEY': apiKey,
            'Content-Type': 'application/json'
        }),
        requestFormat: (text) => ({
            content: text,
            title: 'Humanization Validation',
            aiModelVersion: '1',
            storeScan: false
        }),
        parseResponse: (response) => ({
            score: response.data.score.ai * 100,
            confidence: response.data.score.ai,
            details: response.data
        })
    },
    
    sapling: {
        name: 'Sapling AI Detector',
        endpoint: 'https://api.sapling.ai/api/v1/aidetect',
        apiKeyEnv: 'SAPLING_API_KEY',
        headers: (apiKey) => ({
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        }),
        requestFormat: (text) => ({
            text: text
        }),
        parseResponse: (response) => ({
            score: response.data.score * 100,
            confidence: response.data.score,
            details: response.data
        })
    }
};

/**
 * Validate text against AI detection with automatic retry logic
 */
export async function validateWithRealTimeDetection(text, options = {}) {
    const {
        targetDetection = 10,
        maxRetries = 3,
        preferredAPI = 'gptzero',
        fallbackAPIs = ['originality', 'sapling'],
        timeout = 15000
    } = options;

    console.log(`Starting real-time AI detection validation (target: ≤${targetDetection}%)`);

    // Try preferred API first
    let result = await callDetectionAPI(preferredAPI, text, timeout);
    
    if (!result.success && fallbackAPIs.length > 0) {
        console.log(`${preferredAPI} failed, trying fallback APIs...`);
        
        // Try fallback APIs
        for (const apiName of fallbackAPIs) {
            result = await callDetectionAPI(apiName, text, timeout);
            if (result.success) break;
        }
    }

    if (!result.success) {
        console.warn('All AI detection APIs failed, using heuristic validation');
        return await heuristicDetectionValidation(text, targetDetection);
    }

    const detectionScore = result.score;
    const meetsTarget = detectionScore <= targetDetection;

    console.log(`AI Detection Result: ${detectionScore.toFixed(1)}% (${result.apiName})`);
    console.log(`Target Met: ${meetsTarget ? '✅' : '❌'} (≤${targetDetection}%)`);

    return {
        success: true,
        score: detectionScore,
        meetsTarget: meetsTarget,
        apiName: result.apiName,
        confidence: result.confidence,
        details: result.details,
        recommendation: generateRetryRecommendation(detectionScore, targetDetection)
    };
}

/**
 * Call specific AI detection API
 */
async function callDetectionAPI(apiName, text, timeout = 15000) {
    const api = DETECTION_APIS[apiName];
    if (!api) {
        return { success: false, error: `Unknown API: ${apiName}` };
    }

    const apiKey = process.env[api.apiKeyEnv];
    if (!apiKey) {
        console.log(`${api.name} API key not configured (${api.apiKeyEnv})`);
        return { success: false, error: `API key not configured for ${api.name}` };
    }

    try {
        console.log(`Calling ${api.name} API for detection validation...`);
        
        const requestData = api.requestFormat(text);
        const headers = api.headers(apiKey);

        const response = await axios.post(api.endpoint, requestData, {
            headers,
            timeout
        });

        const parsed = api.parseResponse(response);
        
        return {
            success: true,
            score: parsed.score,
            confidence: parsed.confidence,
            details: parsed.details,
            apiName: api.name
        };

    } catch (error) {
        console.error(`${api.name} API error:`, error.message);
        
        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error || error.response.data?.message || 'Unknown API error';
            return { 
                success: false, 
                error: `${api.name} API error (${status}): ${message}` 
            };
        } else if (error.code === 'ECONNABORTED') {
            return { 
                success: false, 
                error: `${api.name} API timeout` 
            };
        } else {
            return { 
                success: false, 
                error: `${api.name} API error: ${error.message}` 
            };
        }
    }
}

/**
 * Enhanced heuristic detection validation as fallback
 */
async function heuristicDetectionValidation(text, targetDetection) {
    console.log('Using enhanced heuristic AI detection validation...');
    
    const patterns = [
        { pattern: /\b(furthermore|moreover|consequently|therefore|thus|hence|additionally)\b/gi, weight: 4, name: 'formal_transitions' },
        { pattern: /\b(it is important to note|it should be noted|it is worth mentioning|it must be emphasized)\b/gi, weight: 5, name: 'robotic_phrases' },
        { pattern: /\b(in conclusion|to summarize|in summary|to conclude|finally|lastly)\b/gi, weight: 4, name: 'mechanical_conclusions' },
        { pattern: /\b(comprehensive|extensive|significant|substantial|considerable|numerous|various)\b/gi, weight: 2, name: 'overqualification' },
        { pattern: /\b(is|are|was|were|been|being)\s+\w+ed\b/gi, weight: 1, name: 'passive_voice' },
        { pattern: /\b(clearly|obviously|certainly|definitely|undoubtedly|unquestionably)\b/gi, weight: 3, name: 'certainty_markers' },
        { pattern: /^[A-Z][^.!?]{30,}[.!?]\s+[A-Z][^.!?]{30,}[.!?]/gm, weight: 2, name: 'repetitive_structure' }
    ];
    
    let totalScore = 0;
    let patternDetails = [];
    
    patterns.forEach(({ pattern, weight, name }) => {
        const matches = text.match(pattern) || [];
        if (matches.length > 0) {
            const score = matches.length * weight;
            totalScore += score;
            patternDetails.push({
                name,
                matches: matches.length,
                weight,
                score,
                examples: matches.slice(0, 2)
            });
        }
    });
    
    // Additional factors
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    
    if (avgSentenceLength > 30) {
        totalScore += 3;
        patternDetails.push({
            name: 'long_sentences',
            matches: 1,
            weight: 3,
            score: 3,
            examples: [`Average sentence length: ${avgSentenceLength.toFixed(1)} characters`]
        });
    }
    
    // Calculate detection percentage
    const textLength = text.length;
    const density = (totalScore / textLength) * 1000;
    const estimatedDetection = Math.min(density * 12, 95); // Adjusted multiplier for stricter detection
    
    const meetsTarget = estimatedDetection <= targetDetection;
    
    console.log(`Heuristic Detection: ${estimatedDetection.toFixed(1)}% (${patternDetails.length} pattern types)`);
    
    return {
        success: true,
        score: estimatedDetection,
        meetsTarget: meetsTarget,
        apiName: 'Heuristic Analysis',
        confidence: 0.7, // Lower confidence for heuristic method
        details: {
            totalScore,
            density: density.toFixed(2),
            patterns: patternDetails,
            avgSentenceLength: avgSentenceLength.toFixed(1)
        },
        recommendation: generateRetryRecommendation(estimatedDetection, targetDetection)
    };
}

/**
 * Generate recommendation for retry strategy
 */
function generateRetryRecommendation(detectionScore, targetDetection) {
    if (detectionScore <= targetDetection) {
        return {
            shouldRetry: false,
            message: `Detection score ${detectionScore.toFixed(1)}% meets target ≤${targetDetection}%`,
            suggestedAggressiveness: null
        };
    }
    
    const excess = detectionScore - targetDetection;
    let suggestedAggressiveness;
    let message;
    
    if (excess <= 5) {
        suggestedAggressiveness = 0.1; // Minor increase
        message = `Score ${detectionScore.toFixed(1)}% slightly exceeds target. Retry with minor aggressiveness increase.`;
    } else if (excess <= 15) {
        suggestedAggressiveness = 0.2; // Moderate increase
        message = `Score ${detectionScore.toFixed(1)}% moderately exceeds target. Retry with moderate aggressiveness increase.`;
    } else {
        suggestedAggressiveness = 0.3; // Major increase
        message = `Score ${detectionScore.toFixed(1)}% significantly exceeds target. Retry with major aggressiveness increase.`;
    }
    
    return {
        shouldRetry: true,
        message,
        suggestedAggressiveness,
        excessScore: excess
    };
}

/**
 * Check if any AI detection APIs are configured
 */
export function isRealTimeDetectionAvailable() {
    return Object.values(DETECTION_APIS).some(api => 
        process.env[api.apiKeyEnv]
    );
}

/**
 * Get status of all configured detection APIs
 */
export function getDetectionAPIStatus() {
    const status = {};
    
    Object.entries(DETECTION_APIS).forEach(([key, api]) => {
        status[key] = {
            name: api.name,
            configured: !!process.env[api.apiKeyEnv],
            envVar: api.apiKeyEnv
        };
    });
    
    return status;
}
