/**
 * Style Profiles API
 * Handles CRUD operations for user writing style profiles
 */

import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    // Get user session
    const session = await getServerSession(req, res, authOptions);
    
    if (!session?.user?.id) {
        return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = session.user.id;

    try {
        switch (req.method) {
            case 'GET':
                return await handleGet(req, res, userId);
            case 'POST':
                return await handlePost(req, res, userId);
            case 'PUT':
                return await handlePut(req, res, userId);
            case 'DELETE':
                return await handleDelete(req, res, userId);
            default:
                res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
                return res.status(405).json({ error: `Method ${req.method} not allowed` });
        }
    } catch (error) {
        console.error('Style profiles API error:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
}

/**
 * GET /api/style-profiles
 * Retrieves all style profiles for the authenticated user
 */
async function handleGet(req, res, userId) {
    const profiles = await prisma.styleProfile.findMany({
        where: { userId },
        orderBy: [
            { isActive: 'desc' },
            { updatedAt: 'desc' }
        ]
    });

    // Parse JSON fields for each profile
    const parsedProfiles = profiles.map(profile => ({
        ...profile,
        sentencePatterns: JSON.parse(profile.sentencePatterns),
        vocabularyComplexity: JSON.parse(profile.vocabularyComplexity),
        transitionPhrases: JSON.parse(profile.transitionPhrases),
        punctuationStyle: JSON.parse(profile.punctuationStyle),
        personalExpressions: JSON.parse(profile.personalExpressions),
        writingQuirks: JSON.parse(profile.writingQuirks),
        toneAnalysis: JSON.parse(profile.toneAnalysis)
    }));

    return res.status(200).json(parsedProfiles);
}

/**
 * POST /api/style-profiles
 * Creates a new style profile
 */
async function handlePost(req, res, userId) {
    const {
        name,
        description,
        styleAnalysis,
        defaultStrength,
        sampleCount,
        totalSampleWords
    } = req.body;

    // Validation
    if (!name || !styleAnalysis) {
        return res.status(400).json({ error: 'Name and style analysis are required' });
    }

    if (typeof defaultStrength !== 'number' || defaultStrength < 0 || defaultStrength > 100) {
        return res.status(400).json({ error: 'Default strength must be a number between 0 and 100' });
    }

    // Check if user already has a profile with this name
    const existingProfile = await prisma.styleProfile.findFirst({
        where: {
            userId,
            name
        }
    });

    if (existingProfile) {
        return res.status(400).json({ error: 'A style profile with this name already exists' });
    }

    // If this is the user's first profile, make it active
    const existingProfilesCount = await prisma.styleProfile.count({
        where: { userId }
    });

    const isActive = existingProfilesCount === 0;

    // Create the profile
    const profile = await prisma.styleProfile.create({
        data: {
            userId,
            name,
            description: description || null,
            isActive,
            sentencePatterns: JSON.stringify(styleAnalysis.sentencePatterns),
            vocabularyComplexity: JSON.stringify(styleAnalysis.vocabularyComplexity),
            transitionPhrases: JSON.stringify(styleAnalysis.transitionPhrases),
            punctuationStyle: JSON.stringify(styleAnalysis.punctuationStyle),
            personalExpressions: JSON.stringify(styleAnalysis.personalExpressions),
            writingQuirks: JSON.stringify(styleAnalysis.writingQuirks),
            toneAnalysis: JSON.stringify(styleAnalysis.toneAnalysis),
            defaultStrength,
            sampleCount: sampleCount || 0,
            totalSampleWords: totalSampleWords || 0
        }
    });

    // Parse JSON fields for response
    const parsedProfile = {
        ...profile,
        sentencePatterns: JSON.parse(profile.sentencePatterns),
        vocabularyComplexity: JSON.parse(profile.vocabularyComplexity),
        transitionPhrases: JSON.parse(profile.transitionPhrases),
        punctuationStyle: JSON.parse(profile.punctuationStyle),
        personalExpressions: JSON.parse(profile.personalExpressions),
        writingQuirks: JSON.parse(profile.writingQuirks),
        toneAnalysis: JSON.parse(profile.toneAnalysis)
    };

    return res.status(201).json(parsedProfile);
}

/**
 * PUT /api/style-profiles
 * Updates an existing style profile
 */
async function handlePut(req, res, userId) {
    const { id, name, description, defaultStrength, isActive } = req.body;

    if (!id) {
        return res.status(400).json({ error: 'Profile ID is required' });
    }

    // Verify the profile belongs to the user
    const existingProfile = await prisma.styleProfile.findFirst({
        where: {
            id,
            userId
        }
    });

    if (!existingProfile) {
        return res.status(404).json({ error: 'Style profile not found' });
    }

    // If setting this profile as active, deactivate others
    if (isActive) {
        await prisma.styleProfile.updateMany({
            where: {
                userId,
                id: { not: id }
            },
            data: { isActive: false }
        });
    }

    // Update the profile
    const updatedProfile = await prisma.styleProfile.update({
        where: { id },
        data: {
            ...(name && { name }),
            ...(description !== undefined && { description }),
            ...(typeof defaultStrength === 'number' && { defaultStrength }),
            ...(typeof isActive === 'boolean' && { isActive }),
            ...(isActive && { lastUsedAt: new Date() })
        }
    });

    // Parse JSON fields for response
    const parsedProfile = {
        ...updatedProfile,
        sentencePatterns: JSON.parse(updatedProfile.sentencePatterns),
        vocabularyComplexity: JSON.parse(updatedProfile.vocabularyComplexity),
        transitionPhrases: JSON.parse(updatedProfile.transitionPhrases),
        punctuationStyle: JSON.parse(updatedProfile.punctuationStyle),
        personalExpressions: JSON.parse(updatedProfile.personalExpressions),
        writingQuirks: JSON.parse(updatedProfile.writingQuirks),
        toneAnalysis: JSON.parse(updatedProfile.toneAnalysis)
    };

    return res.status(200).json(parsedProfile);
}

/**
 * DELETE /api/style-profiles
 * Deletes a style profile
 */
async function handleDelete(req, res, userId) {
    const { id } = req.body;

    if (!id) {
        return res.status(400).json({ error: 'Profile ID is required' });
    }

    // Verify the profile belongs to the user
    const existingProfile = await prisma.styleProfile.findFirst({
        where: {
            id,
            userId
        }
    });

    if (!existingProfile) {
        return res.status(404).json({ error: 'Style profile not found' });
    }

    // Delete the profile
    await prisma.styleProfile.delete({
        where: { id }
    });

    // If this was the active profile, activate another one if available
    if (existingProfile.isActive) {
        const nextProfile = await prisma.styleProfile.findFirst({
            where: { userId },
            orderBy: { updatedAt: 'desc' }
        });

        if (nextProfile) {
            await prisma.styleProfile.update({
                where: { id: nextProfile.id },
                data: { isActive: true }
            });
        }
    }

    return res.status(200).json({ message: 'Style profile deleted successfully' });
}

// Cleanup function to close Prisma connection
export const config = {
    api: {
        externalResolver: true,
    },
};

// Close Prisma connection when the process exits
process.on('beforeExit', async () => {
    await prisma.$disconnect();
});
