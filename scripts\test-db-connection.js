/**
 * Test Database Connection
 * Verifies that Prisma can connect to the database properly
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDatabaseConnection() {
    console.log('🔍 Testing database connection...');
    
    try {
        // Test basic connection
        await prisma.$connect();
        console.log('✅ Database connection successful');
        
        // Test a simple query
        const userCount = await prisma.user.count();
        console.log(`✅ Database query successful - Found ${userCount} users`);
        
        // Test if all tables exist
        const tables = await prisma.$queryRaw`
            SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;
        `;
        console.log('✅ Database tables:', tables.map(t => t.name).join(', '));
        
        return true;
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        return false;
    } finally {
        await prisma.$disconnect();
    }
}

testDatabaseConnection()
    .then((success) => {
        if (success) {
            console.log('🎉 Database is ready!');
            process.exit(0);
        } else {
            console.log('💥 Database connection failed!');
            process.exit(1);
        }
    })
    .catch((error) => {
        console.error('💥 Test failed:', error);
        process.exit(1);
    });
