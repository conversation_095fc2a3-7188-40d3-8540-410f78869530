import React, { createContext, useContext, useState } from 'react';

// 1. Create the Context
const AppContext = createContext();

// 2. Create a Provider Component
export const AppProvider = ({ children }) => {
    // Example state (can be expanded later)
    const [theme, setTheme] = useState('light'); // Example: theme management
    const [user, setUser] = useState(null);      // Example: user session

    const toggleTheme = () => {
        setTheme(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
    };

    // Values to be passed through context
    const value = {
        theme,
        toggleTheme,
        user,
        setUser,
        // Add other global states or functions here
    };

    return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

// 3. Create a custom hook to use the AppContext
export const useAppContext = () => {
    const context = useContext(AppContext);
    if (context === undefined) {
        throw new Error('useAppContext must be used within an AppProvider');
    }
    return context;
};

// Optional: Export the context directly if needed elsewhere
// export default AppContext;
