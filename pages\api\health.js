// Simple health check for static export
export default function handler(req, res) {
    if (req.method !== 'GET') {
        res.setHeader('Allow', ['GET']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    const healthData = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: 'GhostLayer API',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        platform: 'Static Export'
    };

    res.status(200).json(healthData);
}
