/* Style Profile Creator Component Styles */

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.modal {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 32px;
    border-bottom: 1px solid #e5e7eb;
}

.header h2 {
    margin: 0;
    color: #1f2937;
    font-size: 24px;
    font-weight: 600;
}

.closeButton {
    background: none;
    border: none;
    font-size: 28px;
    color: #6b7280;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.closeButton:hover {
    background: #f3f4f6;
    color: #374151;
}

.progressBar {
    display: flex;
    padding: 20px 32px;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.progressStep {
    flex: 1;
    text-align: center;
    padding: 8px 16px;
    color: #6b7280;
    font-weight: 500;
    position: relative;
}

.progressStep.active {
    color: #3b82f6;
}

.progressStep:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -50%;
    top: 50%;
    width: 100%;
    height: 2px;
    background: #e5e7eb;
    transform: translateY(-50%);
}

.progressStep.active:not(:last-child)::after {
    background: #3b82f6;
}

.step {
    padding: 32px;
}

.step h3 {
    margin: 0 0 16px 0;
    color: #1f2937;
    font-size: 20px;
    font-weight: 600;
}

.instruction {
    color: #6b7280;
    margin-bottom: 24px;
    line-height: 1.6;
}

.sampleContainer {
    margin-bottom: 24px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    background: #f9fafb;
}

.sampleHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.sampleHeader label {
    font-weight: 600;
    color: #374151;
}

.removeButton {
    background: #ef4444;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.removeButton:hover {
    background: #dc2626;
}

.sampleTextarea {
    width: 100%;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 12px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.sampleTextarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.wordCount {
    text-align: right;
    font-size: 12px;
    color: #6b7280;
    margin-top: 8px;
}

.addSampleButton {
    background: #f3f4f6;
    border: 2px dashed #d1d5db;
    color: #6b7280;
    padding: 16px;
    border-radius: 8px;
    width: 100%;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    margin-bottom: 24px;
}

.addSampleButton:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
    color: #374151;
}

.stepActions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.primaryButton {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s ease;
}

.primaryButton:hover:not(:disabled) {
    background: #2563eb;
}

.primaryButton:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

.secondaryButton {
    background: white;
    color: #6b7280;
    border: 1px solid #d1d5db;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.secondaryButton:hover {
    background: #f9fafb;
    color: #374151;
    border-color: #9ca3af;
}

.error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 6px;
    margin: 0 32px 16px 32px;
    font-size: 14px;
}

.analysisGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.analysisCard {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
}

.analysisCard h4 {
    margin: 0 0 12px 0;
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
}

.analysisCard p {
    margin: 4px 0;
    color: #6b7280;
    font-size: 14px;
}

.lengthDistribution {
    display: flex;
    gap: 12px;
    margin-top: 8px;
}

.lengthDistribution div {
    background: #e5e7eb;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #374151;
}

.topTransitions {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
}

.transitionTag {
    background: #dbeafe;
    color: #1e40af;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.punctuationPrefs {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
}

.prefTag {
    background: #dcfce7;
    color: #166534;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.configForm {
    margin-bottom: 24px;
}

.formGroup {
    margin-bottom: 20px;
}

.formGroup label {
    display: block;
    margin-bottom: 6px;
    color: #374151;
    font-weight: 600;
    font-size: 14px;
}

.textInput {
    width: 100%;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 12px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.textInput:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.textareaInput {
    width: 100%;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 12px;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.textareaInput:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.rangeInput {
    width: 100%;
    margin: 8px 0;
}

.strengthDescription {
    text-align: center;
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

@media (max-width: 768px) {
    .modal {
        margin: 10px;
        max-height: calc(100vh - 20px);
    }
    
    .header {
        padding: 20px;
    }
    
    .step {
        padding: 20px;
    }
    
    .progressBar {
        padding: 16px 20px;
    }
    
    .analysisGrid {
        grid-template-columns: 1fr;
    }
    
    .stepActions {
        flex-direction: column-reverse;
    }
    
    .primaryButton,
    .secondaryButton {
        width: 100%;
    }
}
