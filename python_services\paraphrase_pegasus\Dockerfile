# python_services/paraphrase_pegasus/Dockerfile

# Use an official Python runtime as a parent image
# Using python:3.9 or python:3.10 is generally a good choice for ML projects.
# -slim variant is smaller but might require installing more system dependencies manually.
FROM python:3.9-slim

# Set environment variables for Python
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file into the container at /app
COPY requirements.txt .

# Install system dependencies that might be needed by torch or other libraries (optional, depends on base image)
# RUN apt-get update && apt-get install -y --no-install-recommends \
#     build-essential \
#     # Other dependencies like libgomp1 for some operations
#  && rm -rf /var/lib/apt/lists/*

# Install Python packages specified in requirements.txt
# It's often a good practice to install torch separately if you need a specific version (CPU/GPU),
# or if it has complex dependencies. For simplicity here, we install all at once.
# User might optimize this by, for example, first installing torch with --index-url for CPU if desired:
# RUN pip install --no-cache-dir torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code (app.py) into the container at /app
COPY app.py .
# If you had other local Python modules (e.g., a 'utils' folder), copy them too:
# COPY ./utils ./utils

# Make port (defined by FLASK_PORT, default 5001) available to the world outside this container.
# This doesn't actually publish the port. It functions as a type of documentation
# between the person who builds the image and the person who runs the container.
# The actual port mapping to the host or external world happens during `docker run` or cloud deployment.
EXPOSE 5001

# Define environment variables for Flask and Gunicorn
# These can be overridden at runtime (e.g., by `docker run -e FLASK_PORT=xxxx ...` or cloud platform settings).
ENV FLASK_APP=app.py
ENV FLASK_PORT=5001
# Gunicorn will bind to 0.0.0.0:${FLASK_PORT}

# Command to run the application using Gunicorn (recommended for production)
# --workers: Number of worker processes. A common recommendation is (2 * number of CPU cores) + 1.
#            However, for CPU-bound ML model inference, performance might not scale linearly with many workers
#            due to GIL or model's own parallelism. Start with a small number (e.g., 1-2) and test.
# --threads: Number of threads per worker (for gthread worker class).
# --timeout: Workers silent for more than this many seconds are killed and restarted.
#            Important for long model loading times on startup or long inference times.
# app:app:  Refers to the 'app' Flask application object inside the 'app.py' file.
CMD ["gunicorn", "--bind", "0.0.0.0:${FLASK_PORT}", "--workers", "1", "--threads", "2", "--timeout", "120", "app:app"]

# Alternative for Flask development server (NOT FOR PRODUCTION):
# CMD ["flask", "run", "--host=0.0.0.0", "--port=5001"]
