// netlify/functions/test-detection.js
// AI detection testing function for Netlify

const { checkWithGPTZero } = require('../../src/services/gptzeroClient');

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json',
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    // Only allow POST method
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ message: `Method ${event.httpMethod} Not Allowed` }),
        };
    }

    try {
        const { text } = JSON.parse(event.body);

        if (!text || typeof text !== 'string' || !text.trim()) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ message: 'Input text is required and must be a non-empty string.' }),
            };
        }

        // Call the GPTZero client directly with the provided text
        const detectionResult = await checkWithGPTZero(text);

        if (detectionResult.error) {
            console.warn("GPTZero test-detection error:", detectionResult.message);
            return {
                statusCode: 500,
                headers,
                body: JSON.stringify(detectionResult),
            };
        }

        // If successful, send the full result from checkWithGPTZero
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(detectionResult),
        };

    } catch (error) {
        console.error("Error in /api/test-detection:", error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: true,
                message: error.message || 'An unexpected server error occurred.',
                status: "Server Error",
                score: null
            }),
        };
    }
};
