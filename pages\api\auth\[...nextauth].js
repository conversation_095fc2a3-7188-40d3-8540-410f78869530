import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import { PrismaClient } from '@prisma/client';
import { PrismaAdapter } from '@next-auth/prisma-adapter';

// Instantiate PrismaClient. It's good practice to do this globally,
// rather than in every request, to manage connection pooling efficiently.
const prisma = new PrismaClient();

export const authOptions = {
    // Configure the Prisma Adapter to persist user data, accounts, and sessions to your database.
    adapter: PrismaAdapter(prisma),

    providers: [
        GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
            authorization: { // Recommended for Google to ensure refresh tokens are available
                params: {
                    prompt: "consent",
                    access_type: "offline",
                    response_type: "code"
                }
            }
        }),
        // ...add more providers here if needed
    ],

    secret: process.env.NEXTAUTH_SECRET,

    // Session strategy changed to 'database' to use the Prisma Adapter for session management.
    session: {
        strategy: 'database',
        maxAge: 30 * 24 * 60 * 60, // 30 days (session stored in the database)
        updateAge: 24 * 60 * 60, // 24 hours (how often to update the session in the database)
    },

    callbacks: {
        // The JWT callback is primarily used when the session strategy is 'jwt'.
        // With 'database' strategy, session information is read from the database.
        // However, this callback might still be invoked, and the token object here
        // can be used by NextAuth internally or if you call `getToken()`.
        // We can still store the OAuth access token here if needed for external API calls.
        async jwt({ token, account }) {
            if (account?.access_token) {
                token.accessToken = account.access_token;
            }
            // When using database sessions, user.id is already part of the user object from the DB in the session callback.
            // If you still want it in the token for some reason (e.g. if you call getToken explicitly):
            // if (user?.id) {
            //    token.uid = user.id; // Renaming to avoid conflict with standard 'sub' or 'id' in token
            // }
            return token;
        },

        // The session callback is called when a session is checked.
        // With the 'database' strategy, the `user` object passed here is the user from your database.
        async session({ session, user }) {
            // `session.user` will be enriched with fields from your Prisma `User` model.
            // Ensure `user.id` from the database is assigned to `session.user.id`.
            // Also, add any custom fields from your User model that you want accessible in the client-side session.
            if (user) { // `user` object is directly from your database
                session.user.id = user.id;
                session.user.subscriptionTier = user.subscriptionTier;
                session.user.usageCredits = user.usageCredits;
                // session.user.stripeCustomerId = user.stripeCustomerId; // If needed on client
            }

            // If you stored accessToken in the JWT callback and need it in the session (less common for pure DB sessions)
            // if (token?.accessToken) {
            //    session.accessToken = token.accessToken;
            // }

            return session;
        }
    },

    // Optional: Custom pages for NextAuth.js actions.
    // pages: {
    //   signIn: '/auth/signin',
    //   signOut: '/auth/signout',
    //   error: '/auth/error',
    // },

    debug: process.env.NODE_ENV === 'development',
};

export default NextAuth(authOptions);
