import React from 'react';
import Link from 'next/link';
import styles from './Navbar.module.css';
import AuthButtons from '../auth/AuthButtons'; // Import AuthButtons

const Navbar = () => {
    return (
        <nav className={styles.navbar}>
            <div className={styles.container}>
                <Link href="/" legacyBehavior>
                    <a className={styles.title}>GhostLayer</a>
                </Link>

                {/* This div is a placeholder for actual nav links if added later.
                    If no other links, AuthButtons will be pushed to the right by justify-content: space-between.
                    If there are links, this div will group them.
                */}
                <div className={styles.navLinks}>
                    <Link href="/" legacyBehavior>
                        <a className={styles.navLink}>Home</a>
                    </Link>
                    <Link href="/features" legacyBehavior>
                        <a className={styles.navLink}>Features</a>
                    </Link>
                    <Link href="/pricing" legacyBehavior>
                        <a className={styles.navLink}>Pricing</a>
                    </Link>
                    <Link href="/about" legacyBehavior>
                        <a className={styles.navLink}>About</a>
                    </Link>
                </div>

                {/* Authentication buttons will typically be on the far right */}
                <AuthButtons />
            </div>
        </nav>
    );
};

export default Navbar;
