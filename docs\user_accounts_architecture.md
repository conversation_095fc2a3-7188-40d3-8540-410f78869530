# Architectural Outline: User Accounts, Authentication & Tiering

This document outlines the architectural changes and considerations for implementing user accounts, authentication (specifically Google Sign-In), and a freemium/premium tier system in the AI Text Modifier web application.

## 1. Guest Access (Unauthenticated Users)

*   **Available Features:**
    *   Guests will have access to the core text modification features (e.g., "Smart Paraphrasing" via own algorithms, "Controlled Grammar Mistakes," "Style Optimization").
    *   Access to AI Detection Test (e.g., GPTZero) will also be available.
*   **Limitations for Guests:**
    *   **Usage Limits:** Stricter usage limits will apply (e.g., fewer daily processing requests, smaller text input size per request). These limits could be enforced via IP-based rate limiting or session-based tracking (less robust for guests but simpler).
    *   **Feature Subset:** Access to potentially more advanced or resource-intensive modification models (if integrated in the future) would be restricted.
    *   **No History:** Guests will not have their processing history saved.
    *   **Ads:** Guests may be shown advertisements.
*   **Differentiation:**
    *   The UI will prominently display "Sign In" / "Sign Up" options.
    *   Certain UI elements or feature controls might be disabled or accompanied by prompts to sign up for full access or premium benefits.
    *   API endpoints will check for authentication status before applying premium logic or higher limits.

## 2. User Authentication (Google Sign-In with NextAuth.js)

*   **Technology Choice:**
    *   **NextAuth.js:** Recommended due to its seamless integration with Next.js, extensive provider support (including Google), security features, and flexibility in session management and database integration.
*   **Key Components & Setup:**
    *   **`pages/api/auth/[...nextauth].js`:** This dynamic API route will handle all authentication requests (sign-in, sign-out, session management, OAuth callbacks).
        *   It will be configured with the Google OAuth provider.
        *   Callbacks will be used to customize session data and potentially link to the user database.
    *   **Environment Variables:**
        *   `GOOGLE_CLIENT_ID`: Provided by Google Cloud Console for OAuth.
        *   `GOOGLE_CLIENT_SECRET`: Provided by Google Cloud Console for OAuth.
        *   `NEXTAUTH_URL`: The canonical URL of the application (e.g., `http://localhost:3000` in development, `https://yourdomain.com` in production).
        *   `NEXTAUTH_SECRET`: A secret string used for signing JWTs and other security purposes.
    *   **Frontend Components:**
        *   A "Sign in with Google" button component.
        *   UI elements in the Navbar (or user menu) to display user session status (e.g., user name, profile picture, "Sign Out" button). These will conditionally render based on `useSession()` hook from NextAuth.js.
*   **Session Management:**
    *   **JWT (JSON Web Tokens):** For initial simplicity and statelessness, JWT-based sessions are recommended. NextAuth.js can manage JWTs, encoding user session data within them. The JWT is typically stored in a secure, HttpOnly cookie.
    *   **Database Sessions (Optional Later):** For more complex scenarios or if server-side session revocation is critical, NextAuth.js supports database sessions using an Adapter. This would involve storing session data in the user database.

## 3. User Database Schema

A relational database (e.g., PostgreSQL, MySQL) or a NoSQL database (e.g., MongoDB) can be used. The following is a conceptual SQL-like schema for a `User` table:

```sql
CREATE TABLE "User" (
    "id" TEXT NOT NULL PRIMARY KEY, -- Typically a CUID or UUID
    "name" TEXT,
    "email" TEXT UNIQUE, -- From Google, used for linking
    "emailVerified" TIMESTAMP(3), -- NextAuth.js may populate this
    "image" TEXT, -- Profile picture URL from Google
    "provider" TEXT DEFAULT 'credentials', -- e.g., 'google', 'credentials'
    "providerAccountId" TEXT, -- Google's unique ID for the user

    -- Subscription & Tiering Fields
    "subscriptionTier" TEXT DEFAULT 'free', -- e.g., 'free', 'basic_monthly', 'premium_yearly'
    "stripeCustomerId" TEXT UNIQUE, -- If using Stripe for payments
    "stripeSubscriptionId" TEXT UNIQUE, -- Active subscription ID from Stripe
    "subscriptionEndDate" TIMESTAMP(3), -- For time-limited subscriptions
    "showAds" BOOLEAN DEFAULT TRUE, -- Ad preference

    -- Usage Tracking (can be in a separate table or document)
    "dailyProcessingQuota" INTEGER DEFAULT 10, -- Example: Number of API calls
    "lastQuotaReset" TIMESTAMP(3),
    "currentUsage" INTEGER DEFAULT 0,

    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL
);

-- For NextAuth.js default schema (if using Adapters)
-- Account, Session, VerificationToken tables might also be needed.
-- The User table above is extended for application-specific needs.
CREATE TABLE "Account" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL, -- e.g., 'oauth', 'email'
    "provider" TEXT NOT NULL, -- e.g., 'google'
    "providerAccountId" TEXT NOT NULL, -- User's ID from the provider
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,
    CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE
);
-- CREATE UNIQUE INDEX Account_provider_providerAccountId_key ON Account(provider, providerAccountId);
```

### Next Steps: Database Integration for Full User Persistence & Features

The conceptual schema above outlines the necessary fields for user management and application-specific features. The current authentication setup uses JWTs for session management, which is suitable for stateless authentication but doesn't persist user data beyond what's encoded in the token.

**A. Why a Persistent Database is Essential:**

*   **Long-term User Profiles:** To store user information (name, email, preferences) beyond the session lifetime and independent of the OAuth provider's current data.
*   **Application-Specific Data:** To manage data like `subscriptionTier`, `usageCredits`, API keys for user-specific services, processing history, and other user preferences that are not part of the standard OAuth profile.
*   **Account Linking:** If multiple OAuth providers (e.g., Google, GitHub) or credential-based logins are introduced, a database is crucial for linking these accounts to a single user identity.
*   **Advanced Session Management:** While JWTs are used now, database sessions (managed by NextAuth.js via an Adapter) offer server-side session invalidation and can store more session data if needed.

**B. Role of NextAuth.js Adapters:**

NextAuth.js provides **Adapters** that seamlessly connect it to various databases, simplifying the integration process. Popular choices include:
*   `@next-auth/prisma-adapter` (for use with Prisma ORM)
*   `@next-auth/typeorm-adapter` (for use with TypeORM)
*   Adapters for Supabase, FaunaDB, Firebase, MongoDB, and more.

Using an Adapter allows NextAuth.js to automatically handle:
*   **User Creation:** Creating new user records in your database upon their first successful sign-in via an OAuth provider.
*   **Account Linking:** Associating OAuth provider accounts with your user records.
*   **Session Storage (Optional):** If you switch the session strategy to `'database'`, the adapter will manage storing session information in the database.
*   **Profile Updates:** Potentially updating user profile details (like name or image) if they change at the OAuth provider level (behavior can vary by adapter and configuration).

**C. High-Level Steps for Future Database Integration:**

1.  **Choose a Database:** Select a database system (e.g., PostgreSQL for relational, MongoDB for NoSQL).
2.  **Choose an ORM/Database Client:** Select a tool for interacting with your database (e.g., Prisma, TypeORM, Sequelize, or a specific Node.js driver like `pg` or `mongodb`). Prisma is a modern choice often paired with Next.js.
3.  **Define and Migrate Schema:** Implement the `User`, `Account`, `Session`, and `VerificationToken` schemas as required by NextAuth.js (the adapter documentation usually provides these) along with your application-specific fields like `subscriptionTier`. Use the ORM's migration tools to apply the schema to your database.
4.  **Install and Configure Adapter:** Add the chosen NextAuth.js adapter package to your project (e.g., `npm install @next-auth/prisma-adapter @prisma/client`).
5.  **Update NextAuth.js Configuration:** Modify `src/pages/api/auth/[...nextauth].js` to include the adapter in the `authOptions`. This typically involves instantiating the adapter and passing it your ORM client instance.
    ```javascript
    // Example snippet for [...nextauth].js with Prisma adapter
    // import { PrismaAdapter } from "@next-auth/prisma-adapter";
    // import { PrismaClient } from "@prisma/client";
    // const prisma = new PrismaClient();
    //
    // export const authOptions = {
    //   adapter: PrismaAdapter(prisma),
    //   providers: [...],
    //   // ... other options
    // };
    ```
6.  **Update Application Logic:**
    *   Modify API routes and server-side logic to read from and write to the database for user-specific data (e.g., fetching `subscriptionTier` to gate features, updating `currentUsage`).
    *   Ensure NextAuth.js callbacks (like `session` and `jwt`) are updated if needed to include database-persisted information in the session/JWT. For example, `session.user.id` should now reliably come from your database user ID.

**D. Current State & Deferred Implementation:**

*   The current implementation of user authentication relies on **JWT-based sessions**. User information included in the JWT (like `name`, `email`, `id` from the OAuth provider) is made available through the `useSession` hook via the `jwt` and `session` callbacks in `[...nextauth].js`.
*   This setup does **not** currently persist user data into a dedicated database beyond the default behavior of NextAuth.js with JWTs.
*   The full integration of a database with an ORM and a NextAuth.js Adapter is a more involved task, requiring careful setup of the database, schema migrations, and updates to the application's data access logic. **This full database persistence layer is deferred to a future development phase.** This approach allows for rapid initial development of authentication and core features, with a clear path for scaling user data management.

## 4. Backend API Changes

*   **Authentication Protection:**
    *   API routes (e.g., `/api/process`, `/api/test-detection`, future premium-only routes) will use `getSession({ req })` or `getToken({ req })` from NextAuth.js to retrieve the user's session server-side.
    *   If no session exists or the user is not authenticated, the API can return a 401 Unauthorized error.
*   **Tier-Based Logic:**
    *   Authenticated API routes will fetch the user's details (including `subscriptionTier` and `usageLimits`) from the database, potentially using the `userId` from the session.
    *   The API logic will then adjust its behavior:
        *   **Usage Limits:** Compare current usage against `dailyProcessingQuota`. If exceeded for free users, return an appropriate error (e.g., 429 Too Many Requests) with an upgrade prompt.
        *   **Feature Access:** Enable or disable advanced processing features/models based on `subscriptionTier`.
        *   **Data Logging:** May log request metadata differently for paying users (e.g., for support purposes).

## 5. Freemium vs. Premium Feature Gating

*   **Client-Side (UI):**
    *   The frontend will use the `useSession()` hook from NextAuth.js to get user data, including custom fields like `subscriptionTier` (added to the session via NextAuth.js callbacks).
    *   UI elements for premium features will be conditionally rendered (e.g., shown but disabled with an "Upgrade" tooltip, or hidden entirely).
    *   Display current usage against quota.
    *   Prominently feature "Upgrade to Premium" calls-to-action.
*   **Server-Side (API - Source of Truth):**
    *   As described in "Backend API Changes," the server will be the ultimate gatekeeper. Even if a user manipulates the client-side UI to access a premium feature, the API will block the request if their `subscriptionTier` does not permit it.
*   **Potential Premium Features:**
    *   Higher daily/monthly processing limits.
    *   Larger input text capacity.
    *   Access to more advanced (or computationally expensive) AI models for text modification (hypothetical future feature).
    *   Priority processing queues.
    *   Ad-free experience.
    *   Saved history of processed texts.
    *   Team features (if applicable in the future).

## 6. Ad Preference Management (Conceptual)

*   If ads are part of the monetization strategy for the free tier:
    *   The `User` schema includes a `showAds: boolean` field.
    *   This field would default to `true` for `free` tier users and be set to `false` for `premium` tier users.
    *   The frontend would check this flag (available in the session) to decide whether to render ad components.

## Summary of New Architectural Components:

*   **NextAuth.js library.**
*   **`pages/api/auth/[...nextauth].js` API route.**
*   **User Database** (e.g., PostgreSQL) with schema for Users, Accounts (for NextAuth), and potentially sessions/subscriptions.
*   **Environment variables** for OAuth credentials and NextAuth.js configuration.
*   **Frontend components** for sign-in, sign-out, and displaying user session state.
*   **Modified API routes** to check authentication and user tier.
*   **Client-side logic** to adapt UI based on authentication and tier.
*   (Future) **Payment Gateway Integration** (e.g., Stripe) for managing subscriptions (not detailed in this outline but implied by premium tiers).

This outline provides a high-level framework. Each point involves significant implementation details and further design decisions.
