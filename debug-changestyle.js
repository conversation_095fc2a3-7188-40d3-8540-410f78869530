// Debug the changeStyle function specifically
import { changeStyle } from './src/utils/textModifiers.js';

const testText = `This is the first paragraph.

This is the second paragraph.

This is the third paragraph.`;

console.log('🔍 Debugging changeStyle function...\n');

console.log('📝 Input text:');
console.log(JSON.stringify(testText));
console.log('\nInput paragraphs:', testText.split(/\n\s*\n/).length);

const result = changeStyle(testText);

console.log('\n🔄 Output text:');
console.log(JSON.stringify(result));
console.log('\nOutput paragraphs:', result.split(/\n\s*\n/).length);

console.log('\n📊 Character-by-character comparison:');
for (let i = 0; i < Math.max(testText.length, result.length); i++) {
    const inputChar = testText[i] || 'END';
    const outputChar = result[i] || 'END';
    
    if (inputChar !== outputChar) {
        console.log(`Difference at position ${i}:`);
        console.log(`  Input:  "${inputChar}" (${inputChar.charCodeAt ? inputChar.charCodeAt(0) : 'N/A'})`);
        console.log(`  Output: "${outputChar}" (${outputChar.charCodeAt ? outputChar.charCodeAt(0) : 'N/A'})`);
        
        // Show context
        const start = Math.max(0, i - 10);
        const end = Math.min(testText.length, i + 10);
        console.log(`  Context input:  "${testText.substring(start, end)}"`);
        console.log(`  Context output: "${result.substring(start, end)}"`);
        break;
    }
}
