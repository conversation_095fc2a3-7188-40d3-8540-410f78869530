/* Style Controls Component Styles */

.container {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
}

.createButton {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.createButton:hover {
    background: #2563eb;
}

.loading {
    text-align: center;
    color: #6b7280;
    padding: 20px;
    font-style: italic;
}

.error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
}

.noProfiles {
    text-align: center;
    padding: 32px 20px;
    color: #6b7280;
}

.noProfiles p {
    margin-bottom: 16px;
}

.primaryButton {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s ease;
}

.primaryButton:hover {
    background: #2563eb;
}

.profileSelector {
    margin-bottom: 16px;
}

.profileSelector label {
    display: block;
    margin-bottom: 6px;
    color: #374151;
    font-weight: 600;
    font-size: 14px;
}

.select {
    width: 100%;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    background: white;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.profileInfo {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.profileDetails h4 {
    margin: 0 0 8px 0;
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
}

.description {
    color: #6b7280;
    font-size: 14px;
    margin: 0 0 12px 0;
    line-height: 1.5;
}

.profileStats {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: #6b7280;
}

.profileStats span {
    background: #e5e7eb;
    padding: 4px 8px;
    border-radius: 4px;
}

.strengthControl {
    margin-bottom: 20px;
}

.strengthHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.strengthHeader label {
    color: #374151;
    font-weight: 600;
    font-size: 14px;
}

.strengthValue {
    font-weight: 700;
    margin-left: 8px;
}

.strengthDescription {
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

.sliderContainer {
    position: relative;
}

.slider {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    border: 2px solid #3b82f6;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    border: 2px solid #3b82f6;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.slider:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.slider:disabled::-webkit-slider-thumb {
    cursor: not-allowed;
    border-color: #9ca3af;
}

.slider:disabled::-moz-range-thumb {
    cursor: not-allowed;
    border-color: #9ca3af;
}

.sliderLabels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #6b7280;
}

.disabledNote {
    text-align: center;
    color: #9ca3af;
    font-size: 12px;
    font-style: italic;
    margin-top: 8px;
}

.stylePreview {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 16px;
}

.stylePreview h5 {
    margin: 0 0 12px 0;
    color: #0c4a6e;
    font-size: 14px;
    font-weight: 600;
}

.characteristics {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.characteristic {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
}

.label {
    color: #374151;
    font-weight: 500;
    min-width: 120px;
}

.value {
    color: #0c4a6e;
    font-weight: 600;
    text-align: right;
    flex: 1;
}

@media (max-width: 768px) {
    .container {
        padding: 16px;
    }
    
    .header {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .createButton {
        width: 100%;
        justify-content: center;
    }
    
    .strengthHeader {
        flex-direction: column;
        align-items: stretch;
        gap: 4px;
    }
    
    .strengthDescription {
        text-align: center;
    }
    
    .profileStats {
        flex-direction: column;
        gap: 8px;
    }
    
    .characteristic {
        flex-direction: column;
        align-items: stretch;
        gap: 4px;
    }
    
    .label {
        min-width: auto;
    }
    
    .value {
        text-align: left;
    }
}
