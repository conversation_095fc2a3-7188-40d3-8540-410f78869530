.footer {
    background-color: #f8f9fa;
    color: #6c757d;
    padding: 2rem 0; /* Increased padding for better spacing */
    text-align: center;
    border-top: 1px solid #e7e7e7;
    margin-top: 50px; /* Increased margin for more breathing room */
    font-family: inherit; /* Inherit global font */
    font-size: 0.9rem; /* Slightly smaller font for footer */
}

.container {
    max-width: 800px; /* Match Home.module.css container */
    margin: 0 auto;
    padding: 0 1rem; /* Consistent padding */
}

.footer p {
    margin: 0; /* Remove default paragraph margin if any */
}

/* Optional: if you want to add links in the footer */
.footerLinks {
    margin-top: 10px;
}

.footerLinks a {
    color: #0070f3;
    text-decoration: none;
    margin: 0 10px;
    transition: color 0.2s ease-in-out;
}

.footerLinks a:hover {
    color: #005bb5;
    text-decoration: underline;
}
