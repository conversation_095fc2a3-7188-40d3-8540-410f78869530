.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem 1rem;
    min-height: calc(100vh - 200px);
}

.header {
    text-align: center;
    margin-bottom: 4rem;
}

.title {
    font-size: 3rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #0ea5e9, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.missionSection,
.teamSection,
.contactSection {
    margin-bottom: 4rem;
}

.valuesSection {
    margin-bottom: 4rem;
}

.content {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.sectionTitle {
    font-size: 2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: center;
}

.text {
    color: #374151;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.valuesGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.valueCard {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.valueCard:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.valueIcon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.valueTitle {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.valueDescription {
    color: #6b7280;
    line-height: 1.6;
}

.teamContent {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.contactSection {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border-radius: 1rem;
    padding: 3rem 2rem;
    border: 1px solid #bae6fd;
    text-align: center;
}

.contactInfo {
    margin-top: 2rem;
}

.contactItem {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: #374151;
}

.contactItem a {
    color: #0ea5e9;
    text-decoration: none;
    margin-left: 0.5rem;
}

.contactItem a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .content,
    .teamContent {
        padding: 1.5rem;
    }
    
    .valuesGrid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .valueCard {
        padding: 1.5rem;
    }
    
    .contactSection {
        padding: 2rem 1rem;
    }
    
    .sectionTitle {
        font-size: 1.5rem;
    }
    
    .text {
        font-size: 1rem;
    }
}
