// Test individual functions to isolate the paragraph formatting issue
import { balancedHumanization } from './src/utils/balancedHumanizer.js';
import { changeStyle, simpleParaphrase, addControlledMistakes } from './src/utils/textModifiers.js';
import { paraphraseWithPegasus } from './src/services/paraphraseService.js';

const testText = `This is the first paragraph of the test. It contains multiple sentences to verify that the humanization process works correctly.

This is the second paragraph. Additionally, it shows how the system should handle paragraph breaks.

This is the third and final paragraph. It should remain separate from the others.`;

function countParagraphs(text) {
    return text.split(/\n\s*\n/).length;
}

async function testIndividualFunctions() {
    console.log('🧪 Testing Individual Functions for Paragraph Preservation...\n');
    
    const originalParagraphs = countParagraphs(testText);
    console.log(`📝 Original text has ${originalParagraphs} paragraphs\n`);
    
    let currentText = testText;
    
    // Test 1: balancedHumanization
    console.log('1️⃣ Testing balancedHumanization...');
    try {
        const result1 = balancedHumanization(currentText);
        const paragraphs1 = countParagraphs(result1);
        console.log(`   Result: ${paragraphs1} paragraphs ${paragraphs1 === originalParagraphs ? '✅' : '❌'}`);
        if (paragraphs1 !== originalParagraphs) {
            console.log('   ⚠️  balancedHumanization is flattening paragraphs!');
            console.log('   First 200 chars:', result1.substring(0, 200) + '...');
        }
        currentText = result1;
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
    }
    
    // Test 2: changeStyle
    console.log('\n2️⃣ Testing changeStyle...');
    try {
        const result2 = changeStyle(currentText);
        const paragraphs2 = countParagraphs(result2);
        console.log(`   Result: ${paragraphs2} paragraphs ${paragraphs2 === originalParagraphs ? '✅' : '❌'}`);
        if (paragraphs2 !== originalParagraphs) {
            console.log('   ⚠️  changeStyle is flattening paragraphs!');
            console.log('   First 200 chars:', result2.substring(0, 200) + '...');
        }
        currentText = result2;
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
    }
    
    // Test 3: addControlledMistakes
    console.log('\n3️⃣ Testing addControlledMistakes...');
    try {
        const result3 = addControlledMistakes(currentText);
        const paragraphs3 = countParagraphs(result3);
        console.log(`   Result: ${paragraphs3} paragraphs ${paragraphs3 === originalParagraphs ? '✅' : '❌'}`);
        if (paragraphs3 !== originalParagraphs) {
            console.log('   ⚠️  addControlledMistakes is flattening paragraphs!');
            console.log('   First 200 chars:', result3.substring(0, 200) + '...');
        }
        currentText = result3;
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
    }
    
    // Test 4: simpleParaphrase
    console.log('\n4️⃣ Testing simpleParaphrase...');
    try {
        const result4 = await simpleParaphrase(currentText);
        const paragraphs4 = countParagraphs(result4);
        console.log(`   Result: ${paragraphs4} paragraphs ${paragraphs4 === originalParagraphs ? '✅' : '❌'}`);
        if (paragraphs4 !== originalParagraphs) {
            console.log('   ⚠️  simpleParaphrase is flattening paragraphs!');
            console.log('   First 200 chars:', result4.substring(0, 200) + '...');
        }
        currentText = result4;
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
    }
    
    // Test 5: paraphraseWithPegasus (local fallback)
    console.log('\n5️⃣ Testing paraphraseWithPegasus (local fallback)...');
    try {
        const result5 = await paraphraseWithPegasus(testText); // Use original text
        if (result5 && result5.paraphrased_text) {
            const paragraphs5 = countParagraphs(result5.paraphrased_text);
            console.log(`   Result: ${paragraphs5} paragraphs ${paragraphs5 === originalParagraphs ? '✅' : '❌'}`);
            if (paragraphs5 !== originalParagraphs) {
                console.log('   ⚠️  paraphraseWithPegasus is flattening paragraphs!');
                console.log('   First 200 chars:', result5.paraphrased_text.substring(0, 200) + '...');
            }
        } else {
            console.log('   ⚠️  paraphraseWithPegasus returned no result or error');
        }
    } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
    }
    
    console.log('\n📊 Final Analysis:');
    console.log(`Final text has ${countParagraphs(currentText)} paragraphs`);
    console.log('Final text preview:');
    console.log(currentText.substring(0, 300) + '...');
}

testIndividualFunctions();
