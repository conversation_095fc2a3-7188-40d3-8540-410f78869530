/**
 * Simple Falcon Integration Test
 * Tests basic functionality without complex dependencies
 */

// Test the Falcon service directly
async function testFalconService() {
    console.log('🦅 Testing Falcon Service Integration...\n');
    
    try {
        // Import the Falcon service
        const { isAdvancedLLMAvailable, getProviderStatus } = await import('./src/services/falconService.js');
        
        // Test 1: Check availability
        console.log('1. Testing model availability...');
        const isAvailable = isAdvancedLLMAvailable();
        console.log(`   ✅ Advanced LLM Service Available: ${isAvailable}`);
        
        // Test 2: Check provider status
        console.log('\n2. Testing provider status...');
        const providerStatus = getProviderStatus();
        console.log('   📊 Provider Status:');
        Object.entries(providerStatus).forEach(([model, providers]) => {
            console.log(`\n     ${model}:`);
            providers.forEach(provider => {
                const status = provider.available ? '✅' : '❌';
                console.log(`       ${status} ${provider.name}: ${provider.model}`);
            });
        });
        
        return true;
    } catch (error) {
        console.error('❌ Error testing Falcon service:', error.message);
        return false;
    }
}

// Test the advanced humanizer integration
async function testAdvancedHumanizer() {
    console.log('\n🧠 Testing Advanced Humanizer Integration...\n');
    
    try {
        // Import the advanced humanizer
        const { advancedHumanization } = await import('./src/utils/advancedHumanizer.js');
        
        const testText = "Furthermore, it is important to note that artificial intelligence has significantly transformed numerous industries. Moreover, the implementation of machine learning algorithms has consequently led to substantial improvements in efficiency.";
        
        console.log('Testing with sample AI-generated text...');
        console.log(`Input: ${testText.substring(0, 100)}...`);
        
        // Test with Falcon prioritization
        const result = await advancedHumanization(testText, {
            aggressiveness: 0.7,
            maintainTone: true,
            targetDetection: 10,
            useModelBased: true,
            fallbackToPatterns: true
        });
        
        if (result.success) {
            console.log('✅ Advanced humanization successful!');
            console.log(`   Method: ${result.method}`);
            console.log(`   Model: ${result.model || 'N/A'}`);
            console.log(`   Provider: ${result.provider || 'N/A'}`);
            console.log(`   Processing time: ${result.processingTime || 'N/A'}ms`);
            console.log(`   Original length: ${result.originalLength}`);
            console.log(`   New length: ${result.newLength}`);
            console.log(`   Detection target: ≤${result.detectionTarget || 10}%`);
            
            // Show a preview of the result
            const preview = result.text.substring(0, 150) + '...';
            console.log(`   Preview: ${preview}`);
        } else {
            console.log('❌ Advanced humanization failed');
            console.log(`   Error: ${result.error || 'Unknown error'}`);
        }
        
        return result.success;
    } catch (error) {
        console.error('❌ Error testing advanced humanizer:', error.message);
        return false;
    }
}

// Test the humanization service
async function testHumanizationService() {
    console.log('\n🔗 Testing Humanization Service Integration...\n');
    
    try {
        // Import the humanization service
        const { humanizeText } = await import('./src/services/humaneyesService.js');
        
        const testText = "The implementation of artificial intelligence systems requires comprehensive evaluation of technological infrastructure to ensure optimal performance and efficiency.";
        
        console.log('Testing humanization service with Falcon priority...');
        console.log(`Input: ${testText.substring(0, 100)}...`);
        
        // Test with auto method (should prioritize Falcon)
        const result = await humanizeText(testText, {
            aggressiveness: 0.7,
            maintainTone: true,
            targetDetection: 10,
            method: 'auto',
            fallbackEnabled: true
        });
        
        if (result.success) {
            console.log('✅ Humanization service successful!');
            console.log(`   Requested method: auto`);
            console.log(`   Actual method: ${result.actualMethod || result.method}`);
            console.log(`   Model: ${result.modelName || result.model || 'N/A'}`);
            console.log(`   Provider: ${result.provider || 'N/A'}`);
            console.log(`   Total processing time: ${result.totalProcessingTime || result.processingTime || 'N/A'}ms`);
            console.log(`   Detection target: ≤${result.detectionTarget || 10}%`);
            
            // Show a preview of the result
            const outputText = result.text || result.humanizedText || '';
            const preview = outputText.substring(0, 150) + '...';
            console.log(`   Preview: ${preview}`);
        } else {
            console.log('❌ Humanization service failed');
            console.log(`   Error: ${result.error || 'Unknown error'}`);
        }
        
        return result.success;
    } catch (error) {
        console.error('❌ Error testing humanization service:', error.message);
        return false;
    }
}

// Test configuration validation
async function testConfiguration() {
    console.log('\n⚙️ Testing Configuration...\n');
    
    try {
        // Check environment variables
        const envVars = [
            'HUGGINGFACE_API_TOKEN',
            'FIREWORKS_API_KEY',
            'OPENROUTER_API_KEY',
            'GROQ_API_KEY'
        ];
        
        console.log('Checking API key configuration:');
        envVars.forEach(envVar => {
            const isConfigured = !!process.env[envVar];
            const status = isConfigured ? '✅' : '❌';
            console.log(`   ${status} ${envVar}: ${isConfigured ? 'Configured' : 'Not configured'}`);
        });
        
        // Check if at least one key is configured
        const hasAnyKey = envVars.some(envVar => !!process.env[envVar]);
        
        if (!hasAnyKey) {
            console.log('\n⚠️  Warning: No API keys configured. Tests will use fallback methods.');
            console.log('   To test Falcon models, configure at least one of the following:');
            console.log('   - HUGGINGFACE_API_TOKEN (recommended for Falcon models)');
            console.log('   - FIREWORKS_API_KEY (for enhanced performance)');
            console.log('   - OPENROUTER_API_KEY (for alternative access)');
        }
        
        return hasAnyKey;
    } catch (error) {
        console.error('❌ Error testing configuration:', error.message);
        return false;
    }
}

// Main test runner
async function runSimpleFalconTest() {
    console.log('🦅 FALCON INTEGRATION - SIMPLE TEST SUITE');
    console.log('==========================================\n');
    
    const results = {
        configuration: false,
        falconService: false,
        advancedHumanizer: false,
        humanizationService: false
    };
    
    try {
        // Test 1: Configuration
        results.configuration = await testConfiguration();
        
        // Test 2: Falcon Service
        results.falconService = await testFalconService();
        
        // Test 3: Advanced Humanizer
        results.advancedHumanizer = await testAdvancedHumanizer();
        
        // Test 4: Humanization Service
        results.humanizationService = await testHumanizationService();
        
        // Summary
        console.log('\n📋 TEST SUMMARY');
        console.log('================');
        Object.entries(results).forEach(([test, passed]) => {
            const status = passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${test}`);
        });
        
        const passedTests = Object.values(results).filter(Boolean).length;
        const totalTests = Object.keys(results).length;
        
        console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
        
        if (passedTests === totalTests) {
            console.log('🎉 All tests passed! Falcon integration is working correctly.');
        } else if (passedTests > 0) {
            console.log('⚠️  Some tests passed. Falcon integration is partially working.');
            console.log('   Check API key configuration and network connectivity.');
        } else {
            console.log('❌ All tests failed. Please check the integration setup.');
        }
        
        console.log('\n📖 Next Steps:');
        console.log('   1. Configure API keys for better performance');
        console.log('   2. Test with actual content in the web interface');
        console.log('   3. Monitor AI detection scores in production');
        
    } catch (error) {
        console.error('\n❌ Test suite error:', error);
    }
}

// Run the tests
runSimpleFalconTest().catch(console.error);
