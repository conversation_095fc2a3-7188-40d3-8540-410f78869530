# Database Configuration for GhostLayer Production

## 🗄️ Recommended Database Options

### 1. Vercel Postgres (Recommended)
**Best for Vercel deployments**
- **Free Tier**: 60 hours compute time, 256MB storage
- **Pricing**: $20/month for hobby tier (unlimited compute)
- **Benefits**: 
  - Zero configuration with Vercel
  - Automatic connection pooling
  - Built-in monitoring
  - Serverless-optimized

**Setup Steps:**
1. Go to Vercel Dashboard → Storage → Create Database
2. Select "Postgres"
3. Choose your region (same as your app)
4. Copy the connection string
5. Add to environment variables as `DATABASE_URL`

### 2. Supabase (Great Alternative)
**Free tier with generous limits**
- **Free Tier**: 500MB database, 2GB bandwidth
- **Pricing**: $25/month for pro tier
- **Benefits**:
  - Real-time subscriptions
  - Built-in auth (can replace NextAuth if needed)
  - REST API auto-generation
  - Dashboard for data management

**Setup Steps:**
1. Sign up at supabase.com
2. Create new project
3. Go to Settings → Database
4. Copy connection string (use "Connection pooling" URL)
5. Add to environment variables as `DATABASE_URL`

### 3. PlanetScale (MySQL Alternative)
**Serverless MySQL platform**
- **Free Tier**: 1 database, 1GB storage, 1 billion row reads
- **Pricing**: $29/month for scaler tier
- **Benefits**:
  - Branching (like Git for databases)
  - No migration downtime
  - Automatic scaling
  - Built-in connection pooling

**Setup Steps:**
1. Sign up at planetscale.com
2. Create database
3. Create branch (main)
4. Get connection string
5. Add to environment variables as `DATABASE_URL`

## 🔧 Production Configuration

### Environment Variables
```bash
# Primary database connection
DATABASE_URL="****************************************/database?schema=public&connection_limit=5&pool_timeout=20"

# Optional: Direct URL for migrations (some providers require this)
DIRECT_URL="****************************************/database?schema=public"

# Shadow database for migrations (development)
SHADOW_DATABASE_URL="****************************************/shadow_db?schema=public"
```

### Connection Pool Settings
```javascript
// lib/prisma.js - Singleton pattern for serverless
import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis

export const prisma = globalForPrisma.prisma || new PrismaClient({
  log: ['query', 'error', 'warn'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma
```

## 🚀 Migration Strategy

### Development to Production Migration
```bash
# 1. Generate Prisma client
npx prisma generate

# 2. Create and apply migrations
npx prisma migrate dev --name init

# 3. Deploy migrations to production
npx prisma migrate deploy

# 4. Seed production database (optional)
npx prisma db seed
```

### Vercel Deployment Migration
```bash
# Add to package.json scripts
"vercel-build": "prisma generate && prisma migrate deploy && next build"

# Or use Vercel build command
"build": "prisma generate && next build"
```

## 📊 Database Schema Optimization

### Indexes for Performance
```prisma
model User {
  // ... existing fields
  
  // Add indexes for common queries
  @@index([email])
  @@index([stripeCustomerId])
  @@index([subscriptionTier])
  @@index([createdAt])
}

model Subscription {
  // ... existing fields
  
  // Optimize subscription queries
  @@index([userId])
  @@index([status])
  @@index([stripeCurrentPeriodEnd])
  @@index([userId, status])
}

model StyleProfile {
  // ... existing fields
  
  // Optimize style profile queries
  @@index([userId])
  @@index([userId, isActive])
  @@index([lastUsedAt])
}
```

### Connection Pooling Configuration
```prisma
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  // Optimize for serverless
  relationMode = "prisma"
}
```

## 🔐 Security Configuration

### Database Security
```bash
# Use connection pooling URLs when available
DATABASE_URL="postgresql://username:<EMAIL>:5432/database?sslmode=require&pgbouncer=true"

# Enable SSL for production
DATABASE_URL="****************************************/database?sslmode=require"

# Connection limits for serverless
DATABASE_URL="****************************************/database?connection_limit=5&pool_timeout=20"
```

### Access Control
```sql
-- Create read-only user for analytics (optional)
CREATE USER analytics_user WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE ghostlayer TO analytics_user;
GRANT USAGE ON SCHEMA public TO analytics_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO analytics_user;
```

## 📈 Monitoring & Maintenance

### Database Monitoring
```javascript
// Add to your API routes for monitoring
import { prisma } from '../../../lib/prisma'

export default async function handler(req, res) {
  try {
    // Monitor connection health
    await prisma.$queryRaw`SELECT 1`
    
    // Your API logic here
    
  } catch (error) {
    console.error('Database connection error:', error)
    res.status(500).json({ error: 'Database unavailable' })
  }
}
```

### Performance Monitoring
```javascript
// Monitor query performance
const startTime = Date.now()
const result = await prisma.user.findMany()
const queryTime = Date.now() - startTime
console.log(`Query executed in ${queryTime}ms`)
```

## 🔄 Backup Strategy

### Automated Backups
Most managed database providers offer automated backups:

**Vercel Postgres:**
- Automatic daily backups
- Point-in-time recovery
- Managed through Vercel dashboard

**Supabase:**
- Daily backups on free tier
- Point-in-time recovery on paid tiers
- Manual backup exports available

**PlanetScale:**
- Automatic backups
- Branch-based recovery
- Data export tools

### Manual Backup
```bash
# Export database schema
npx prisma db pull

# Export data (if needed)
pg_dump $DATABASE_URL > backup.sql
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Connection Pool Exhaustion
```bash
# Error: "Too many connections"
# Solution: Reduce connection_limit in DATABASE_URL
DATABASE_URL="...?connection_limit=3&pool_timeout=20"
```

#### 2. Migration Failures
```bash
# Reset database (development only)
npx prisma migrate reset

# Force migration (use carefully)
npx prisma db push --force-reset
```

#### 3. Serverless Timeouts
```javascript
// Implement connection timeout
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  // Add timeout configuration
  __internal: {
    engine: {
      connectTimeout: 20000,
      queryTimeout: 20000,
    },
  },
})
```

## 📋 Production Checklist

### Pre-Deployment
- [ ] Database provider selected and configured
- [ ] Connection string added to environment variables
- [ ] Prisma schema optimized with indexes
- [ ] Migration files created and tested
- [ ] Connection pooling configured
- [ ] SSL enabled for production

### Post-Deployment
- [ ] Run `prisma migrate deploy`
- [ ] Verify database connection
- [ ] Test all CRUD operations
- [ ] Monitor connection pool usage
- [ ] Set up automated backups
- [ ] Configure monitoring alerts

### Performance Optimization
- [ ] Add database indexes for common queries
- [ ] Implement query result caching
- [ ] Monitor slow queries
- [ ] Optimize N+1 query problems
- [ ] Use connection pooling
- [ ] Implement proper error handling

## 💰 Cost Optimization

### Free Tier Limits
- **Vercel Postgres**: 60 hours compute/month
- **Supabase**: 500MB storage, 2GB bandwidth
- **PlanetScale**: 1GB storage, 1B row reads

### Optimization Tips
1. Use connection pooling to reduce connection overhead
2. Implement query result caching
3. Add proper database indexes
4. Monitor and optimize slow queries
5. Use read replicas for analytics (if available)
6. Implement data archiving for old records
