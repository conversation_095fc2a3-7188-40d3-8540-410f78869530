/**
 * Comprehensive Test Suite for Falcon Model Integration
 * Tests the new Falcon-enhanced humanization system for ≤10% AI detection
 */

import { humanizeWithAdvancedLLM, isAdvancedLLMAvailable, getProviderStatus } from './src/services/falconService.js';
import { advancedHumanization } from './src/utils/advancedHumanizer.js';
import { humanizeText } from './src/services/humaneyesService.js';
import { humanizeTextWithHF } from './src/services/huggingFaceClient.js';

// Test content samples with varying AI detection likelihood
const testSamples = {
    highAI: `
        Furthermore, it is important to note that artificial intelligence has significantly transformed numerous industries. 
        Moreover, the implementation of machine learning algorithms has consequently led to substantial improvements in efficiency. 
        Therefore, organizations must comprehensively evaluate their technological infrastructure to ensure optimal performance.
    `,
    
    mediumAI: `
        AI technology is changing how businesses operate today. Companies are using machine learning to improve their processes 
        and make better decisions. This transformation is happening across many different sectors, from healthcare to finance.
    `,
    
    lowAI: `
        I've been thinking about how AI is really shaking things up in business lately. It's pretty amazing to see companies 
        using these smart algorithms to get better results. The whole thing is happening everywhere - hospitals, banks, you name it.
    `,
    
    technical: `
        The implementation of a RESTful API architecture requires careful consideration of endpoint design, authentication mechanisms, 
        and data serialization formats. Developers must ensure proper error handling and implement rate limiting to maintain system stability.
    `
};

/**
 * Test Falcon model availability and configuration
 */
async function testFalconAvailability() {
    console.log('\n🦅 Testing Falcon Model Availability...\n');
    
    try {
        const isAvailable = isAdvancedLLMAvailable();
        console.log(`✅ Advanced LLM Service Available: ${isAvailable}`);
        
        const providerStatus = getProviderStatus();
        console.log('\n📊 Provider Status:');
        Object.entries(providerStatus).forEach(([model, providers]) => {
            console.log(`\n  ${model}:`);
            providers.forEach(provider => {
                const status = provider.available ? '✅' : '❌';
                console.log(`    ${status} ${provider.name}: ${provider.model}`);
            });
        });
        
        return isAvailable;
    } catch (error) {
        console.error('❌ Error testing Falcon availability:', error.message);
        return false;
    }
}

/**
 * Test Falcon-enhanced humanization with different aggressiveness levels
 */
async function testFalconHumanization() {
    console.log('\n🔬 Testing Falcon-Enhanced Humanization...\n');
    
    const testConfigs = [
        { aggressiveness: 0.5, targetDetection: 10, label: 'Conservative (≤10%)' },
        { aggressiveness: 0.7, targetDetection: 10, label: 'Moderate (≤10%)' },
        { aggressiveness: 0.9, targetDetection: 10, label: 'Aggressive (≤10%)' }
    ];
    
    for (const config of testConfigs) {
        console.log(`\n--- Testing ${config.label} ---`);
        
        try {
            const result = await humanizeWithAdvancedLLM(testSamples.highAI, {
                aggressiveness: config.aggressiveness,
                maintainTone: true,
                targetDetection: config.targetDetection,
                preferredModel: 'falcon-3-7b'
            });
            
            if (result.success) {
                console.log(`✅ Success with ${result.modelName} (${result.provider})`);
                console.log(`⏱️  Processing time: ${result.processingTime}ms`);
                console.log(`📝 Original length: ${testSamples.highAI.length} chars`);
                console.log(`📝 Humanized length: ${result.text.length} chars`);
                console.log(`🎯 Target detection: ≤${config.targetDetection}%`);
                
                // Show first 100 characters of result
                const preview = result.text.substring(0, 100) + '...';
                console.log(`📄 Preview: ${preview}`);
            } else {
                console.log(`❌ Failed: ${result.error}`);
            }
        } catch (error) {
            console.log(`❌ Error: ${error.message}`);
        }
    }
}

/**
 * Test advanced humanizer with Falcon prioritization
 */
async function testAdvancedHumanizerFalcon() {
    console.log('\n🧠 Testing Advanced Humanizer with Falcon Priority...\n');
    
    const testCases = [
        { sample: testSamples.highAI, label: 'High AI Content' },
        { sample: testSamples.mediumAI, label: 'Medium AI Content' },
        { sample: testSamples.technical, label: 'Technical Content' }
    ];
    
    for (const testCase of testCases) {
        console.log(`\n--- Testing ${testCase.label} ---`);
        
        try {
            const result = await advancedHumanization(testCase.sample, {
                aggressiveness: 0.7,
                maintainTone: true,
                targetDetection: 10,
                useModelBased: true,
                fallbackToPatterns: true
            });
            
            if (result.success) {
                console.log(`✅ Success with method: ${result.method}`);
                console.log(`🤖 Model: ${result.model || 'N/A'}`);
                console.log(`🏢 Provider: ${result.provider || 'N/A'}`);
                console.log(`⏱️  Processing time: ${result.processingTime || 'N/A'}ms`);
                console.log(`🎯 Detection target: ≤${result.detectionTarget || 10}%`);
                
                // Calculate transformation ratio
                const transformationRatio = ((result.newLength - result.originalLength) / result.originalLength * 100).toFixed(1);
                console.log(`📊 Length change: ${transformationRatio}%`);
            } else {
                console.log(`❌ Failed: ${result.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.log(`❌ Error: ${error.message}`);
        }
    }
}

/**
 * Test HuggingFace client with Falcon models
 */
async function testHuggingFaceFalcon() {
    console.log('\n🤗 Testing HuggingFace Client with Falcon Models...\n');
    
    try {
        const result = await humanizeTextWithHF(testSamples.mediumAI, {
            aggressiveness: 0.7,
            maintainTone: true,
            targetDetection: 10
        });
        
        if (result.success) {
            console.log(`✅ HuggingFace Success`);
            console.log(`🤖 Model used: ${result.model}`);
            console.log(`🏢 Provider: ${result.provider}`);
            console.log(`⏱️  Processing time: ${result.processingTime}ms`);
            console.log(`📊 Session ID: ${result.sessionId}`);
            
            // Show usage stats if available
            if (result.usage) {
                console.log(`📈 Token usage:`, result.usage);
            }
        } else {
            console.log(`❌ HuggingFace Failed: ${result.error}`);
            console.log(`🔄 Attempts made: ${result.attempts || 0}`);
        }
    } catch (error) {
        console.log(`❌ Error: ${error.message}`);
    }
}

/**
 * Test complete humanization service integration
 */
async function testCompleteIntegration() {
    console.log('\n🔗 Testing Complete Service Integration...\n');
    
    const methods = ['auto', 'llm', 'pattern'];
    
    for (const method of methods) {
        console.log(`\n--- Testing method: ${method} ---`);
        
        try {
            const result = await humanizeText(testSamples.highAI, {
                aggressiveness: 0.7,
                maintainTone: true,
                targetDetection: 10,
                method: method,
                fallbackEnabled: true
            });
            
            if (result.success) {
                console.log(`✅ Success with ${method} method`);
                console.log(`🔧 Actual method: ${result.actualMethod || result.method}`);
                console.log(`⏱️  Total processing time: ${result.totalProcessingTime}ms`);
                console.log(`🎯 Detection target: ≤${result.detectionTarget || 10}%`);
                
                if (result.modelName) {
                    console.log(`🤖 Model: ${result.modelName}`);
                }
                if (result.provider) {
                    console.log(`🏢 Provider: ${result.provider}`);
                }
            } else {
                console.log(`❌ Failed with ${method}: ${result.error}`);
            }
        } catch (error) {
            console.log(`❌ Error with ${method}: ${error.message}`);
        }
    }
}

/**
 * Performance benchmark test
 */
async function benchmarkPerformance() {
    console.log('\n⚡ Performance Benchmark...\n');
    
    const iterations = 3;
    const testText = testSamples.mediumAI;
    const times = [];
    
    for (let i = 0; i < iterations; i++) {
        console.log(`Running iteration ${i + 1}/${iterations}...`);
        
        try {
            const startTime = Date.now();
            const result = await humanizeText(testText, {
                aggressiveness: 0.7,
                targetDetection: 10,
                method: 'auto'
            });
            const endTime = Date.now();
            
            if (result.success) {
                const totalTime = endTime - startTime;
                times.push(totalTime);
                console.log(`✅ Iteration ${i + 1}: ${totalTime}ms`);
            } else {
                console.log(`❌ Iteration ${i + 1} failed`);
            }
        } catch (error) {
            console.log(`❌ Iteration ${i + 1} error: ${error.message}`);
        }
    }
    
    if (times.length > 0) {
        const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
        const minTime = Math.min(...times);
        const maxTime = Math.max(...times);
        
        console.log(`\n📊 Performance Summary:`);
        console.log(`   Average: ${avgTime.toFixed(0)}ms`);
        console.log(`   Minimum: ${minTime}ms`);
        console.log(`   Maximum: ${maxTime}ms`);
    }
}

/**
 * Main test runner
 */
async function runFalconIntegrationTests() {
    console.log('🦅 FALCON MODEL INTEGRATION TEST SUITE');
    console.log('=====================================');
    
    try {
        // Test 1: Check availability
        const isAvailable = await testFalconAvailability();
        
        if (!isAvailable) {
            console.log('\n⚠️  Warning: No API keys configured. Some tests may fail.');
            console.log('   Please configure HUGGINGFACE_API_TOKEN, FIREWORKS_API_KEY, or other provider keys.');
        }
        
        // Test 2: Direct Falcon humanization
        await testFalconHumanization();
        
        // Test 3: Advanced humanizer integration
        await testAdvancedHumanizerFalcon();
        
        // Test 4: HuggingFace client
        await testHuggingFaceFalcon();
        
        // Test 5: Complete service integration
        await testCompleteIntegration();
        
        // Test 6: Performance benchmark
        await benchmarkPerformance();
        
        console.log('\n✅ Falcon Integration Test Suite Complete!');
        console.log('\n📋 Summary:');
        console.log('   - Falcon models are now prioritized for ≤10% AI detection targets');
        console.log('   - Enhanced prompting optimized for Falcon model performance');
        console.log('   - Fallback system maintains compatibility with existing models');
        console.log('   - All service layers updated to support Falcon integration');
        
    } catch (error) {
        console.error('\n❌ Test suite error:', error);
    }
}

// Run the tests
runFalconIntegrationTests().catch(console.error);
