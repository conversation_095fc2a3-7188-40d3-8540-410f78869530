# 🔐 Google OAuth Setup Guide

## Quick Fix for "OAuth client was not found" Error

### Step 1: Create Google Cloud Project

1. **Visit Google Cloud Console**
   - Go to: https://console.cloud.google.com/
   - Sign in with your Google account

2. **Create New Project**
   - Click the project dropdown at the top
   - Click "New Project"
   - Name: "GhostLayer"
   - Click "Create"

### Step 2: Configure OAuth Consent Screen

1. **Go to OAuth Consent Screen**
   - Navigate to "APIs & Services" > "OAuth consent screen"
   - Choose "External" user type
   - Click "Create"

2. **Fill Required Information**
   - App name: `Ghost<PERSON>ayer`
   - User support email: `<EMAIL>`
   - Developer contact: `<EMAIL>`
   - Click "Save and Continue"

3. **Scopes (Optional for now)**
   - Click "Save and Continue" (default scopes are fine)

4. **Test Users (Optional)**
   - Add your email as a test user if needed
   - Click "Save and Continue"

### Step 3: Create OAuth Credentials

1. **Go to Credentials**
   - Navigate to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth client ID"

2. **Configure OAuth Client**
   - Application type: `Web application`
   - Name: `GhostLayer - Development`

3. **Add Authorized Origins**
   - Authorized JavaScript origins:
     ```
     http://localhost:3000
     ```

4. **Add Redirect URIs**
   - Authorized redirect URIs:
     ```
     http://localhost:3000/api/auth/callback/google
     ```

5. **Create and Copy Credentials**
   - Click "Create"
   - Copy the Client ID and Client Secret

### Step 4: Update Environment Variables

1. **Open `.env.local` file in your project**

2. **Replace the placeholder values:**
   ```bash
   # Replace these with your actual credentials
   GOOGLE_CLIENT_ID=your_actual_client_id_here
   GOOGLE_CLIENT_SECRET=your_actual_client_secret_here
   ```

   Example (yours will be different):
   ```bash
   GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
   GOOGLE_CLIENT_SECRET=GOCSPX-your_secret_here
   ```

### Step 5: Restart Development Server

1. **Stop the current server** (Ctrl+C in terminal)
2. **Start again:**
   ```bash
   npm run dev
   ```

### Step 6: Test Login

1. **Visit:** http://localhost:3000
2. **Click "Sign In" button**
3. **Choose "Sign in with Google"**
4. **Complete OAuth flow**

## 🔍 Troubleshooting

### Common Issues:

1. **"OAuth client was not found"**
   - Double-check Client ID and Secret in `.env.local`
   - Ensure no extra spaces or quotes

2. **"Redirect URI mismatch"**
   - Verify redirect URI is exactly: `http://localhost:3000/api/auth/callback/google`
   - Check for typos in Google Console

3. **"This app isn't verified"**
   - This is normal for development
   - Click "Advanced" > "Go to GhostLayer (unsafe)"

4. **Environment variables not loading**
   - Restart the development server
   - Check file is named `.env.local` (not `.env.local.txt`)

### Verification Steps:

1. **Check if credentials are loaded:**
   - Visit: http://localhost:3000/api/auth/providers
   - Should show Google as available provider

2. **Check environment variables:**
   ```bash
   # In your terminal (from project root)
   node -e "console.log(process.env.GOOGLE_CLIENT_ID)"
   ```

## 🚀 For Production

When deploying to production, you'll need to:

1. **Add production domains** to Google Console:
   - Authorized origins: `https://yourdomain.com`
   - Redirect URIs: `https://yourdomain.com/api/auth/callback/google`

2. **Update environment variables** on your hosting platform

3. **Verify OAuth consent screen** for public use

## 📝 Quick Reference

- **Google Console:** https://console.cloud.google.com/
- **OAuth Consent:** APIs & Services > OAuth consent screen
- **Credentials:** APIs & Services > Credentials
- **Callback URL:** `http://localhost:3000/api/auth/callback/google`

---

**Need help?** Check the server logs in your terminal for detailed error messages.
