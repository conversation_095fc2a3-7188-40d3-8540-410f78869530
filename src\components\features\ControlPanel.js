import React from 'react';
import styles from './ControlPanel.module.css'; // Import the CSS module

const ControlPanel = ({ onProcessText, isLoading, disabled }) => {
    return (
        <div className={styles.controlPanel}>
            <button
                onClick={onProcessText}
                disabled={isLoading || disabled}
                className={styles.processButton} // Apply button style
            >
                {isLoading ? 'Processing...' : 'Humanize Text'}
            </button>
            {/*
            <div className={styles.optionsContainer}>
                <label htmlFor="intensity">Intensity:</label>
                <input type="range" id="intensity" name="intensity" min="1" max="5" defaultValue="3" />
            </div>
            */}
        </div>
    );
};

export default ControlPanel;
