.navbar {
    background-color: #2c3e50; /* Slightly more modern dark blue/grey */
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Subtle shadow for depth */
    font-family: inherit; /* Inherit global font */
}

.container {
    max-width: 800px; /* Match Home.module.css container */
    margin: 0 auto;
    padding: 0 1rem; /* Consistent padding */
    display: flex;
    justify-content: space-between; /* Pushes title to left, future links to right */
    align-items: center; /* Vertically align items */
}

.title {
    font-size: 1.6rem; /* Slightly larger */
    font-weight: bold;
    color: white;
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

.title:hover {
    color: #ecf0f1; /* Lighter shade on hover */
}

/* Placeholder for future nav links */
.navLinks {
    display: flex;
    gap: 15px;
}

.navLink {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.2s ease-in-out;
    font-weight: 500;
}

.navLink:hover {
    background-color: #34495e;
    transform: translateY(-1px);
}

.navLinks a {
    color: white;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.2s ease-in-out;
}

.navLinks a:hover {
    background-color: #34495e; /* Slightly darker background on link hover */
}
