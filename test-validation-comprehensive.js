/**
 * Comprehensive Validation Test for Enhanced Humanization
 * Tests contextual hesitation markers, formal document protection, 
 * word/structural variations, and hesitation frequency limits
 */

import { advancedHumanization } from './src/utils/advancedHumanizer.js';

// Test cases for different scenarios
const validationTests = [
    {
        name: "Contextual Hesitation Markers Test",
        text: "This technology demonstrates significant benefits for organizations. The implementation requires careful planning and substantial investment. The results justify the costs in most cases.",
        expectedBehavior: "Should add contextual hesitation markers like 'Notably,', 'Importantly,', 'Furthermore,' based on sentence context",
        testType: "hesitation"
    },
    {
        name: "Formal Document Protection Test", 
        text: `I. Executive Summary
        
A. Current State Analysis
B. Strategic Recommendations

1. Implementation Phase
2. Evaluation Metrics

The analysis reveals significant opportunities for improvement.`,
        expectedBehavior: "Should preserve ALL formal elements (Roman numerals, letters, numbers) while humanizing paragraph content",
        testType: "protection"
    },
    {
        name: "Technical Content Protection Test",
        text: "The API uses JSON for data exchange. HTTP methods include GET, POST, PUT, and DELETE. OAuth 2.0 handles authentication. Rate limiting is 1000 requests per hour.",
        expectedBehavior: "Should protect technical terms (API, JSON, HTTP, OAuth) and avoid hesitation in technical content",
        testType: "technical"
    },
    {
        name: "Word Variations Test",
        text: "This is very important because it shows good results. The technology is also beneficial and provides significant advantages.",
        expectedBehavior: "Should replace common words: 'very' → 'quite/rather', 'also' → 'additionally', 'good' → 'effective'",
        testType: "variations"
    },
    {
        name: "Hesitation Frequency Limit Test",
        text: "The first sentence discusses implementation. The second sentence covers benefits. The third sentence addresses challenges. The fourth sentence examines solutions. The fifth sentence reviews outcomes.",
        expectedBehavior: "Should respect maximum 5% hesitation frequency (max 1 hesitation marker for 5 sentences)",
        testType: "frequency"
    }
];

/**
 * Run comprehensive validation tests
 */
async function runValidationTests() {
    console.log('🔍 COMPREHENSIVE VALIDATION TEST SUITE');
    console.log('═'.repeat(60));
    
    const results = {
        passed: 0,
        failed: 0,
        details: []
    };
    
    for (const test of validationTests) {
        console.log(`\n📋 ${test.name}`);
        console.log(`Expected: ${test.expectedBehavior}`);
        console.log('─'.repeat(50));
        
        try {
            const result = await runSingleValidationTest(test);
            results.details.push(result);
            
            if (result.passed) {
                results.passed++;
                console.log(`✅ PASSED: ${result.summary}`);
            } else {
                results.failed++;
                console.log(`❌ FAILED: ${result.summary}`);
            }
            
        } catch (error) {
            results.failed++;
            console.log(`❌ ERROR: ${error.message}`);
            results.details.push({
                name: test.name,
                passed: false,
                error: error.message
            });
        }
    }
    
    // Generate final report
    generateValidationReport(results);
}

/**
 * Run a single validation test
 */
async function runSingleValidationTest(test) {
    const original = test.text;
    
    // Run humanization multiple times to test consistency
    const runs = [];
    for (let i = 0; i < 5; i++) {
        const humanized = advancedHumanization(original, { aggressiveness: 0.7 });
        runs.push(humanized);
    }
    
    // Analyze results based on test type
    switch (test.testType) {
        case 'hesitation':
            return validateHesitationMarkers(test, original, runs);
        case 'protection':
            return validateFormalProtection(test, original, runs);
        case 'technical':
            return validateTechnicalProtection(test, original, runs);
        case 'variations':
            return validateWordVariations(test, original, runs);
        case 'frequency':
            return validateHesitationFrequency(test, original, runs);
        default:
            throw new Error(`Unknown test type: ${test.testType}`);
    }
}

/**
 * Validate contextual hesitation markers
 */
function validateHesitationMarkers(test, original, runs) {
    const contextualMarkers = [
        'notably,', 'importantly,', 'significantly,', 'remarkably,',
        'however,', 'meanwhile,', 'furthermore,', 'additionally,',
        'specifically,', 'namely,', 'in other words,', 'that is to say,'
    ];
    
    let hasContextualMarkers = false;
    let inappropriateMarkers = false;
    
    runs.forEach(run => {
        // Check for contextual markers
        contextualMarkers.forEach(marker => {
            if (run.toLowerCase().includes(marker)) {
                hasContextualMarkers = true;
            }
        });
        
        // Check for inappropriate markers in wrong contexts
        if (run.includes('Listen,') || run.includes('Look,')) {
            inappropriateMarkers = true;
        }
    });
    
    const passed = hasContextualMarkers && !inappropriateMarkers;
    
    return {
        name: test.name,
        passed,
        summary: passed ? 
            'Contextual hesitation markers applied appropriately' : 
            'Missing contextual markers or inappropriate marker usage',
        details: {
            hasContextualMarkers,
            inappropriateMarkers,
            sampleOutput: runs[0].substring(0, 100) + '...'
        }
    };
}

/**
 * Validate formal document protection
 */
function validateFormalProtection(test, original, runs) {
    const formalPatterns = [
        /^[IVX]+\./gm,    // Roman numerals
        /^[A-Z]\./gm,     // Letter markers
        /^\d+\./gm        // Number markers
    ];
    
    let allProtected = true;
    
    formalPatterns.forEach(pattern => {
        const originalMatches = original.match(pattern) || [];
        
        runs.forEach(run => {
            const runMatches = run.match(pattern) || [];
            if (originalMatches.length !== runMatches.length) {
                allProtected = false;
            }
        });
    });
    
    return {
        name: test.name,
        passed: allProtected,
        summary: allProtected ? 
            'All formal document elements preserved' : 
            'Some formal elements were modified',
        details: {
            originalElements: original.match(/^[IVX]+\.|^[A-Z]\.|^\d+\./gm) || [],
            sampleOutput: runs[0]
        }
    };
}

/**
 * Validate technical content protection
 */
function validateTechnicalProtection(test, original, runs) {
    const technicalTerms = ['API', 'JSON', 'HTTP', 'OAuth', 'GET', 'POST', 'PUT', 'DELETE'];
    
    let allTermsProtected = true;
    let hasInappropriateHesitation = false;
    
    runs.forEach(run => {
        // Check if technical terms are preserved
        technicalTerms.forEach(term => {
            const originalCount = (original.match(new RegExp(`\\b${term}\\b`, 'g')) || []).length;
            const runCount = (run.match(new RegExp(`\\b${term}\\b`, 'g')) || []).length;
            if (originalCount !== runCount) {
                allTermsProtected = false;
            }
        });
        
        // Check for inappropriate hesitation in technical content
        const sentences = run.split(/[.!?]+/);
        sentences.forEach(sentence => {
            if (technicalTerms.some(term => sentence.includes(term))) {
                if (/^(Well,|Actually,|Listen,|Look,)/i.test(sentence.trim())) {
                    hasInappropriateHesitation = true;
                }
            }
        });
    });
    
    const passed = allTermsProtected && !hasInappropriateHesitation;
    
    return {
        name: test.name,
        passed,
        summary: passed ? 
            'Technical content properly protected' : 
            'Technical terms modified or inappropriate hesitation added',
        details: {
            allTermsProtected,
            hasInappropriateHesitation,
            sampleOutput: runs[0]
        }
    };
}

/**
 * Validate word variations
 */
function validateWordVariations(test, original, runs) {
    const targetWords = ['very', 'also', 'good', 'important', 'significant'];
    const expectedReplacements = {
        'very': ['quite', 'rather', 'pretty', 'fairly'],
        'also': ['additionally', 'furthermore', 'moreover', 'as well'],
        'good': ['effective', 'beneficial', 'valuable', 'positive']
    };
    
    let hasVariations = false;
    
    runs.forEach(run => {
        targetWords.forEach(word => {
            const originalCount = (original.match(new RegExp(`\\b${word}\\b`, 'gi')) || []).length;
            const runCount = (run.match(new RegExp(`\\b${word}\\b`, 'gi')) || []).length;
            
            if (runCount < originalCount) {
                hasVariations = true;
            }
        });
    });
    
    return {
        name: test.name,
        passed: hasVariations,
        summary: hasVariations ? 
            'Word variations successfully applied' : 
            'No word variations detected',
        details: {
            hasVariations,
            sampleOutput: runs[0]
        }
    };
}

/**
 * Validate hesitation frequency limits
 */
function validateHesitationFrequency(test, original, runs) {
    const hesitationMarkers = [
        'actually,', 'well,', 'so,', 'notably,', 'importantly,', 
        'however,', 'furthermore,', 'specifically,'
    ];
    
    let respectsFrequencyLimit = true;
    const maxAllowedHesitations = Math.ceil(5 * 0.05); // 5% of 5 sentences = 0.25, rounded up to 1
    
    runs.forEach(run => {
        const sentences = run.split(/[.!?]+/).filter(s => s.trim().length > 0);
        let hesitationCount = 0;
        
        hesitationMarkers.forEach(marker => {
            const regex = new RegExp(`\\b${marker}`, 'gi');
            const matches = run.match(regex);
            if (matches) {
                hesitationCount += matches.length;
            }
        });
        
        const frequencyPercentage = (hesitationCount / sentences.length) * 100;
        
        if (frequencyPercentage > 5) {
            respectsFrequencyLimit = false;
        }
    });
    
    return {
        name: test.name,
        passed: respectsFrequencyLimit,
        summary: respectsFrequencyLimit ? 
            'Hesitation frequency within 5% limit' : 
            'Hesitation frequency exceeds 5% limit',
        details: {
            respectsFrequencyLimit,
            maxAllowedHesitations,
            sampleOutput: runs[0]
        }
    };
}

/**
 * Generate comprehensive validation report
 */
function generateValidationReport(results) {
    console.log('\n' + '═'.repeat(60));
    console.log('📊 VALIDATION TEST SUMMARY');
    console.log('═'.repeat(60));
    
    console.log(`\n🎯 Overall Results:`);
    console.log(`   ✅ Passed: ${results.passed}`);
    console.log(`   ❌ Failed: ${results.failed}`);
    console.log(`   📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
    
    console.log(`\n📋 Detailed Results:`);
    results.details.forEach(result => {
        const status = result.passed ? '✅' : '❌';
        console.log(`   ${status} ${result.name}: ${result.summary}`);
    });
    
    const overallSuccess = results.passed >= 4; // At least 4 out of 5 tests should pass
    console.log(`\n🏆 Overall Assessment: ${overallSuccess ? '✅ VALIDATION PASSED' : '❌ VALIDATION FAILED'}`);
    
    if (!overallSuccess) {
        console.log('\n⚠️  Recommendations:');
        console.log('   - Review failed test cases and adjust humanization algorithms');
        console.log('   - Ensure protection patterns are comprehensive');
        console.log('   - Verify hesitation frequency calculations');
    }
}

// Run validation tests
if (import.meta.url === `file://${process.argv[1]}`) {
    runValidationTests().catch(console.error);
}

export { runValidationTests };
