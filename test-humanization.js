/**
 * Test script for enhanced humanization algorithms
 * Run with: node test-humanization.js
 */

// Import the enhanced humanization functions
async function loadModules() {
    const balancedModule = await import('./src/utils/balancedHumanizer.js');
    const advancedModule = await import('./src/utils/advancedHumanizer.js');
    const multiPassModule = await import('./src/utils/multiPassHumanizer.js');

    return {
        balancedHumanization: balancedModule.balancedHumanization,
        advancedHumanization: advancedModule.advancedHumanization,
        multiPassHumanization: multiPassModule.multiPassHumanization
    };
}

// Sample AI-generated text for testing
const testTexts = [
    {
        name: "Academic Style AI Text",
        text: `Artificial intelligence has revolutionized numerous industries and continues to demonstrate significant potential for transformative applications. It is important to note that machine learning algorithms have become increasingly sophisticated, enabling organizations to optimize their operations and enhance their capabilities. Furthermore, the implementation of AI technologies facilitates improved decision-making processes and streamlines complex workflows. Additionally, it is essential to recognize that these advancements represent a paradigm shift in how businesses approach technological solutions.`
    },
    {
        name: "Business Report AI Text", 
        text: `The comprehensive analysis reveals that organizations must leverage innovative technologies to maintain competitive advantages in today's dynamic marketplace. It is crucial to understand that digital transformation initiatives require substantial investment and strategic planning. Moreover, companies that successfully implement these solutions demonstrate improved operational efficiency and enhanced customer satisfaction. Therefore, it is recommended that stakeholders prioritize the adoption of cutting-edge technologies to ensure long-term sustainability and growth.`
    },
    {
        name: "Technical Documentation AI Text",
        text: `The system architecture utilizes advanced algorithms to process data efficiently and deliver optimal performance. It is worth mentioning that the implementation follows industry best practices and incorporates robust security measures. Furthermore, the framework provides comprehensive functionality while maintaining scalability and reliability. Additionally, the solution demonstrates exceptional compatibility with existing infrastructure and supports seamless integration across multiple platforms.`
    }
];

async function testHumanization() {
    console.log('🧪 Testing Enhanced Humanization Algorithms\n');
    console.log('=' .repeat(80));

    // Load the modules
    const { balancedHumanization, advancedHumanization, multiPassHumanization } = await loadModules();
    
    for (const testCase of testTexts) {
        console.log(`\n📝 Testing: ${testCase.name}`);
        console.log('-'.repeat(50));
        
        console.log('\n🤖 Original AI Text:');
        console.log(testCase.text);
        
        // Test 1: Original balanced humanization
        console.log('\n🔧 Original Balanced Humanization:');
        try {
            const originalResult = balancedHumanization(testCase.text, null, 0, { useAdvanced: false });
            console.log(originalResult);
        } catch (error) {
            console.error('Error with original humanization:', error.message);
        }
        
        // Test 2: Enhanced balanced humanization
        console.log('\n✨ Enhanced Balanced Humanization:');
        try {
            const enhancedResult = balancedHumanization(testCase.text, null, 0, { 
                useAdvanced: true, 
                aggressiveness: 0.7,
                maintainTone: true 
            });
            console.log(enhancedResult);
        } catch (error) {
            console.error('Error with enhanced humanization:', error.message);
        }
        
        // Test 3: Advanced humanization (direct)
        console.log('\n🚀 Advanced Humanization (Direct):');
        try {
            const advancedResult = advancedHumanization(testCase.text, {
                aggressiveness: 0.8,
                preserveLength: false,
                maintainTone: true
            });
            console.log(advancedResult);
        } catch (error) {
            console.error('Error with advanced humanization:', error.message);
        }
        
        // Test 4: Multi-pass humanization
        console.log('\n🔄 Multi-Pass Humanization:');
        try {
            const multiPassResult = multiPassHumanization(testCase.text, {
                useAdvancedEngine: true,
                maxAggressiveness: 0.9
            });
            console.log(multiPassResult);
        } catch (error) {
            console.error('Error with multi-pass humanization:', error.message);
        }
        
        console.log('\n' + '='.repeat(80));
    }
    
    // Performance comparison
    console.log('\n⚡ Performance Comparison');
    console.log('-'.repeat(50));

    const performanceText = testTexts[0].text;

    console.time('Original Humanization');
    try {
        balancedHumanization(performanceText, null, 0, { useAdvanced: false });
    } catch (error) {
        console.error('Performance test error (original):', error.message);
    }
    console.timeEnd('Original Humanization');

    console.time('Enhanced Humanization');
    try {
        balancedHumanization(performanceText, null, 0, { useAdvanced: true, aggressiveness: 0.7 });
    } catch (error) {
        console.error('Performance test error (enhanced):', error.message);
    }
    console.timeEnd('Enhanced Humanization');

    console.time('Advanced Humanization');
    try {
        advancedHumanization(performanceText, { aggressiveness: 0.8 });
    } catch (error) {
        console.error('Performance test error (advanced):', error.message);
    }
    console.timeEnd('Advanced Humanization');
    
    console.log('\n✅ Testing completed!');
    console.log('\n📊 Expected Improvements:');
    console.log('• AI Detection Score: From 85% → Target ≤20%');
    console.log('• Sentence Structure: More varied and human-like');
    console.log('• Word Choice: Context-aware and natural');
    console.log('• Writing Patterns: Human inconsistencies and quirks');
    console.log('• Readability: Maintained professional quality');
    
    console.log('\n🎯 Next Steps:');
    console.log('1. Test with actual AI detection tools (ZeroGPT, GPTZero)');
    console.log('2. Compare detection scores before/after enhancement');
    console.log('3. Validate readability and meaning preservation');
    console.log('4. Fine-tune aggressiveness levels based on results');
}

// Run the test
if (require.main === module) {
    testHumanization().catch(console.error);
}

module.exports = { testHumanization };
