.controlPanel {
    margin: 25px 0; /* Increased margin for better spacing */
    display: flex;
    justify-content: center; /* Center the button */
    align-items: center;
    gap: 20px; /* For future controls like sliders */
}

.processButton {
    background-color: #0070f3; /* Next.js blue */
    color: white;
    border: none;
    padding: 12px 28px; /* Slightly more padding */
    font-size: 1.05rem; /* Slightly larger font */
    font-weight: 500;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out, transform 0.1s ease-in-out;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.processButton:hover:not(:disabled) {
    background-color: #005bb5; /* Darker blue on hover */
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.processButton:disabled {
    background-color: #a0c7e8; /* Lighter blue when disabled */
    color: #f0f0f0;
    cursor: not-allowed;
    box-shadow: none;
}

.processButton:active:not(:disabled) {
    transform: translateY(1px); /* Subtle press effect */
    background-color: #0052a0;
}

/* Placeholder for future controls like sliders */
.optionsContainer {
    display: flex;
    align-items: center;
    gap: 10px;
}

.optionsContainer label {
    font-size: 0.9rem;
    color: #555;
}

.optionsContainer input[type="range"] {
    cursor: pointer;
}
