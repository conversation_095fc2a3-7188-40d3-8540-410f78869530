import React, { useState } from 'react';
import { useSession, signIn, signOut } from 'next-auth/react';
import Link from 'next/link';
import styles from './Header.module.css';

const Header = () => {
  const { data: session, status } = useSession();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header className={styles.header}>
      <div className={styles.container}>
        <div className={styles.nav}>
          {/* Logo */}
          <Link href="/" className={styles.logo}>
            <div className={styles.logoIcon}>
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                <rect width="32" height="32" rx="8" fill="url(#gradient)" />
                <path
                  d="M8 12h16M8 16h16M8 20h12"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#0ea5e9" />
                    <stop offset="100%" stopColor="#0369a1" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <span className={styles.logoText}>GhostLayer</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className={styles.desktopNav}>
            <Link href="/" className={styles.navLink}>
              Home
            </Link>
            <Link href="/features" className={styles.navLink}>
              Features
            </Link>
            <Link href="/pricing" className={styles.navLink}>
              Pricing
            </Link>
            <Link href="/about" className={styles.navLink}>
              About
            </Link>
          </nav>

          {/* User Actions */}
          <div className={styles.userActions}>
            {status === 'loading' ? (
              <div className={styles.loadingSpinner}></div>
            ) : session ? (
              <div className={styles.userMenu}>
                <div className={styles.userInfo}>
                  {session.user.image && (
                    <img
                      src={session.user.image}
                      alt={session.user.name}
                      className={styles.userAvatar}
                    />
                  )}
                  <div className={styles.userDetails}>
                    <span className={styles.userName}>{session.user.name}</span>
                    <span className={styles.userTier}>
                      {session.user.subscriptionTier === 'premium_monthly' ||
                      session.user.subscriptionTier === 'premium_yearly'
                        ? 'Premium'
                        : 'Free'}
                    </span>
                  </div>
                </div>
                <button
                  onClick={() => signOut()}
                  className={styles.signOutButton}
                >
                  Sign Out
                </button>
              </div>
            ) : (
              <div className={styles.authButtons}>
                <button
                  onClick={() => signIn('google')}
                  className={styles.signInButton}
                >
                  <svg width="20" height="20" viewBox="0 0 24 24" className={styles.googleIcon}>
                    <path
                      fill="#4285F4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34A853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#FBBC05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#EA4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  Sign in with Google
                </button>
              </div>
            )}

            {/* Mobile Menu Button */}
            <button
              className={styles.mobileMenuButton}
              onClick={toggleMenu}
              aria-label="Toggle menu"
            >
              <span className={`${styles.hamburger} ${isMenuOpen ? styles.open : ''}`}>
                <span></span>
                <span></span>
                <span></span>
              </span>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <nav className={`${styles.mobileNav} ${isMenuOpen ? styles.open : ''}`}>
          <Link href="/" className={styles.mobileNavLink} onClick={() => setIsMenuOpen(false)}>
            Home
          </Link>
          <Link href="/features" className={styles.mobileNavLink} onClick={() => setIsMenuOpen(false)}>
            Features
          </Link>
          <Link href="/pricing" className={styles.mobileNavLink} onClick={() => setIsMenuOpen(false)}>
            Pricing
          </Link>
          <Link href="/about" className={styles.mobileNavLink} onClick={() => setIsMenuOpen(false)}>
            About
          </Link>
          {!session && (
            <button
              onClick={() => {
                signIn('google');
                setIsMenuOpen(false);
              }}
              className={styles.mobileSignInButton}
            >
              Sign in with Google
            </button>
          )}
        </nav>
      </div>
    </header>
  );
};

export default Header;
