/**
 * Enhanced Humanization Service
 * Integrates advanced LLM-based humanization with pattern-based fallback
 * Now powered by DeepSeek-R1, Llama 3.1, and other state-of-the-art models
 */

import { HfInference } from '@huggingface/inference';
import { advancedHumanization, testHumanizationSystem, getHumanizationSystemStatus } from '../utils/advancedHumanizer.js';
import { withCacheAndMonitoring, getPerformanceReport, humanizationCache, performanceMonitor } from '../utils/performanceCache.js';
import { humanizeWithAdvancedLLM, isAdvancedLLMAvailable, getProviderStatus } from './falconService.js';

// Legacy model configurations (kept for backward compatibility)
const HUMANEYES_MODEL = 'Eemansleepdeprived/Humaneyes';
const PEGASUS_PARAPHRASE_MODEL = 'tuner007/pegasus_paraphrase';

// Initialize Hugging Face client
let hfClient = null;

/**
 * Initialize the Hugging Face client
 */
function initializeHuggingFaceClient() {
    const apiKey = process.env.HUGGINGFACE_API_KEY;
    
    if (!apiKey) {
        console.warn('Hugging Face API key not found. Humaneyes service will be disabled.');
        return null;
    }
    
    try {
        hfClient = new HfInference(apiKey);
        console.log('Hugging Face client initialized successfully');
        return hfClient;
    } catch (error) {
        console.error('Failed to initialize Hugging Face client:', error.message);
        return null;
    }
}

/**
 * Check if enhanced humanization service is available
 */
export function isHumanizationAvailable() {
    const status = getHumanizationSystemStatus();
    return status.llmBased.available || status.patternBased.available;
}

/**
 * Check if legacy Humaneyes service is available (backward compatibility)
 */
export function isHumaneyesAvailable() {
    return hfClient !== null || initializeHuggingFaceClient() !== null;
}

/**
 * Core humanization function (without caching/monitoring)
 */
async function coreHumanizeText(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        method = 'auto', // 'auto', 'llm', 'pattern', 'legacy'
        fallbackEnabled = true
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string',
            originalText: text,
            method: 'none'
        };
    }

    const startTime = Date.now();

    try {
        console.log(`Starting enhanced humanization (method: ${method}, target: ≤${targetDetection}%)...`);

        // Determine which method to use and prioritize advanced LLMs
        if (method === 'pattern') {
            // Force pattern-based only
            const result = await advancedHumanization(text, {
                aggressiveness,
                maintainTone,
                targetDetection,
                useModelBased: false,
                fallbackToPatterns: false
            });

            return {
                ...result,
                totalProcessingTime: Date.now() - startTime,
                requestedMethod: method,
                actualMethod: result.method || 'pattern'
            };

        } else if (method === 'llm') {
            // Force LLM-based only (try Falcon-enhanced LLMs first)
            if (isAdvancedLLMAvailable()) {
                // Prioritize Falcon models for ≤10% AI detection target
                const preferredModel = targetDetection <= 10 ? 'falcon-3-7b' : 'deepseek-r1';

                const advancedResult = await humanizeWithAdvancedLLM(text, {
                    aggressiveness,
                    maintainTone,
                    targetDetection,
                    preferredModel: preferredModel
                });

                if (advancedResult.success) {
                    return {
                        ...advancedResult,
                        totalProcessingTime: Date.now() - startTime,
                        requestedMethod: method,
                        actualMethod: advancedResult.method || 'llm-advanced-falcon',
                        detectionTarget: targetDetection
                    };
                }
            }

            // Fallback to standard LLM approach
            const result = await advancedHumanization(text, {
                aggressiveness,
                maintainTone,
                targetDetection,
                useModelBased: true,
                fallbackToPatterns: false
            });

            return {
                ...result,
                totalProcessingTime: Date.now() - startTime,
                requestedMethod: method,
                actualMethod: result.method || 'llm'
            };

        } else if (method === 'legacy') {
            // Use legacy Humaneyes model
            return await humanizeWithHumaneyes(text, options);
        }

        // 'auto' method: Try Falcon-enhanced LLMs first, then fallback chain
        if (isAdvancedLLMAvailable()) {
            try {
                console.log(`Trying Falcon-enhanced humanization (auto mode, target: ≤${targetDetection}%)...`);

                // Prioritize Falcon models for ≤10% AI detection target
                const preferredModel = targetDetection <= 10 ? 'falcon-3-7b' : 'deepseek-r1';

                const advancedResult = await humanizeWithAdvancedLLM(text, {
                    aggressiveness,
                    maintainTone,
                    targetDetection,
                    preferredModel: preferredModel
                });

                if (advancedResult.success) {
                    console.log(`Falcon-enhanced success: ${advancedResult.provider}/${advancedResult.modelName}`);
                    return {
                        ...advancedResult,
                        totalProcessingTime: Date.now() - startTime,
                        requestedMethod: method,
                        actualMethod: advancedResult.method || 'llm-advanced-falcon',
                        detectionTarget: targetDetection
                    };
                } else {
                    console.log('Falcon-enhanced LLM failed, trying standard approach...');
                }
            } catch (error) {
                console.warn('Falcon-enhanced LLM error:', error.message);
            }
        }

        // Fallback to standard advanced humanization
        const result = await advancedHumanization(text, {
            aggressiveness,
            maintainTone,
            targetDetection,
            useModelBased: true,
            fallbackToPatterns: fallbackEnabled
        });

        const totalTime = Date.now() - startTime;

        console.log(`Enhanced humanization completed: ${result.method} method, ${totalTime}ms total`);

        return {
            ...result,
            totalProcessingTime: totalTime,
            requestedMethod: method,
            actualMethod: result.method
        };

    } catch (error) {
        console.error('Enhanced humanization failed:', error.message);

        const totalTime = Date.now() - startTime;

        return {
            success: false,
            error: error.message,
            originalText: text,
            method: 'failed',
            requestedMethod: method,
            totalProcessingTime: totalTime
        };
    }
}

/**
 * Enhanced humanization function - Main entry point for text humanization
 * Uses advanced LLM-based humanization with pattern-based fallback
 * Includes caching, rate limiting, and performance monitoring
 * @param {string} text - Text to humanize
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Humanization result
 */
export const humanizeText = withCacheAndMonitoring(coreHumanizeText);

/**
 * Test the humanization system
 * @returns {Promise<Object>} Test results
 */
export async function testHumanizationService() {
    try {
        console.log('Testing humanization system...');
        const result = await testHumanizationSystem();

        return {
            success: true,
            systemTest: result,
            timestamp: new Date().toISOString()
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

/**
 * Get humanization service status and configuration
 * @returns {Object} Service status
 */
export function getHumanizationServiceStatus() {
    const systemStatus = getHumanizationSystemStatus();
    const legacyAvailable = isHumaneyesAvailable();

    return {
        enhanced: systemStatus,
        legacy: {
            available: legacyAvailable,
            model: HUMANEYES_MODEL
        },
        recommendedMethod: systemStatus.defaultMode,
        availableMethods: [
            ...(systemStatus.llmBased.available ? ['llm', 'auto'] : []),
            'pattern',
            ...(legacyAvailable ? ['legacy'] : [])
        ],
        timestamp: new Date().toISOString()
    };
}

/**
 * Batch humanization for multiple texts
 * @param {Array<string>} texts - Array of texts to humanize
 * @param {Object} options - Configuration options
 * @returns {Promise<Array>} Array of humanization results
 */
export async function batchHumanizeTexts(texts, options = {}) {
    if (!Array.isArray(texts)) {
        throw new Error('Texts must be an array');
    }

    const results = [];
    const batchSize = options.batchSize || 5;

    console.log(`Starting batch humanization: ${texts.length} texts, batch size: ${batchSize}`);

    for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        const batchPromises = batch.map((text, index) =>
            humanizeText(text, { ...options, batchIndex: i + index })
        );

        const batchResults = await Promise.allSettled(batchPromises);
        results.push(...batchResults.map(result =>
            result.status === 'fulfilled' ? result.value : {
                success: false,
                error: result.reason.message,
                method: 'failed'
            }
        ));

        // Add delay between batches to respect rate limits
        if (i + batchSize < texts.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`Batch humanization completed: ${successCount}/${texts.length} successful`);

    return results;
}

/**
 * Humanize text using the Humaneyes model
 * @param {string} text - Text to humanize
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Humanization result
 */
export async function humanizeWithHumaneyes(text, options = {}) {
    const {
        maxLength = 512,
        numBeams = 5,
        temperature = 1.2,
        doSample = true,
        topP = 0.9,
        repetitionPenalty = 1.1
    } = options;
    
    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string',
            originalText: text
        };
    }
    
    // Initialize client if not already done
    if (!hfClient && !initializeHuggingFaceClient()) {
        return {
            success: false,
            error: 'Hugging Face client not available',
            originalText: text
        };
    }
    
    try {
        console.log('Starting Humaneyes humanization...');
        const startTime = Date.now();
        
        // Prepare the input for the model
        const input = text.trim();
        
        // Call the Humaneyes model
        const response = await hfClient.textGeneration({
            model: HUMANEYES_MODEL,
            inputs: input,
            parameters: {
                max_length: maxLength,
                num_beams: numBeams,
                temperature: temperature,
                do_sample: doSample,
                top_p: topP,
                repetition_penalty: repetitionPenalty,
                return_full_text: false
            }
        });
        
        const processingTime = Date.now() - startTime;
        
        // Extract the generated text
        const humanizedText = response.generated_text || response;
        
        console.log(`Humaneyes humanization completed in ${processingTime}ms`);
        
        return {
            success: true,
            humanizedText: humanizedText.trim(),
            originalText: text,
            processingTime,
            model: 'humaneyes',
            metadata: {
                modelUsed: HUMANEYES_MODEL,
                parameters: {
                    maxLength,
                    numBeams,
                    temperature,
                    doSample,
                    topP,
                    repetitionPenalty
                }
            }
        };
        
    } catch (error) {
        console.error('Humaneyes humanization failed:', error.message);
        
        return {
            success: false,
            error: error.message,
            originalText: text,
            fallbackRecommended: true
        };
    }
}

/**
 * Humanize text using the base Pegasus paraphrase model (fallback)
 * @param {string} text - Text to humanize
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Humanization result
 */
export async function humanizeWithPegasus(text, options = {}) {
    const {
        maxLength = 60,
        numBeams = 10,
        numReturnSequences = 1,
        temperature = 1.5
    } = options;
    
    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string',
            originalText: text
        };
    }
    
    // Initialize client if not already done
    if (!hfClient && !initializeHuggingFaceClient()) {
        return {
            success: false,
            error: 'Hugging Face client not available',
            originalText: text
        };
    }
    
    try {
        console.log('Starting Pegasus paraphrase humanization...');
        const startTime = Date.now();
        
        // Call the Pegasus paraphrase model
        const response = await hfClient.textGeneration({
            model: PEGASUS_PARAPHRASE_MODEL,
            inputs: text.trim(),
            parameters: {
                max_length: maxLength,
                num_beams: numBeams,
                num_return_sequences: numReturnSequences,
                temperature: temperature,
                return_full_text: false
            }
        });
        
        const processingTime = Date.now() - startTime;
        
        // Extract the generated text
        const humanizedText = response.generated_text || response;
        
        console.log(`Pegasus humanization completed in ${processingTime}ms`);
        
        return {
            success: true,
            humanizedText: humanizedText.trim(),
            originalText: text,
            processingTime,
            model: 'pegasus-paraphrase',
            metadata: {
                modelUsed: PEGASUS_PARAPHRASE_MODEL,
                parameters: {
                    maxLength,
                    numBeams,
                    numReturnSequences,
                    temperature
                }
            }
        };
        
    } catch (error) {
        console.error('Pegasus humanization failed:', error.message);
        
        return {
            success: false,
            error: error.message,
            originalText: text,
            fallbackRecommended: true
        };
    }
}

/**
 * Enhanced humanization with multiple model approach
 * Uses Humaneyes as primary, with fallbacks to Pegasus and local methods
 * @param {string} text - Text to humanize
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Humanization result
 */
export async function enhancedHumanization(text, options = {}) {
    const {
        useHumaneyes = true,
        usePegasus = true,
        useLocalFallback = true,
        maxRetries = 2
    } = options;
    
    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string',
            originalText: text
        };
    }
    
    const attempts = [];
    
    // Try Humaneyes first if enabled and available
    if (useHumaneyes && isHumaneyesAvailable()) {
        try {
            const result = await humanizeWithHumaneyes(text, options);
            attempts.push({ model: 'humaneyes', ...result });
            
            if (result.success) {
                return {
                    ...result,
                    attempts,
                    primaryModel: 'humaneyes'
                };
            }
        } catch (error) {
            attempts.push({ 
                model: 'humaneyes', 
                success: false, 
                error: error.message 
            });
        }
    }
    
    // Try Pegasus paraphrase as fallback
    if (usePegasus && isHumaneyesAvailable()) {
        try {
            const result = await humanizeWithPegasus(text, options);
            attempts.push({ model: 'pegasus', ...result });
            
            if (result.success) {
                return {
                    ...result,
                    attempts,
                    primaryModel: 'pegasus'
                };
            }
        } catch (error) {
            attempts.push({ 
                model: 'pegasus', 
                success: false, 
                error: error.message 
            });
        }
    }
    
    // Use local fallback if all else fails
    if (useLocalFallback) {
        try {
            // Import local humanization as fallback
            const { advancedHumanization } = await import('../utils/advancedHumanizer.js');
            const result = advancedHumanization(text, { aggressiveness: 0.7 });
            
            attempts.push({ 
                model: 'local-advanced', 
                success: true, 
                humanizedText: result,
                originalText: text
            });
            
            return {
                success: true,
                humanizedText: result,
                originalText: text,
                model: 'local-advanced',
                attempts,
                primaryModel: 'local-advanced'
            };
        } catch (error) {
            attempts.push({ 
                model: 'local-advanced', 
                success: false, 
                error: error.message 
            });
        }
    }
    
    // If all methods failed
    return {
        success: false,
        error: 'All humanization methods failed',
        originalText: text,
        attempts
    };
}

/**
 * Get service status and available models
 */
export function getServiceStatus() {
    return {
        huggingFaceAvailable: isHumaneyesAvailable(),
        models: {
            humaneyes: {
                available: isHumaneyesAvailable(),
                model: HUMANEYES_MODEL,
                description: 'Advanced AI text humanization with anti-detection capabilities'
            },
            pegasus: {
                available: isHumaneyesAvailable(),
                model: PEGASUS_PARAPHRASE_MODEL,
                description: 'Pegasus-based paraphrasing for text variation'
            },
            localAdvanced: {
                available: true,
                description: 'Local advanced humanization with contextual intelligence'
            }
        },
        apiKeyConfigured: !!process.env.HUGGINGFACE_API_KEY
    };
}

// Initialize the client on module load
initializeHuggingFaceClient();
