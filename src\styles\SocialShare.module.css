.socialShare {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 2rem;
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
  margin: 2rem 0;
}

.socialShare::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="sparkles" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="25" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="25" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23sparkles)"/></svg>') repeat;
  opacity: 0.3;
}

.header {
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: white;
}

.subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
}

.shareButtons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  position: relative;
  z-index: 1;
}

.shareButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: white;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.shareButton:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.shareButton.twitter:hover {
  background: #1da1f2;
  border-color: #1da1f2;
}

.shareButton.facebook:hover {
  background: #4267b2;
  border-color: #4267b2;
}

.shareButton.linkedin:hover {
  background: #0077b5;
  border-color: #0077b5;
}

.shareButton.whatsapp:hover {
  background: #25d366;
  border-color: #25d366;
}

.shareButton.reddit:hover {
  background: #ff4500;
  border-color: #ff4500;
}

.shareButton.copyLink:hover {
  background: #6366f1;
  border-color: #6366f1;
}

.shareButton.copied {
  background: #10b981 !important;
  border-color: #10b981 !important;
  transform: scale(1.05);
}

.shareButton.native:hover {
  background: #8b5cf6;
  border-color: #8b5cf6;
}

.icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

.sharePreview {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: left;
  position: relative;
  z-index: 1;
}

.previewTitle {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
}

.previewContent {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 1rem;
}

.previewText {
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  color: white;
  font-style: italic;
}

.previewHashtags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.hashtag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .socialShare {
    padding: 1.5rem;
    margin: 1rem 0;
  }
  
  .title {
    font-size: 1.3rem;
  }
  
  .subtitle {
    font-size: 0.9rem;
  }
  
  .shareButtons {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
  
  .shareButton {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }
  
  .icon {
    width: 18px;
    height: 18px;
  }
  
  .previewText {
    font-size: 0.8rem;
  }
  
  .hashtag {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (max-width: 480px) {
  .shareButtons {
    grid-template-columns: 1fr;
  }
  
  .shareButton {
    flex-direction: row;
    justify-content: center;
    padding: 1rem;
  }
  
  .icon {
    width: 16px;
    height: 16px;
  }
}

/* Animation for success states */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.shareButton.copied {
  animation: pulse 0.6s ease-in-out;
}

/* Hover effects for better UX */
.shareButton:active {
  transform: translateY(-1px) scale(0.98);
}

.shareButton:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Loading state for native share */
.shareButton.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.shareButton.loading .icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
