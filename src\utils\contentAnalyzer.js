/**
 * Smart Content Analyzer - Detects formal document elements and structure
 * Prevents inappropriate modifications to headings, lists, and formal content
 */

/**
 * Analyzes text to identify formal document elements and structure
 * @param {string} text - Text to analyze
 * @returns {Object} Analysis results with element detection
 */
export function analyzeDocumentStructure(text) {
    // Input validation
    if (!text || typeof text !== 'string') {
        console.warn('Invalid input to analyzeDocumentStructure:', typeof text);
        return {
            hasHeadings: false,
            hasSectionNumbers: false,
            hasListItems: false,
            hasFormalStructure: false,
            preserveFormatting: false,
            formalElements: [],
            protectedRanges: []
        };
    }

    const lines = text.split('\n');
    const analysis = {
        hasHeadings: false,
        hasSectionNumbers: false,
        hasListItems: false,
        hasFormalStructure: false,
        preserveFormatting: false,
        formalElements: [],
        protectedRanges: []
    };

    // Detect various formal elements
    lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        
        // Skip empty lines
        if (!trimmedLine) return;

        // Detect headings and section markers
        if (isHeading(trimmedLine)) {
            analysis.hasHeadings = true;
            analysis.formalElements.push({
                type: 'heading',
                line: index,
                content: trimmedLine
            });
        }

        // Detect section numbers (I., II., A., B., 1., 2., etc.)
        if (isSectionNumber(trimmedLine)) {
            analysis.hasSectionNumbers = true;
            analysis.formalElements.push({
                type: 'section',
                line: index,
                content: trimmedLine
            });
        }

        // Detect list items
        if (isListItem(trimmedLine)) {
            analysis.hasListItems = true;
            analysis.formalElements.push({
                type: 'list',
                line: index,
                content: trimmedLine
            });
        }
    });

    // Determine if document has formal structure
    analysis.hasFormalStructure = analysis.hasHeadings || analysis.hasSectionNumbers || analysis.hasListItems;
    analysis.preserveFormatting = analysis.hasFormalStructure || hasSignificantLineBreaks(text);

    return analysis;
}

/**
 * Checks if a line is a heading
 */
function isHeading(line) {
    // Common heading patterns
    const headingPatterns = [
        /^#{1,6}\s+/,                    // Markdown headings
        /^[A-Z][A-Z\s]{2,}:?\s*$/,      // ALL CAPS headings
        /^[A-Z][a-z\s]+:$/,             // Title Case headings with colon
        /^\d+\.\s*[A-Z]/,               // Numbered headings (1. Introduction)
        /^[IVX]+\.\s*[A-Z]/,            // Roman numeral headings
        /^[A-Z]\.\s*[A-Z]/,             // Letter headings (A. Overview)
        /^(Introduction|Conclusion|Summary|Overview|Abstract|Background):/i,
        /^(Chapter|Section|Part)\s+\d+/i
    ];

    return headingPatterns.some(pattern => pattern.test(line));
}

/**
 * Checks if a line is a section number or formal marker
 */
function isSectionNumber(line) {
    const sectionPatterns = [
        /^[IVX]+\.\s*/,                 // Roman numerals (I., II., III.)
        /^[A-Z]\.\s*/,                  // Letters (A., B., C.)
        /^\d+\.\s*/,                    // Numbers (1., 2., 3.)
        /^\(\d+\)\s*/,                  // Parenthetical numbers (1), (2)
        /^\([a-z]\)\s*/,                // Parenthetical letters (a), (b)
        /^[a-z]\.\s*/,                  // Lowercase letters (a., b., c.)
        /^\d+\.\d+\s*/,                 // Decimal numbering (1.1, 1.2)
        /^Step\s+\d+/i,                 // Step numbering
        /^Phase\s+[IVX\d]+/i           // Phase numbering
    ];

    return sectionPatterns.some(pattern => pattern.test(line));
}

/**
 * Checks if a line is a list item
 */
function isListItem(line) {
    const listPatterns = [
        /^[-*+]\s+/,                    // Bullet points
        /^•\s+/,                        // Bullet character
        /^\d+\)\s+/,                    // Numbered list with parenthesis
        /^[a-z]\)\s+/,                  // Lettered list with parenthesis
        /^>\s+/,                        // Quote/blockquote
        /^\|\s+/                        // Pipe list items
    ];

    return listPatterns.some(pattern => pattern.test(line));
}

/**
 * Checks if text has significant line breaks that should be preserved
 */
function hasSignificantLineBreaks(text) {
    // Count line breaks and analyze patterns
    const lines = text.split('\n');
    const nonEmptyLines = lines.filter(line => line.trim().length > 0);
    const emptyLines = lines.length - nonEmptyLines.length;

    // If more than 15% of lines are empty, likely has intentional formatting (reduced threshold)
    const emptyLineRatio = emptyLines / lines.length;

    // Check for paragraph breaks (double line breaks)
    const paragraphBreaks = text.match(/\n\s*\n/g);
    const hasParagraphBreaks = paragraphBreaks && paragraphBreaks.length >= 1; // Changed from > 1 to >= 1

    // Check if text has multiple lines (even without empty lines)
    const hasMultipleLines = lines.length > 1;

    // Check for intentional line breaks (lines that don't end with sentence terminators)
    const intentionalBreaks = lines.filter(line => {
        const trimmed = line.trim();
        return trimmed.length > 0 && !trimmed.match(/[.!?]$/);
    }).length;

    return emptyLineRatio > 0.15 || hasParagraphBreaks || (hasMultipleLines && intentionalBreaks > 0);
}

/**
 * Identifies sentences that should not be modified
 * @param {string} text - Full text
 * @param {Object} analysis - Document structure analysis
 * @returns {Array} Array of protected sentence indices
 */
export function identifyProtectedSentences(text, analysis) {
    // Input validation
    if (!text || typeof text !== 'string') {
        console.warn('Invalid input to identifyProtectedSentences:', typeof text);
        return [];
    }

    const sentences = text.split(/(?<=[.!?])\s+/);
    const protectedIndices = [];

    sentences.forEach((sentence, index) => {
        if (shouldProtectSentence(sentence, analysis)) {
            protectedIndices.push(index);
        }
    });

    return protectedIndices;
}

/**
 * Determines if a sentence should be protected from modification
 */
function shouldProtectSentence(sentence, analysis) {
    const trimmed = sentence.trim();
    
    // Protect very short sentences (likely headings or labels)
    if (trimmed.length < 10) return true;
    
    // Protect sentences that look like headings
    if (isHeading(trimmed)) return true;
    
    // Protect section numbers and formal markers
    if (isSectionNumber(trimmed)) return true;
    
    // Protect list items
    if (isListItem(trimmed)) return true;
    
    // Protect sentences with formal markers
    const formalMarkers = [
        /^(Hook|Thesis|Topic|Conclusion):/i,
        /^(Note|Warning|Important|Attention):/i,
        /^(Example|Sample|Demo):/i,
        /^(Input|Output|Result):/i,
        /^(Before|After|During):/i
    ];
    
    if (formalMarkers.some(pattern => pattern.test(trimmed))) return true;
    
    // Protect sentences with technical terms or code-like content
    if (hasTechnicalContent(trimmed)) return true;
    
    // Protect sentences that are mostly uppercase (likely headings)
    const uppercaseRatio = (trimmed.match(/[A-Z]/g) || []).length / trimmed.length;
    if (uppercaseRatio > 0.5) return true;
    
    return false;
}

/**
 * Checks if sentence contains technical content that should be preserved
 */
function hasTechnicalContent(sentence) {
    const technicalPatterns = [
        /\b[A-Z]{2,}\b/,                // Acronyms
        /\b\w+\.\w+\b/,                 // Domain names or file extensions
        /\b\d+\.\d+\b/,                 // Version numbers
        /[{}[\]()]/,                    // Brackets/braces (code-like)
        /\b(API|URL|HTTP|JSON|XML|CSS|HTML|JS)\b/i,
        /\b(function|class|method|property|variable)\b/i
    ];
    
    return technicalPatterns.some(pattern => pattern.test(sentence));
}

/**
 * Preserves original formatting while allowing content modification
 * @param {string} originalText - Original text with formatting
 * @param {string} modifiedText - Modified text content
 * @returns {string} Text with preserved formatting
 */
export function preserveFormatting(originalText, modifiedText) {
    // If original text has significant line breaks, preserve them
    const analysis = analyzeDocumentStructure(originalText);
    
    if (!analysis.preserveFormatting) {
        return modifiedText;
    }
    
    // Split both texts into lines
    const originalLines = originalText.split('\n');
    const modifiedSentences = modifiedText.split(/(?<=[.!?])\s+/);
    
    // Try to map modified sentences back to original line structure
    let sentenceIndex = 0;
    const reconstructedLines = [];
    
    for (const originalLine of originalLines) {
        const trimmedLine = originalLine.trim();
        
        if (!trimmedLine) {
            // Preserve empty lines
            reconstructedLines.push('');
            continue;
        }
        
        if (shouldProtectSentence(trimmedLine, analysis)) {
            // Keep original line unchanged
            reconstructedLines.push(originalLine);
        } else if (sentenceIndex < modifiedSentences.length) {
            // Use modified sentence but preserve indentation
            const indentation = originalLine.match(/^\s*/)[0];
            reconstructedLines.push(indentation + modifiedSentences[sentenceIndex].trim());
            sentenceIndex++;
        } else {
            // Fallback to original line
            reconstructedLines.push(originalLine);
        }
    }
    
    return reconstructedLines.join('\n');
}

/**
 * Calculates appropriate hesitation frequency based on content analysis
 * @param {Object} analysis - Document structure analysis
 * @returns {number} Frequency multiplier (0-1)
 */
export function calculateHesitationFrequency(analysis) {
    let baseFrequency = 0.05; // 5% base frequency
    
    // Reduce frequency for formal documents
    if (analysis.hasFormalStructure) {
        baseFrequency *= 0.3; // Reduce to 1.5% for formal content
    }
    
    // Further reduce for documents with many headings/sections
    if (analysis.hasHeadings && analysis.hasSectionNumbers) {
        baseFrequency *= 0.2; // Reduce to 0.3% for highly structured content
    }
    
    return Math.max(0.001, baseFrequency); // Minimum 0.1%
}
