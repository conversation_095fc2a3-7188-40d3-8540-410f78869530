import React from 'react';
import styles from './ResultsDisplay.module.css';

const ResultsDisplay = ({ outputText, detectionResult, isLoading }) => {
    if (isLoading) {
        return (
            <div className={styles.resultsDisplay}>
                <div className={styles.loadingContainer}>
                    <div className={styles.loadingSpinner}></div>
                    <p>Analyzing text...</p>
                </div>
            </div>
        );
    }

    if (!detectionResult && !outputText) return null;

    // Determine status class for conditional styling
    let statusClass = '';
    if (detectionResult?.status) {
        const lowerStatus = detectionResult.status.toLowerCase();
        if (lowerStatus.includes('human') || lowerStatus.includes('skipped')) {
            statusClass = styles.statusHuman;
        } else if (lowerStatus.includes('ai') || lowerStatus.includes('potentially')) {
            statusClass = styles.statusAI;
        } else if (lowerStatus.includes('mixed')) {
            statusClass = styles.statusMixed;
        }
    }

    if (detectionResult?.error && detectionResult?.message) {
         return (
            <div className={styles.resultsDisplay}>
                <div className={styles.header}>
                    <svg className={styles.icon} width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                        <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" strokeWidth="2"/>
                        <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" strokeWidth="2"/>
                    </svg>
                    <h4>AI Detection Check</h4>
                </div>
                <div className={styles.content}>
                    <p className={styles.errorMessage}>
                        <strong>Error:</strong> {detectionResult.message}
                    </p>
                    {detectionResult.status && (
                        <p>
                            <strong>Status:</strong>
                            <span className={statusClass || ''}>{detectionResult.status}</span>
                        </p>
                    )}
                </div>
            </div>
        );
    }

    return (
        <div className={styles.resultsDisplay}>
            <div className={styles.header}>
                <svg className={styles.icon} width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M9 12l2 2 4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                </svg>
                <h4>AI Detection Results</h4>
            </div>

            <div className={styles.content}>
                {detectionResult?.status && (
                    <div className={styles.statusSection}>
                        <div className={styles.statusItem}>
                            <strong>Detection Status:</strong>
                            <span className={`${styles.statusBadge} ${statusClass || ''}`}>
                                {detectionResult.status}
                            </span>
                        </div>
                    </div>
                )}

                {detectionResult?.score !== null && detectionResult?.score !== undefined && (
                    <div className={styles.scoreSection}>
                        <div className={styles.scoreItem}>
                            <strong>AI Probability:</strong>
                            <div className={styles.scoreDisplay}>
                                <span className={styles.scoreValue}>
                                    {(detectionResult.score * 100).toFixed(1)}%
                                </span>
                                <div className={styles.scoreBar}>
                                    <div
                                        className={styles.scoreProgress}
                                        style={{ width: `${detectionResult.score * 100}%` }}
                                    ></div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {detectionResult?.message && (
                    <div className={styles.messageSection}>
                        <strong>Details:</strong>
                        <p className={styles.message}>{detectionResult.message}</p>
                    </div>
                )}

                {outputText && (
                    <div className={styles.summary}>
                        <p className={styles.summaryText}>
                            ✅ Text processing completed successfully.
                            Your content has been optimized for human-like characteristics.
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ResultsDisplay;
