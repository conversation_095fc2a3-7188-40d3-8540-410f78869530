// --- Helper Functions ---

/**
 * Fetches synonyms for a given word using the Datamuse API.
 * @param {string} word - The word to fetch synonyms for.
 * @returns {Promise<string[]>} A promise that resolves to an array of synonym strings.
 */
async function getSynonyms(word) {
    if (!word || word.length < 3) return []; // Avoid API calls for very short words or empty strings

    try {
        const response = await fetch(`https://api.datamuse.com/words?rel_syn=${word}&max=5`);
        if (!response.ok) {
            console.warn(`Datamuse API request failed for "${word}" with status: ${response.status}`);
            return [];
        }
        const data = await response.json();
        return data.map(item => item.word);
    } catch (error) {
        console.error(`Error fetching synonyms for "${word}":`, error);
        return [];
    }
}

/**
 * Preserves the original casing of a word when replacing it with a new word.
 * @param {string} originalWord - The original word.
 * @param {string} newWord - The new word.
 * @returns {string} The new word with casing matching the original word.
 */
function preserveCase(originalWord, newWord) {
    if (!originalWord || !newWord) return newWord;

    if (originalWord === originalWord.toUpperCase()) {
        return newWord.toUpperCase();
    }
    if (originalWord === originalWord.toLowerCase()) {
        return newWord.toLowerCase();
    }
    if (originalWord.length > 0 && originalWord[0] === originalWord[0].toUpperCase()) {
        return newWord.charAt(0).toUpperCase() + newWord.slice(1).toLowerCase();
    }
    return newWord; // Default to new word's original casing if no clear pattern
}

// Words to generally avoid replacing with synonyms (articles, prepositions, conjunctions, common verbs)
const COMMON_WORDS_TO_SKIP = new Set([
    'a', 'an', 'the', 'is', 'am', 'are', 'was', 'were', 'be', 'been', 'being',
    'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'should', 'can', 'could', 'may', 'might', 'must',
    'and', 'but', 'or', 'nor', 'for', 'so', 'yet',
    'in', 'on', 'at', 'by', 'from', 'to', 'with', 'about', 'above', 'after', 'around', 'before', 'behind',
    'below', 'beneath', 'beside', 'between', 'beyond', 'down', 'during', 'inside', 'into', 'near', 'off',
    'out', 'outside', 'over', 'past', 'since', 'through', 'throughout', 'under', 'until', 'up', 'upon',
    'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them',
    'my', 'your', 'his', 'its', 'our', 'their', 'mine', 'yours', 'hers', 'ours', 'theirs',
    'myself', 'yourself', 'himself', 'herself', 'itself', 'ourselves', 'themselves',
    'this', 'that', 'these', 'those', 'very', 'really', 'quite', 'much', 'many', 'more', 'most',
    'some', 'any', 'no', 'not', 'all', 'few', 'less'
]);


// --- Core Text Modification Functions ---

/**
 * 1. Simple Paraphrasing
 * - Replaces common verbose words with simpler alternatives.
 * - Attempts synonym replacement for less common words using Datamuse API.
 * - Optionally shuffles sentences.
 * @param {string} text - The input text.
 * @returns {Promise<string>} A promise that resolves to the paraphrased text.
 */
export const simpleParaphrase = async (text) => {
    let newText = text;

    // Enhanced predefined replacements (case-insensitive) with more variety
    newText = newText.replace(/\butilize\b/gi, 'use');
    newText = newText.replace(/\beffective\b/gi, 'good');
    newText = newText.replace(/\bnecessitate\b/gi, 'require');
    newText = newText.replace(/\bdetermine\b/gi, 'find');
    newText = newText.replace(/\bimplement\b/gi, 'do');
    newText = newText.replace(/\bconsequently\b/gi, 'so');
    newText = newText.replace(/\bin order to\b/gi, 'to');

    // Additional AI-typical words to replace
    newText = newText.replace(/\bsophisticated\b/gi, 'advanced');
    newText = newText.replace(/\beffectively\b/gi, 'well');
    newText = newText.replace(/\bundoubtedly\b/gi, 'clearly');
    newText = newText.replace(/\brevolutionized\b/gi, 'changed');
    newText = newText.replace(/\bdemonstrate\b/gi, 'show');
    newText = newText.replace(/\binnovative\b/gi, 'new');
    newText = newText.replace(/\btransformative\b/gi, 'big');
    newText = newText.replace(/\bsubstantial\b/gi, 'large');
    newText = newText.replace(/\bsignificant\b/gi, 'important');
    newText = newText.replace(/\bnumerous\b/gi, 'many');
    newText = newText.replace(/\bvarious\b/gi, 'different');

    // Additional common academic/formal words to casual
    newText = newText.replace(/\bfacilitate\b/gi, 'help');
    newText = newText.replace(/\bdemonstrate\b/gi, 'show');
    newText = newText.replace(/\boptimize\b/gi, 'improve');
    newText = newText.replace(/\benhance\b/gi, 'boost');
    newText = newText.replace(/\bleverage\b/gi, 'use');
    newText = newText.replace(/\bcomprehensive\b/gi, 'complete');
    newText = newText.replace(/\bsignificant\b/gi, 'important');
    newText = newText.replace(/\bsubstantial\b/gi, 'large');
    newText = newText.replace(/\badditionally\b/gi, 'also');
    newText = newText.replace(/\bfurthermore\b/gi, 'moreover');
    newText = newText.replace(/\btherefore\b/gi, 'thus');
    newText = newText.replace(/\bhowever\b/gi, 'but');
    newText = newText.replace(/\bnevertheless\b/gi, 'still');
    newText = newText.replace(/\bsubsequently\b/gi, 'later');
    newText = newText.replace(/\bpreviously\b/gi, 'before');
    newText = newText.replace(/\bcurrently\b/gi, 'now');
    newText = newText.replace(/\bultimately\b/gi, 'finally');
    newText = newText.replace(/\binitially\b/gi, 'first');
    newText = newText.replace(/\bparticularly\b/gi, 'especially');
    newText = newText.replace(/\bsignificantly\b/gi, 'greatly');
    newText = newText.replace(/\bextremely\b/gi, 'very');

    // Sentence structure changes - more comprehensive AI pattern removal
    newText = newText.replace(/\bIt is important to note that\b/gi, 'Note that');
    newText = newText.replace(/\bIt should be noted that\b/gi, 'Worth noting,');
    newText = newText.replace(/\bIt is worth mentioning that\b/gi, 'Also,');
    newText = newText.replace(/\bIn conclusion,\b/gi, 'So,');
    newText = newText.replace(/\bIn summary,\b/gi, 'Overall,');
    newText = newText.replace(/\bFor example,\b/gi, 'Like,');
    newText = newText.replace(/\bSuch as\b/gi, 'Like');
    newText = newText.replace(/\bDue to the fact that\b/gi, 'Because');
    newText = newText.replace(/\bIn spite of the fact that\b/gi, 'Even though');
    newText = newText.replace(/\bWith regard to\b/gi, 'About');
    newText = newText.replace(/\bIn relation to\b/gi, 'About');

    // Additional formal phrases to casual
    newText = newText.replace(/\bConsequently,\b/gi, 'So,');
    newText = newText.replace(/\bTherefore,\b/gi, 'So,');
    newText = newText.replace(/\bFurthermore,\b/gi, 'Also,');
    newText = newText.replace(/\bMoreover,\b/gi, 'Plus,');
    newText = newText.replace(/\bAdditionally,\b/gi, 'Also,');
    newText = newText.replace(/\bHowever,\b/gi, 'But,');
    newText = newText.replace(/\bNevertheless,\b/gi, 'Still,');

    // Synonym replacement using Datamuse API
    const words = newText.split(/(\s+)/); // Split by space, keeping spaces for rejoining
    const processedWords = [];

    for (const word of words) {
        if (word.trim() === '' || COMMON_WORDS_TO_SKIP.has(word.toLowerCase()) || !/^[a-zA-Z]+$/.test(word.trim())) {
            processedWords.push(word);
            continue;
        }
        // ~20% chance to attempt synonym replacement for eligible words
        if (Math.random() < 0.2) {
            const synonyms = await getSynonyms(word.trim().toLowerCase());
            if (synonyms.length > 0) {
                const chosenSynonym = synonyms[Math.floor(Math.random() * Math.min(synonyms.length, 3))]; // Pick from top 3
                processedWords.push(preserveCase(word, chosenSynonym));
            } else {
                processedWords.push(word); // No synonyms found or error
            }
        } else {
            processedWords.push(word);
        }
    }
    newText = processedWords.join('');

    // Enhanced sentence shuffling and restructuring (if 2+ sentences, 50% chance)
    // Process each paragraph separately to preserve paragraph breaks
    const paragraphs = newText.split(/\n\s*\n/);
    const processedParagraphs = paragraphs.map(paragraph => {
        if (!paragraph.trim()) return paragraph;

        const sentences = paragraph.split(/(?<=[.!?])\s+/); // Split by sentence-ending punctuation
        if (sentences.length >= 3 && Math.random() < 0.2) {
            // Conservative shuffling - swap only one pair occasionally
            for (let i = 0; i < 1; i++) {
                const idx1 = Math.floor(Math.random() * sentences.length);
                let idx2 = Math.floor(Math.random() * sentences.length);
                while (idx2 === idx1) { // Ensure different indices
                    idx2 = Math.floor(Math.random() * sentences.length);
                }
                // Swap two sentences
                [sentences[idx1], sentences[idx2]] = [sentences[idx2], sentences[idx1]];
            }
        }
        return sentences.join(' '); // Rejoin sentences within paragraph
    });
    newText = processedParagraphs.join('\n\n'); // Rejoin paragraphs with double newlines

    // Add some connecting words between sentences for better flow (avoid paragraph breaks)
    newText = newText.replace(/\.(\s)([A-Z])/g, (match, space, letter) => {
        // Don't modify if this is a paragraph break (multiple newlines)
        if (space.includes('\n')) {
            return match;
        }
        const connectors = ['. Additionally, ', '. Furthermore, ', '. Moreover, ', '. Also, ', '. '];
        const randomConnector = connectors[Math.floor(Math.random() * connectors.length)];
        return Math.random() < 0.2 ? randomConnector + letter.toLowerCase() : match;
    });

    return newText;
};

/**
 * 2. Controlled Grammar Mistakes / Human-like Anomalies
 * Introduces subtle, common typos or grammatical variations with low probability.
 * @param {string} text - The input text.
 * @returns {string} The text with potential controlled mistakes.
 */
export const addControlledMistakes = (text) => {
    let newText = text;

    // More noticeable word variations and natural human patterns

    // Common word variations (higher chance for more noticeable changes)
    newText = newText.replace(/\bwhich\b/g, (match) => (Math.random() < 0.15 ? preserveCase(match, 'that') : match));
    newText = newText.replace(/\bthat\b/g, (match) => (Math.random() < 0.10 ? preserveCase(match, 'which') : match));

    // Contraction variations
    newText = newText.replace(/\bcannot\b/g, (match) => (Math.random() < 0.3 ? preserveCase(match, "can't") : match));
    newText = newText.replace(/\bdo not\b/g, (match) => (Math.random() < 0.3 ? preserveCase(match, "don't") : match));
    newText = newText.replace(/\bwill not\b/g, (match) => (Math.random() < 0.3 ? preserveCase(match, "won't") : match));
    newText = newText.replace(/\bshould not\b/g, (match) => (Math.random() < 0.3 ? preserveCase(match, "shouldn't") : match));

    // Reverse some contractions for variety
    newText = newText.replace(/\bcan't\b/g, (match) => (Math.random() < 0.2 ? preserveCase(match, "cannot") : match));
    newText = newText.replace(/\bdon't\b/g, (match) => (Math.random() < 0.2 ? preserveCase(match, "do not") : match));

    // Number variations
    newText = newText.replace(/\b1\b/g, (match) => (Math.random() < 0.4 ? 'one' : match));
    newText = newText.replace(/\b2\b/g, (match) => (Math.random() < 0.4 ? 'two' : match));
    newText = newText.replace(/\b3\b/g, (match) => (Math.random() < 0.4 ? 'three' : match));
    newText = newText.replace(/\bone\b/g, (match) => (Math.random() < 0.3 ? '1' : match));
    newText = newText.replace(/\btwo\b/g, (match) => (Math.random() < 0.3 ? '2' : match));
    newText = newText.replace(/\bthree\b/g, (match) => (Math.random() < 0.3 ? '3' : match));

    // Subtle typos (reduced frequency but still present)
    newText = newText.replace(/\bthe\b/g, (match) => (Math.random() < 0.02 ? preserveCase(match, 'teh') : match));
    newText = newText.replace(/\breceive\b/g, (match) => (Math.random() < 0.02 ? preserveCase(match, 'recieve') : match));

    // Homophone confusion (slightly increased for more noticeable effect)
    if (Math.random() < 0.03) newText = newText.replace(/\byour\b/g, (match) => preserveCase(match, "you're"));
    else if (Math.random() < 0.03) newText = newText.replace(/\byou're\b/g, (match) => preserveCase(match, "your"));

    // Punctuation variations (more aggressive)
    if (Math.random() < 0.2) {
        newText = newText.replace(/, and\b/g, ' and');
        newText = newText.replace(/, but\b/g, ' but');
        newText = newText.replace(/, or\b/g, ' or');
    }

    // Add some natural pauses
    if (Math.random() < 0.15) {
        newText = newText.replace(/\. ([A-Z])/g, '. However, $1');
    }

    return newText;
};

/**
 * 3. Style Optimization
 * - Randomly expands/contracts common phrases.
 * - Attempts simple sentence length variation by removing parenthetical phrases.
 * - Attempts simple active/passive voice toggling.
 * @param {string} text - The input text.
 * @returns {string} The text with style modifications.
 */
export const changeStyle = (text) => {
    let newText = text;

    // Contraction/Expansion (randomly)
    if (Math.random() < 0.2) newText = newText.replace(/\bit is\b/gi, (match) => preserveCase(match, "it's"));
    else if (Math.random() < 0.2) newText = newText.replace(/\bIt is\b/g, (match) => preserveCase(match, "It's"));
    if (Math.random() < 0.15) newText = newText.replace(/\byou are\b/gi, (match) => preserveCase(match, "you're"));
    else if (Math.random() < 0.15) newText = newText.replace(/\bYou are\b/g, (match) => preserveCase(match, "You're"));
    if (Math.random() < 0.1) newText = newText.replace(/\bdo not\b/gi, (match) => preserveCase(match, "don't"));
    else if (Math.random() < 0.1) newText = newText.replace(/\bDo not\b/g, (match) => preserveCase(match, "Don't"));


    // Sentence Length Variation: Remove a simple parenthetical phrase (15% chance if pattern found)
    if (Math.random() < 0.15) {
        newText = newText.replace(/\s*\([^)]*\)\s*/, ' '); // Removes content in ()
        newText = newText.replace(/\s*--[^--]*--\s*/, ' '); // Removes content in -- --
        newText = newText.replace(/\s*, which also [^,.]*,/, ','); // Removes ", which also ..., "
    }
    // Clean up multiple spaces but preserve paragraph breaks (newlines)
    newText = newText.replace(/[ \t]{2,}/g, ' '); // Only collapse spaces and tabs, not newlines

    // Active/Passive Toggle (Simplified, rule-based, 10% chance per sentence if pattern matches)
    // This is highly experimental and rule-based. Process paragraphs separately.
    const paragraphs = newText.split(/\n\s*\n/);
    const processedParagraphs = paragraphs.map(paragraph => {
        if (!paragraph.trim()) return paragraph;

        const sentences = paragraph.split(/(?<=[.!?])\s+/);
        const modifiedSentences = sentences.map(sentence => {
            if (Math.random() < 0.1) {
                // Simple Active to Passive: "The Subject verb the object." -> "The object was verbed by the Subject."
                // Regex for "The Subject verb(ed) the object." - very specific
                const activeMatch = sentence.match(/^(The\s+[A-Za-z]+\s+)([a-zA-Z]+ed)(\s+the\s+[A-Za-z]+)\.$/);
                if (activeMatch) {
                    // This is a naive split, assumes single word subject/object "The cat chased the mouse."
                    const subjectPart = activeMatch[1].trim(); // "The cat"
                    const verb = activeMatch[2].trim(); // "chased"
                    const objectPart = activeMatch[3].trim(); // "the mouse"
                    // This construction is also naive and may not always be grammatically correct for all verbs
                    return `${objectPart.charAt(0).toUpperCase() + objectPart.slice(1)} was ${verb} by ${subjectPart.toLowerCase()}.`;
                }

                // Simple Passive to Active: "The Object was verbed by the Subject." -> "The Subject verbed the Object."
                const passiveMatch = sentence.match(/^(The\s+[A-Za-z]+\s+)(was\s+[a-zA-Z]+ed)(\s+by\s+the\s+[A-Za-z]+)\.$/);
                if (passiveMatch) {
                    const objectPart = passiveMatch[1].trim(); // "The mouse"
                    const verbPhrase = passiveMatch[2].trim().split(" ")[1]; // "chased" from "was chased"
                    const subjectPart = passiveMatch[3].replace("by ", "").trim(); // "the cat"
                    return `${subjectPart.charAt(0).toUpperCase() + subjectPart.slice(1)} ${verbPhrase} ${objectPart.toLowerCase()}.`;
                }
            }
            return sentence;
        });
        return modifiedSentences.join(' ');
    });
    newText = processedParagraphs.join('\n\n');

    return newText;
};


// --- More Advanced (Placeholder - requires significant R&D or libraries) ---
// This function remains a placeholder as implementing true LLM paraphrasing is out of scope.
export const smartParaphraseLLM = async (text) => {
    console.warn("smartParaphraseLLM is not implemented. Using simple version.");
    return simpleParaphrase(text); // Fallback to the enhanced simpleParaphrase
};
