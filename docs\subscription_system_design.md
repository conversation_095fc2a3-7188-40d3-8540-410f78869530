# Subscription System Design & Stripe Integration (Conceptual)

This document outlines the logic for managing user subscriptions and the conceptual integration with <PERSON>e as the payment provider for the AI Text Modifier application.

## Overview

The system will enable users to upgrade from a Freemium tier to a Premium tier by subscribing through Stripe. <PERSON>e will securely handle all payment processing. Our application will then manage user subscription statuses and gate feature access based on information primarily received from <PERSON><PERSON> via webhooks. This ensures that our application logic is synchronized with the actual payment and subscription states managed by <PERSON>e.

## Current Implementation Status

*   **Stripe SDK Initialization:**
    *   **Server-side:** The Stripe Node.js SDK is installed, and `src/lib/stripe.js` initializes and exports the Stripe instance using the `STRIPE_SECRET_KEY` and a pinned API version. This utility includes checks for the presence of the secret key.
    *   **Client-side:** The `@stripe/stripe-js` library is installed, and `src/lib/stripe-client.js` provides `getStripePromise` to load Stripe.js with the `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`.
*   **Backend API Routes:** The core backend API routes for Stripe integration have their Node.js code implemented:
    *   `src/pages/api/stripe/create-checkout-session.js`: Handles creation of Stripe Checkout sessions.
    *   `src/pages/api/stripe/webhook.js`: Handles incoming webhook events from Stripe to synchronize subscription states.
    *   `src/pages/api/stripe/create-customer-portal-session.js`: Handles creation of Stripe Customer Portal sessions.
*   **Frontend Integration:**
    *   Client-side JavaScript logic to call the backend APIs for creating Stripe Checkout and Customer Portal sessions, and then redirecting to Stripe using Stripe.js (for Checkout) or direct URL redirection (for Customer Portal), has been **implemented** in:
        *   `src/pages/pricing.js`: For initiating new subscriptions and managing subscriptions if the user is already premium.
        *   `src/pages/client-protected.js`: For initiating customer portal sessions from the user dashboard.
    *   UI elements on these pages, as well as on `src/pages/index.js` and in `src/components/auth/AuthButtons.js` (Navbar), dynamically change based on the user's fetched or session-based subscription status.
*   **Critical Next Steps (User/Developer Responsibility):**
    *   **Stripe Account Configuration:** A fully configured Stripe account is required with:
        *   Defined products (e.g., "Premium Plan").
        *   Corresponding Price IDs (e.g., for monthly/yearly billing, to be used in `PREMIUM_PLAN_PRICE_ID` and other related environment variables).
        *   A configured webhook endpoint in the Stripe dashboard pointing to the deployed `/api/stripe/webhook` route, listening for essential events.
    *   **Environment Variables:** All necessary Stripe-related environment variables (`NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`, `STRIPE_SECRET_KEY`, `STRIPE_WEBHOOK_SECRET`, `PREMIUM_PLAN_PRICE_ID`, etc.) must be correctly set in `.env.local` (for local testing) and in the deployment environment.
    *   **Thorough End-to-End Testing:** Crucially, comprehensive testing of the entire subscription lifecycle (new subscription, payment success/failure, renewal, cancellation, portal management, webhook handling, and UI updates) is **pending active Stripe configuration and execution by the developer/user.**

## Core User Flows & Stripe Interactions

### 1. User Subscribes to Premium (Stripe Checkout)

This flow describes how a user initiates and completes a new subscription to a premium plan.

*   **Trigger:** User clicks an "Upgrade to Premium," "Subscribe," or similar button on the application's frontend.
*   **Frontend Action (Client-Side):** Makes an authenticated API request to `POST /api/stripe/create-checkout-session`.
    *   **Status:** Implemented in `src/pages/pricing.js` within the `handleUpgradeClick` function.
*   **Backend API Endpoint (`/api/stripe/create-checkout-session`):**
    *   **Status:** Implemented in `src/pages/api/stripe/create-checkout-session.js`.
    *   **Key Logic:** Authenticates user, retrieves/creates Stripe Customer ID (saving to local DB `User` record), creates Stripe Checkout Session, returns `sessionId`.
*   **Frontend Action (Client-Side - Using Stripe.js):**
    *   **Status:** Implemented in `src/pages/pricing.js` within `handleUpgradeClick`.
    *   **Key Logic:** Initializes Stripe.js (via `getStripePromise` from `src/lib/stripe-client.js`), then uses the received `sessionId` to redirect the user to Stripe Checkout: `stripe.redirectToCheckout({ sessionId })`. Includes error handling and UI updates for loading states.

### 2. Stripe Webhooks for Fulfillment & Status Updates

Webhooks are essential for Stripe to asynchronously notify our application about events.

*   **Webhook Endpoint (`POST /api/stripe/webhook`):**
    *   **Status:** Implemented in `src/pages/api/stripe/webhook.js`.
    *   **Key Logic:** Verifies Stripe signature, handles `checkout.session.completed`, `invoice.payment_succeeded`, `invoice.payment_failed`, `customer.subscription.updated`, and `customer.subscription.deleted` events by updating local `Subscription` and `User` database records.
*   **(Detailed event handling logic as previously defined in this document)...**

### 3. User Manages Subscription (Stripe Customer Portal)

Stripe provides a Customer Portal for users to manage their subscriptions.

*   **Trigger:** User clicks a "Manage Subscription" button.
*   **Frontend Action (Client-Side):** Makes an authenticated API request to `POST /api/stripe/create-customer-portal-session`.
    *   **Status:** Implemented in `src/pages/client-protected.js` (within `handleManageSubscription`) and `src/pages/pricing.js` (for already premium users, via `handleManageSubscriptionClick`).
*   **Backend API Endpoint (`/api/stripe/create-customer-portal-session`):**
    *   **Status:** Implemented in `src/pages/api/stripe/create-customer-portal-session.js`.
    *   **Key Logic:** Authenticates user, retrieves `stripeCustomerId`, creates Stripe Billing Portal Session, returns portal session `url`.
*   **Frontend Action (Client-Side):**
    *   **Status:** Implemented in `src/pages/client-protected.js` and `src/pages/pricing.js`.
    *   **Key Logic:** Redirects the user to the Stripe Customer Portal URL: `window.location.href = portalSessionUrl;`. Includes error handling and UI updates for loading states.

## Testing Stripe Integration

Thorough testing is critical for ensuring the subscription system works correctly and reliably. **This requires a configured Stripe account (in test mode) and properly set environment variables.**

*   **Stripe Test Mode:**
    *   Always use Stripe's **test mode** during development.
    *   Use your Stripe test API keys (`pk_test_...` and `sk_test_...`) in your `.env.local` file.
*   **Test Card Numbers:**
    *   Stripe provides a range of [test card numbers](https://stripe.com/docs/testing) for various scenarios.
*   **Products and Prices in Stripe Test Mode:**
    *   Create products (e.g., "Premium Plan") and corresponding prices (e.g., monthly, yearly) in your Stripe **test environment dashboard**. The IDs of these prices must match those used in your environment variables (e.g., `PREMIUM_PLAN_PRICE_ID`).
*   **Webhook Testing:**
    *   **Stripe CLI:** Essential for local webhook testing.
        *   Install the [Stripe CLI](https://stripe.com/docs/stripe-cli).
        *   Log in: `stripe login`.
        *   Forward events to your local server:
          ```bash
          stripe listen --forward-to localhost:3000/api/stripe/webhook
          ```
          (Adjust port if needed). This will provide a webhook secret for your local testing (`STRIPE_WEBHOOK_SECRET`).
        *   Trigger test events: `stripe trigger checkout.session.completed --add customer=cus_YOUR_TEST_CUSTOMER_ID` (or other relevant events).
    *   **Stripe Dashboard:** View event logs and manually resend events in the Stripe test dashboard.
*   **Key Frontend and End-to-End Test Scenarios:**
    *   **Subscription Flow:**
        *   Click "Upgrade to Premium" on `pricing.js` as a logged-in free user. Verify redirection to Stripe Checkout.
        *   Complete a test payment using Stripe's test card numbers.
        *   Verify redirection to the `success_url` (e.g., `/profile?subscribe=success...`).
        *   Check if the UI (e.g., Navbar badge, `client-protected.js` dashboard, `index.js` status) updates to reflect "premium" status after a short delay (allowing for webhook processing).
    *   **Customer Portal Access:**
        *   As a premium user, click "Manage Subscription" (on `pricing.js` or `client-protected.js`).
        *   Verify redirection to the Stripe Customer Portal.
        *   (In Stripe Portal) Test actions like attempting to cancel or update card details (if Stripe test mode allows).
    *   **Webhook Data Verification:**
        *   After test events (e.g., `checkout.session.completed`, `customer.subscription.deleted` triggered via portal or Stripe CLI), inspect your local database (`User` and `Subscription` tables) to ensure records are created/updated correctly by the webhook handler.
    *   **UI States for Different Users:**
        *   Verify the `pricing.js` page displays correct buttons/text for:
            *   Logged-out users.
            *   Logged-in free users.
            *   Logged-in premium users.
        *   Verify `client-protected.js` correctly gates premium UI sections and the "Manage Subscription" button.
        *   Verify `index.js` status box and ad display logic based on subscription tier.
    *   **Error Handling:**
        *   Test scenarios where API calls might fail (e.g., temporarily stop the Next.js backend API for creating checkout session and see how frontend handles it).
        *   Test with incorrect or missing Stripe Price IDs (if possible to simulate).
        *   Verify webhook signature verification by sending a mock request to your webhook endpoint without a valid signature (should be rejected).

## Environment Variables Needed for Stripe:
(This section remains the same)
*   `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`
*   `STRIPE_SECRET_KEY`
*   `STRIPE_WEBHOOK_SECRET`
*   `PREMIUM_PLAN_PRICE_ID`

## Important Considerations:
(This section remains the same, with "Testing" now more detailed above)
*   Idempotency
*   Security
*   Error Handling
*   User Feedback
*   Plan Management

This conceptual design provides a foundation for integrating Stripe for subscription management. The actual implementation will involve careful coding of these flows, thorough testing, and adherence to Stripe's best practices.

## Feature Gating Mechanism
(This section remains the same)
Feature gating is the process of controlling user access to specific features or functionalities based on their subscription status or other criteria. This is crucial for managing Freemium vs. Premium tier benefits.
... (content as previously defined) ...
