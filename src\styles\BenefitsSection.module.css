.benefitsSection {
  padding: 5rem 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.benefitsSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>') repeat;
  opacity: 0.3;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
  z-index: 1;
}

.benefitsContainer {
  margin-bottom: 6rem;
}

.useCasesContainer {
  margin-bottom: 6rem;
}

.header {
  text-align: center;
  margin-bottom: 4rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.8;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.benefitsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.benefitCard {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.benefitCard:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.benefitIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.benefitTitle {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
}

.benefitDescription {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.benefitStats {
  font-size: 0.9rem;
  color: #fbbf24;
  font-weight: 600;
  background: rgba(251, 191, 36, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: inline-block;
}

.useCasesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.useCaseCard {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.useCaseCard:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.useCaseHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.useCaseIcon {
  font-size: 2rem;
}

.useCaseTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin: 0;
}

.examplesList {
  list-style: none;
  padding: 0;
  margin: 0 0 1.5rem 0;
}

.example {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  opacity: 0.9;
}

.checkmark {
  color: #10b981;
  font-weight: bold;
  font-size: 1rem;
}

.useCaseBenefit {
  background: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  padding: 1rem;
  font-size: 0.9rem;
  line-height: 1.5;
}

.useCaseBenefit strong {
  color: #a5b4fc;
}

.statsSection {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
}

.statsTitle {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 3rem;
  color: white;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.statItem {
  text-align: center;
}

.statNumber {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  display: block;
}

.statLabel {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.5rem;
}

.statDetail {
  font-size: 0.9rem;
  opacity: 0.7;
  color: #e2e8f0;
}

@media (max-width: 768px) {
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .benefitsGrid,
  .useCasesGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .benefitCard,
  .useCaseCard {
    padding: 1.5rem;
  }
  
  .statsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .statNumber {
    font-size: 2rem;
  }
  
  .statsSection {
    padding: 2rem 1rem;
  }
  
  .statsTitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
  }
}
