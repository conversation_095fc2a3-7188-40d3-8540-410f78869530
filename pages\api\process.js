// Import enhanced humanization services
import { humanizeText } from '../../src/services/humaneyesService';
import { checkWithGPTZero } from '../../src/services/gptzeroClient';
import { addControlledMistakes, changeStyle, simpleParaphrase } from '../../src/utils/textModifiers';
import { paraphraseWithPegasus } from '../../src/services/paraphraseService';
import { balancedHumanization, qualityCheck } from '../../src/utils/balancedHumanizer';

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    const { text, styleProfile, styleStrength } = req.body;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return res.status(400).json({ message: 'Input text is required and must be a non-empty string.' });
    }

    // Validate style parameters if provided
    if (styleStrength !== undefined && (typeof styleStrength !== 'number' || styleStrength < 0 || styleStrength > 100)) {
        return res.status(400).json({ message: 'Style strength must be a number between 0 and 100.' });
    }

    let modifiedText = text; // Start with the original text

    try {
        const startTime = Date.now();

        // --- Step 1: Enhanced Humanization with Advanced LLMs ---
        console.log("Starting enhanced humanization with advanced models...");

        try {
            // Calculate target aggressiveness based on style strength
            const baseAggressiveness = 0.7;
            const adjustedAggressiveness = styleStrength ?
                Math.min(0.95, baseAggressiveness + (styleStrength / 100) * 0.3) :
                baseAggressiveness;

            const humanizationResult = await humanizeText(modifiedText, {
                aggressiveness: adjustedAggressiveness,
                maintainTone: true,
                targetDetection: 10, // Target ≤10% AI detection
                method: 'auto', // Use advanced LLMs with fallback
                fallbackEnabled: true
            });

            if (humanizationResult.success) {
                modifiedText = humanizationResult.text || humanizationResult.humanizedText;
                const processingTime = Date.now() - startTime;
                console.log(`Successfully humanized with ${humanizationResult.actualMethod} method in ${processingTime}ms`);
                console.log('DEBUG: modifiedText after extraction:', {
                    type: typeof modifiedText,
                    length: modifiedText ? modifiedText.length : 'N/A',
                    preview: modifiedText ? modifiedText.substring(0, 100) + '...' : 'null/undefined'
                });

                // Log model information if available
                if (humanizationResult.model) {
                    console.log(`Model used: ${humanizationResult.model}${humanizationResult.provider ? ` (${humanizationResult.provider})` : ''}`);
                }
            } else {
                console.warn(`Enhanced humanization failed: ${humanizationResult.error}. Using fallback methods.`);

                // Fallback to legacy paraphrasing
                const paraphraseResult = await paraphraseWithPegasus(modifiedText);
                if (paraphraseResult && !paraphraseResult.error) {
                    modifiedText = paraphraseResult.paraphrased_text;
                    console.log("Fallback paraphrasing completed.");
                } else {
                    modifiedText = await simpleParaphrase(modifiedText);
                    console.log("Simple paraphrasing fallback completed.");
                }
            }
        } catch (error) {
            console.error('Humanization error:', error.message);

            // Final fallback to simple paraphrasing
            console.log("Applying simple paraphrasing as final fallback...");
            modifiedText = await simpleParaphrase(modifiedText);
        }

        // --- Step 2: Apply enhanced balanced humanization with optional style ---
        console.log("Applying enhanced balanced humanization... [FIXED]");
        console.log('DEBUG: modifiedText before balanced humanization:', {
            type: typeof modifiedText,
            length: modifiedText ? modifiedText.length : 'N/A',
            preview: modifiedText ? modifiedText.substring(0, 100) + '...' : 'null/undefined'
        });
        if (styleProfile && styleStrength > 0) {
            console.log(`Applying personal style: ${styleProfile.name} at ${styleStrength}% strength`);
            // The balancedHumanization function now handles style integration
            const result = await balancedHumanization(modifiedText, styleProfile, styleStrength, {
                useAdvanced: true,
                aggressiveness: 0.7,
                maintainTone: true
            });
            modifiedText = typeof result === 'string' ? result : await result;
        } else {
            modifiedText = await balancedHumanization(modifiedText, null, 0, {
                useAdvanced: true,
                aggressiveness: 0.7,
                maintainTone: true
            });
        }

        // --- Step 3: Quality check ---
        console.log("Performing quality check...");
        const qualityResult = qualityCheck(modifiedText);

        if (qualityResult.hasIssues) {
            console.log("Quality issues detected:", qualityResult.issues);
            // Apply minimal additional processing if needed
            modifiedText = addControlledMistakes(modifiedText);
        } else {
            console.log("Quality check passed");
        }

        // --- Step 4: Apply subtle style changes ---
        console.log("Applying subtle style changes...");
        modifiedText = changeStyle(modifiedText);

        // --- Step 5: AI Detection Check ---
        console.log("Performing AI detection check...");
        const detectionResult = await checkWithGPTZero(modifiedText);

        res.status(200).json({ modifiedText, detectionResult });

    } catch (error) {
        // This catches errors from textModifiers or other unexpected issues within this handler
        console.error("Error in /api/process main try block:", error);
        const errorMessage = error.message || 'Error processing text.';
        res.status(500).json({
            message: errorMessage,
            error: error.toString(),
            detectionResult: { // Ensure detectionResult has a consistent error structure
                error: true,
                status: "Server Error",
                message: "Failed to process text due to an internal server error in the API handler.",
                score: null
            }
        });
    }
}
