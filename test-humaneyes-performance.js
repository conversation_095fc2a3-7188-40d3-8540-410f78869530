/**
 * Humaneyes Model Performance Testing
 * Compares Humaneyes model against current enhanced system
 */

import { enhancedHumanization, humanizeWithHumaneyes, humanizeWithPegasus, getServiceStatus } from './src/services/humaneyesService.js';
import { advancedHumanization } from './src/utils/advancedHumanizer.js';
import { checkWithGPTZero } from './src/services/gptzeroClient.js';

// Test cases for performance comparison
const performanceTestCases = [
    {
        name: "Business Content",
        text: "The implementation of artificial intelligence systems requires careful planning and substantial investment. Many organizations are finding that the results justify the costs and provide significant benefits for their operations.",
        category: "business"
    },
    {
        name: "Technical Documentation",
        text: "The API uses JSON for data exchange and supports standard HTTP methods. Authentication is handled via OAuth 2.0 tokens with rate limiting enforced at 1000 requests per hour.",
        category: "technical"
    },
    {
        name: "Academic Writing",
        text: "Recent studies demonstrate that artificial intelligence implementation results in significant improvements in productivity metrics. The research indicates that customer satisfaction scores improve substantially when AI-powered systems are deployed.",
        category: "academic"
    },
    {
        name: "Marketing Content",
        text: "Transform your business with advanced AI solutions. Our cutting-edge platform delivers exceptional results that drive real business value and provide competitive advantages.",
        category: "marketing"
    },
    {
        name: "Formal Document",
        text: `I. Executive Summary

The quarterly analysis reveals significant improvements in operational efficiency. Our implementation of advanced technologies has resulted in measurable benefits.

II. Key Findings

A. Revenue increased by 15%
B. Customer satisfaction improved by 12%`,
        category: "formal"
    }
];

/**
 * Run comprehensive performance comparison
 */
async function runPerformanceComparison() {
    console.log('🚀 HUMANEYES MODEL PERFORMANCE COMPARISON');
    console.log('═'.repeat(70));
    
    // Check service status
    const status = getServiceStatus();
    console.log('\n📊 Service Status:');
    console.log(`   Hugging Face Available: ${status.huggingFaceAvailable ? '✅' : '❌'}`);
    console.log(`   API Key Configured: ${status.apiKeyConfigured ? '✅' : '❌'}`);
    console.log(`   Humaneyes Model: ${status.models.humaneyes.available ? '✅' : '❌'}`);
    console.log(`   Pegasus Model: ${status.models.pegasus.available ? '✅' : '❌'}`);
    
    if (!status.huggingFaceAvailable) {
        console.log('\n⚠️  Hugging Face service not available. Testing local methods only.');
    }
    
    const results = [];
    
    for (const testCase of performanceTestCases) {
        console.log(`\n📝 Testing: ${testCase.name} (${testCase.category})`);
        console.log('─'.repeat(50));
        
        const testResult = await compareModels(testCase);
        results.push(testResult);
        
        displayTestResults(testResult);
    }
    
    // Generate comprehensive report
    generateComparisonReport(results);
}

/**
 * Compare different humanization models on a single test case
 */
async function compareModels(testCase) {
    const { text, name, category } = testCase;
    const models = [];
    
    // Test Local Advanced Humanization (baseline)
    console.log('   🔧 Testing Local Advanced...');
    try {
        const startTime = Date.now();
        const localResult = advancedHumanization(text, { aggressiveness: 0.7 });
        const localTime = Date.now() - startTime;
        
        models.push({
            name: 'Local Advanced',
            success: true,
            result: localResult,
            processingTime: localTime,
            model: 'local-advanced'
        });
    } catch (error) {
        models.push({
            name: 'Local Advanced',
            success: false,
            error: error.message,
            model: 'local-advanced'
        });
    }
    
    // Test Humaneyes Model (if available)
    if (getServiceStatus().huggingFaceAvailable) {
        console.log('   🤖 Testing Humaneyes...');
        try {
            const humaneyesResult = await humanizeWithHumaneyes(text, {
                maxLength: 512,
                temperature: 1.2,
                numBeams: 5
            });
            
            models.push({
                name: 'Humaneyes',
                success: humaneyesResult.success,
                result: humaneyesResult.humanizedText,
                processingTime: humaneyesResult.processingTime,
                error: humaneyesResult.error,
                model: 'humaneyes'
            });
        } catch (error) {
            models.push({
                name: 'Humaneyes',
                success: false,
                error: error.message,
                model: 'humaneyes'
            });
        }
        
        // Test Pegasus Paraphrase (if available)
        console.log('   📝 Testing Pegasus...');
        try {
            const pegasusResult = await humanizeWithPegasus(text, {
                maxLength: 60,
                numBeams: 10,
                temperature: 1.5
            });
            
            models.push({
                name: 'Pegasus',
                success: pegasusResult.success,
                result: pegasusResult.humanizedText,
                processingTime: pegasusResult.processingTime,
                error: pegasusResult.error,
                model: 'pegasus'
            });
        } catch (error) {
            models.push({
                name: 'Pegasus',
                success: false,
                error: error.message,
                model: 'pegasus'
            });
        }
    }
    
    // Test AI detection for successful results
    for (const model of models) {
        if (model.success && model.result) {
            try {
                const detectionResult = await checkWithGPTZero(model.result);
                model.aiDetectionScore = Math.round((detectionResult.ai_probability || 0) * 100);
            } catch (error) {
                model.aiDetectionError = error.message;
            }
        }
    }
    
    return {
        testCase: { name, category },
        originalText: text,
        models,
        timestamp: new Date().toISOString()
    };
}

/**
 * Display results for a single test
 */
function displayTestResults(testResult) {
    const { testCase, models } = testResult;
    
    console.log(`\n   📊 Results for ${testCase.name}:`);
    
    models.forEach(model => {
        const status = model.success ? '✅' : '❌';
        console.log(`\n   ${status} ${model.name}:`);
        
        if (model.success) {
            console.log(`      ⏱️  Processing Time: ${model.processingTime}ms`);
            console.log(`      📏 Output Length: ${model.result.length} chars`);
            
            if (model.aiDetectionScore !== undefined) {
                const scoreStatus = model.aiDetectionScore <= 20 ? '✅' : '⚠️';
                console.log(`      🤖 AI Detection: ${model.aiDetectionScore}% ${scoreStatus}`);
            } else if (model.aiDetectionError) {
                console.log(`      🤖 AI Detection: ⚠️ ${model.aiDetectionError}`);
            }
            
            console.log(`      📄 Sample: "${model.result.substring(0, 80)}..."`);
        } else {
            console.log(`      ❌ Error: ${model.error}`);
        }
    });
}

/**
 * Generate comprehensive comparison report
 */
function generateComparisonReport(results) {
    console.log('\n' + '═'.repeat(70));
    console.log('📈 COMPREHENSIVE PERFORMANCE REPORT');
    console.log('═'.repeat(70));
    
    // Calculate statistics
    const stats = {
        totalTests: results.length,
        modelStats: {}
    };
    
    // Initialize model stats
    const modelNames = ['Local Advanced', 'Humaneyes', 'Pegasus'];
    modelNames.forEach(name => {
        stats.modelStats[name] = {
            successCount: 0,
            totalTests: 0,
            avgProcessingTime: 0,
            avgDetectionScore: 0,
            detectionTests: 0,
            processingTimes: []
        };
    });
    
    // Collect statistics
    results.forEach(result => {
        result.models.forEach(model => {
            const modelStat = stats.modelStats[model.name];
            if (modelStat) {
                modelStat.totalTests++;
                
                if (model.success) {
                    modelStat.successCount++;
                    modelStat.processingTimes.push(model.processingTime);
                    
                    if (model.aiDetectionScore !== undefined) {
                        modelStat.avgDetectionScore += model.aiDetectionScore;
                        modelStat.detectionTests++;
                    }
                }
            }
        });
    });
    
    // Calculate averages
    Object.values(stats.modelStats).forEach(modelStat => {
        if (modelStat.processingTimes.length > 0) {
            modelStat.avgProcessingTime = modelStat.processingTimes.reduce((a, b) => a + b, 0) / modelStat.processingTimes.length;
        }
        if (modelStat.detectionTests > 0) {
            modelStat.avgDetectionScore = modelStat.avgDetectionScore / modelStat.detectionTests;
        }
    });
    
    // Display report
    console.log(`\n🎯 Overall Performance Summary:`);
    console.log(`   Total Test Cases: ${stats.totalTests}`);
    
    console.log(`\n📊 Model Comparison:`);
    
    modelNames.forEach(modelName => {
        const stat = stats.modelStats[modelName];
        if (stat.totalTests > 0) {
            const successRate = (stat.successCount / stat.totalTests * 100).toFixed(1);
            console.log(`\n   🔧 ${modelName}:`);
            console.log(`      Success Rate: ${successRate}% (${stat.successCount}/${stat.totalTests})`);
            
            if (stat.avgProcessingTime > 0) {
                console.log(`      Avg Processing Time: ${stat.avgProcessingTime.toFixed(0)}ms`);
            }
            
            if (stat.detectionTests > 0) {
                const avgScore = stat.avgDetectionScore.toFixed(1);
                const targetMet = stat.avgDetectionScore <= 20 ? '✅' : '⚠️';
                console.log(`      Avg AI Detection: ${avgScore}% ${targetMet}`);
            }
        }
    });
    
    // Recommendations
    console.log(`\n💡 Recommendations:`);
    
    const localStat = stats.modelStats['Local Advanced'];
    const humaneyesStat = stats.modelStats['Humaneyes'];
    
    if (humaneyesStat.totalTests === 0) {
        console.log('   • Configure Hugging Face API key to test Humaneyes model');
        console.log('   • Current local advanced humanization is performing well');
    } else if (humaneyesStat.successCount > 0 && localStat.successCount > 0) {
        if (humaneyesStat.avgDetectionScore < localStat.avgDetectionScore) {
            console.log('   • Humaneyes model shows better AI detection scores');
            console.log('   • Consider using Humaneyes as primary with local fallback');
        } else {
            console.log('   • Local advanced humanization is competitive');
            console.log('   • Current system provides excellent performance');
        }
        
        if (humaneyesStat.avgProcessingTime > localStat.avgProcessingTime * 10) {
            console.log('   • Local processing is significantly faster');
            console.log('   • Consider hybrid approach for performance optimization');
        }
    }
    
    console.log('\n🎉 Performance comparison completed!');
}

// Run the performance comparison
if (import.meta.url === `file://${process.argv[1]}`) {
    runPerformanceComparison().catch(console.error);
}

export { runPerformanceComparison, compareModels };
