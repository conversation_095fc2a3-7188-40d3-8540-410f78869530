/**
 * Style Application Service
 * Applies user's writing style to humanized text
 */

import { balancedHumanization } from '../utils/balancedHumanizer.js';

/**
 * Applies user's writing style to text with specified strength
 * @param {string} text - Text to apply style to
 * @param {Object} styleProfile - User's style profile from analyzeWritingStyle
 * @param {number} styleStrength - Style strength (0-100)
 * @returns {string} Text with applied style
 */
export function applyWritingStyle(text, styleProfile, styleStrength = 50) {
    if (!text || !styleProfile) {
        return text;
    }

    // Normalize style strength to 0-1 range
    const strength = Math.max(0, Math.min(100, styleStrength)) / 100;
    
    let result = text;
    
    // Step 1: Apply balanced humanization first
    result = balancedHumanization(result);
    
    // Step 2: Apply style-specific modifications based on strength
    if (strength > 0) {
        result = applySentencePatterns(result, styleProfile.sentencePatterns, strength);
        result = applyVocabularyStyle(result, styleProfile.vocabularyComplexity, strength);
        result = applyTransitionPhrases(result, styleProfile.transitionPhrases, strength);
        result = applyPunctuationStyle(result, styleProfile.punctuationStyle, strength);
        result = applyPersonalExpressions(result, styleProfile.personalExpressions, strength);
        result = applyWritingQuirks(result, styleProfile.writingQuirks, strength);
        result = applyToneAdjustments(result, styleProfile.toneAnalysis, strength);
    }
    
    return result;
}

/**
 * Applies sentence pattern preferences
 */
function applySentencePatterns(text, patterns, strength) {
    if (!patterns || strength === 0) return text;
    
    const sentences = text.split(/(?<=[.!?])\s+/);
    const targetAvgLength = patterns.averageLength;
    const preferredStarters = patterns.preferredStarters || [];
    
    const modifiedSentences = sentences.map((sentence, index) => {
        let modifiedSentence = sentence;
        
        // Apply sentence length adjustments based on user's preference
        if (Math.random() < strength * 0.3) {
            const currentLength = sentence.length;
            
            if (patterns.complexity === 'complex' && currentLength < targetAvgLength) {
                // Add complexity for users who prefer longer sentences
                modifiedSentence = addSentenceComplexity(sentence);
            } else if (patterns.complexity === 'simple' && currentLength > targetAvgLength) {
                // Simplify for users who prefer shorter sentences
                modifiedSentence = simplifySentence(sentence);
            }
        }
        
        // Apply preferred sentence starters
        if (Math.random() < strength * 0.2 && preferredStarters.length > 0) {
            const randomStarter = preferredStarters[Math.floor(Math.random() * preferredStarters.length)];
            if (!modifiedSentence.toLowerCase().startsWith(randomStarter)) {
                // Only apply if it makes grammatical sense
                if (canApplyStarter(modifiedSentence, randomStarter)) {
                    modifiedSentence = applyStarter(modifiedSentence, randomStarter);
                }
            }
        }
        
        return modifiedSentence;
    });
    
    return modifiedSentences.join(' ');
}

/**
 * Applies vocabulary complexity preferences
 */
function applyVocabularyStyle(text, vocabulary, strength) {
    if (!vocabulary || strength === 0) return text;
    
    let result = text;
    const preferredWords = vocabulary.preferredWords || [];
    
    // Replace words with user's preferred vocabulary
    if (preferredWords.length > 0 && Math.random() < strength * 0.4) {
        const words = result.split(/\b/);
        const modifiedWords = words.map(word => {
            if (Math.random() < strength * 0.1 && word.length > 4) {
                // Find similar preferred words
                const similarWord = findSimilarPreferredWord(word, preferredWords);
                if (similarWord) {
                    return preserveCase(word, similarWord);
                }
            }
            return word;
        });
        result = modifiedWords.join('');
    }
    
    // Adjust complexity level
    if (vocabulary.complexityLevel === 'high' && Math.random() < strength * 0.3) {
        result = increaseLexicalComplexity(result);
    } else if (vocabulary.complexityLevel === 'low' && Math.random() < strength * 0.3) {
        result = decreaseLexicalComplexity(result);
    }
    
    return result;
}

/**
 * Applies user's transition phrase preferences
 */
function applyTransitionPhrases(text, transitions, strength) {
    if (!transitions || !transitions.mostUsed || strength === 0) return text;
    
    let result = text;
    const userTransitions = transitions.mostUsed.map(t => t.phrase);
    
    // Replace generic transitions with user's preferred ones
    const genericTransitions = {
        'however': userTransitions.find(t => t.includes('however')) || 'however',
        'therefore': userTransitions.find(t => t.includes('therefore')) || 'therefore',
        'furthermore': userTransitions.find(t => t.includes('furthermore')) || 'furthermore',
        'additionally': userTransitions.find(t => t.includes('additionally')) || 'additionally'
    };
    
    Object.entries(genericTransitions).forEach(([generic, preferred]) => {
        if (Math.random() < strength * 0.4) {
            const regex = new RegExp(`\\b${generic}\\b`, 'gi');
            result = result.replace(regex, (match) => {
                return preserveCase(match, preferred);
            });
        }
    });
    
    return result;
}

/**
 * Applies punctuation style preferences
 */
function applyPunctuationStyle(text, punctuation, strength) {
    if (!punctuation || strength === 0) return text;
    
    let result = text;
    const prefs = punctuation.preferences;
    
    // Apply comma preferences
    if (prefs.commaHeavy && Math.random() < strength * 0.3) {
        result = addMoreCommas(result);
    }
    
    // Apply dash usage
    if (prefs.dashUser && Math.random() < strength * 0.2) {
        result = result.replace(/,\s+/g, (match) => {
            return Math.random() < 0.3 ? ' - ' : match;
        });
    }
    
    // Apply parenthetical usage
    if (prefs.parentheticalUser && Math.random() < strength * 0.2) {
        result = addParentheticalRemarks(result);
    }
    
    // Apply exclamation preferences
    if (prefs.exclamatory && Math.random() < strength * 0.1) {
        result = result.replace(/\./g, (match) => {
            return Math.random() < 0.1 ? '!' : match;
        });
    }
    
    return result;
}

/**
 * Applies personal expressions and markers
 */
function applyPersonalExpressions(text, expressions, strength) {
    if (!expressions || strength === 0) return text;
    
    let result = text;
    
    // Add personal markers
    if (expressions.personalMarkers.length > 0 && Math.random() < strength * 0.3) {
        const sentences = result.split(/(?<=[.!?])\s+/);
        const modifiedSentences = sentences.map((sentence, index) => {
            if (Math.random() < strength * 0.2 && index > 0) {
                const marker = expressions.personalMarkers[Math.floor(Math.random() * expressions.personalMarkers.length)];
                return `${marker.charAt(0).toUpperCase() + marker.slice(1)}, ${sentence.toLowerCase()}`;
            }
            return sentence;
        });
        result = modifiedSentences.join(' ');
    }
    
    // Add hedging language
    if (expressions.hedgingLanguage.length > 0 && Math.random() < strength * 0.2) {
        const hedges = expressions.hedgingLanguage;
        result = result.replace(/\b(is|are|was|were)\b/g, (match) => {
            if (Math.random() < 0.1) {
                const hedge = hedges[Math.floor(Math.random() * hedges.length)];
                return `${match} ${hedge}`;
            }
            return match;
        });
    }
    
    // Add intensifiers
    if (expressions.intensifiers.length > 0 && Math.random() < strength * 0.2) {
        const intensifiers = expressions.intensifiers;
        result = result.replace(/\b(good|bad|important|interesting|difficult|easy)\b/g, (match) => {
            if (Math.random() < 0.15) {
                const intensifier = intensifiers[Math.floor(Math.random() * intensifiers.length)];
                return `${intensifier} ${match}`;
            }
            return match;
        });
    }
    
    return result;
}

/**
 * Applies writing quirks
 */
function applyWritingQuirks(text, quirks, strength) {
    if (!quirks || quirks.length === 0 || strength === 0) return text;
    
    let result = text;
    
    quirks.forEach(quirk => {
        if (Math.random() < strength * 0.3) {
            switch (quirk.type) {
                case 'ellipsis_user':
                    result = result.replace(/\./g, (match) => {
                        return Math.random() < 0.05 ? '...' : match;
                    });
                    break;
                case 'double_dash_user':
                    result = result.replace(/,\s+/g, (match) => {
                        return Math.random() < 0.1 ? ' -- ' : match;
                    });
                    break;
                case 'parenthetical_heavy':
                    result = addParentheticalRemarks(result);
                    break;
            }
        }
    });
    
    return result;
}

/**
 * Applies tone adjustments
 */
function applyToneAdjustments(text, tone, strength) {
    if (!tone || strength === 0) return text;
    
    let result = text;
    
    // Apply dominant tone
    if (tone.dominantTone === 'positive' && Math.random() < strength * 0.2) {
        result = addPositiveTone(result);
    } else if (tone.dominantTone === 'negative' && Math.random() < strength * 0.1) {
        result = addCautionaryTone(result);
    }
    
    return result;
}

// Helper functions
function addSentenceComplexity(sentence) {
    const complexifiers = [
        ', which is important to consider',
        ', as this demonstrates',
        ', particularly when we think about',
        ', especially in the context of'
    ];
    const complexifier = complexifiers[Math.floor(Math.random() * complexifiers.length)];
    const insertPoint = Math.floor(sentence.length * 0.7);
    return sentence.slice(0, insertPoint) + complexifier + sentence.slice(insertPoint);
}

function simplifySentence(sentence) {
    return sentence
        .replace(/,\s+which\s+[^,]+,/g, '')
        .replace(/,\s+particularly\s+[^,]+,/g, '')
        .replace(/,\s+especially\s+[^,]+,/g, '');
}

function canApplyStarter(sentence, starter) {
    const firstWord = sentence.trim().split(/\s+/)[0].toLowerCase();
    return !['the', 'a', 'an', 'this', 'that'].includes(firstWord);
}

function applyStarter(sentence, starter) {
    return `${starter.charAt(0).toUpperCase() + starter.slice(1)}, ${sentence.toLowerCase()}`;
}

function findSimilarPreferredWord(word, preferredWords) {
    // Simple similarity check - could be enhanced with more sophisticated matching
    return preferredWords.find(preferred => 
        preferred.length === word.length || 
        Math.abs(preferred.length - word.length) <= 2
    );
}

function preserveCase(original, replacement) {
    if (original === original.toUpperCase()) {
        return replacement.toUpperCase();
    } else if (original[0] === original[0].toUpperCase()) {
        return replacement.charAt(0).toUpperCase() + replacement.slice(1);
    }
    return replacement;
}

function increaseLexicalComplexity(text) {
    const complexReplacements = {
        'use': 'utilize',
        'help': 'facilitate',
        'show': 'demonstrate',
        'make': 'create',
        'get': 'obtain'
    };
    
    let result = text;
    Object.entries(complexReplacements).forEach(([simple, complex]) => {
        const regex = new RegExp(`\\b${simple}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            return Math.random() < 0.3 ? preserveCase(match, complex) : match;
        });
    });
    
    return result;
}

function decreaseLexicalComplexity(text) {
    const simpleReplacements = {
        'utilize': 'use',
        'facilitate': 'help',
        'demonstrate': 'show',
        'obtain': 'get',
        'commence': 'start'
    };
    
    let result = text;
    Object.entries(simpleReplacements).forEach(([complex, simple]) => {
        const regex = new RegExp(`\\b${complex}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            return preserveCase(match, simple);
        });
    });
    
    return result;
}

function addMoreCommas(text) {
    return text.replace(/\s+(and|but|or|so)\s+/g, ', $1 ');
}

function addParentheticalRemarks(text) {
    const remarks = [
        '(which is interesting)',
        '(as you might expect)',
        '(in my experience)',
        '(to be honest)',
        '(if you ask me)'
    ];
    
    const sentences = text.split(/(?<=[.!?])\s+/);
    const modifiedSentences = sentences.map(sentence => {
        if (Math.random() < 0.1 && sentence.length > 30) {
            const remark = remarks[Math.floor(Math.random() * remarks.length)];
            const insertPoint = Math.floor(sentence.length * 0.6);
            return sentence.slice(0, insertPoint) + ' ' + remark + sentence.slice(insertPoint);
        }
        return sentence;
    });
    
    return modifiedSentences.join(' ');
}

function addPositiveTone(text) {
    return text.replace(/\b(good|nice|okay)\b/gi, (match) => {
        const positives = ['excellent', 'wonderful', 'fantastic'];
        return Math.random() < 0.2 ? positives[Math.floor(Math.random() * positives.length)] : match;
    });
}

function addCautionaryTone(text) {
    return text.replace(/\b(is|are)\b/g, (match) => {
        return Math.random() < 0.1 ? `${match} potentially` : match;
    });
}
