import logger from './logger';

// Analytics configuration
const ANALYTICS_CONFIG = {
  enabled: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
  googleAnalyticsId: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
  sentryDsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
};

// Google Analytics 4 integration
export const gtag = (...args) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag(...args);
  }
};

// Initialize Google Analytics
export const initGA = () => {
  if (!ANALYTICS_CONFIG.enabled || !ANALYTICS_CONFIG.googleAnalyticsId) {
    return;
  }

  // Load Google Analytics script
  const script = document.createElement('script');
  script.async = true;
  script.src = `https://www.googletagmanager.com/gtag/js?id=${ANALYTICS_CONFIG.googleAnalyticsId}`;
  document.head.appendChild(script);

  // Initialize gtag
  window.dataLayer = window.dataLayer || [];
  window.gtag = function() {
    window.dataLayer.push(arguments);
  };

  gtag('js', new Date());
  gtag('config', ANALYTICS_CONFIG.googleAnalyticsId, {
    page_title: document.title,
    page_location: window.location.href,
  });
};

// Track page views
export const trackPageView = (url, title) => {
  if (!ANALYTICS_CONFIG.enabled) return;

  gtag('config', ANALYTICS_CONFIG.googleAnalyticsId, {
    page_title: title,
    page_location: url,
  });

  logger.info('Page view tracked', { url, title });
};

// Track custom events
export const trackEvent = (eventName, parameters = {}) => {
  if (!ANALYTICS_CONFIG.enabled) return;

  gtag('event', eventName, {
    event_category: parameters.category || 'engagement',
    event_label: parameters.label,
    value: parameters.value,
    ...parameters,
  });

  logger.info('Event tracked', { eventName, parameters });
};

// Business-specific event tracking
export const trackTextProcessing = (data) => {
  trackEvent('text_processing', {
    category: 'feature_usage',
    label: 'text_processed',
    value: data.textLength || 0,
    processing_time: data.processingTime,
    user_tier: data.userTier,
    success: data.success,
  });
};

export const trackSubscription = (action, data = {}) => {
  trackEvent('subscription', {
    category: 'monetization',
    label: action, // 'started', 'completed', 'cancelled', 'upgraded'
    plan: data.plan,
    amount: data.amount,
    currency: data.currency,
  });
};

export const trackUserRegistration = (method) => {
  trackEvent('sign_up', {
    category: 'user_engagement',
    method: method, // 'google', 'email', etc.
  });
};

export const trackFeatureUsage = (feature, data = {}) => {
  trackEvent('feature_usage', {
    category: 'product_analytics',
    label: feature,
    user_tier: data.userTier,
    success: data.success,
  });
};

export const trackError = (error, context = {}) => {
  trackEvent('exception', {
    category: 'errors',
    description: error.message,
    fatal: context.fatal || false,
    page: context.page,
    user_tier: context.userTier,
  });

  // Also log to our internal logging system
  logger.logError(error, {
    type: 'CLIENT_ERROR',
    ...context,
  });
};

// Performance monitoring
export const trackPerformance = (metric, value, context = {}) => {
  trackEvent('timing_complete', {
    category: 'performance',
    name: metric,
    value: Math.round(value),
    ...context,
  });
};

// User behavior tracking
export const trackUserBehavior = (action, data = {}) => {
  const behaviorEvents = {
    'text_input_started': () => trackEvent('text_input_started', {
      category: 'user_behavior',
      text_length: data.textLength,
    }),
    
    'text_input_completed': () => trackEvent('text_input_completed', {
      category: 'user_behavior',
      text_length: data.textLength,
      time_spent: data.timeSpent,
    }),
    
    'result_copied': () => trackEvent('result_copied', {
      category: 'user_behavior',
      result_length: data.resultLength,
    }),
    
    'result_downloaded': () => trackEvent('result_downloaded', {
      category: 'user_behavior',
      format: data.format,
    }),
    
    'premium_feature_attempted': () => trackEvent('premium_feature_attempted', {
      category: 'monetization',
      feature: data.feature,
      user_tier: data.userTier,
    }),
    
    'pricing_page_viewed': () => trackEvent('pricing_page_viewed', {
      category: 'monetization',
      source: data.source,
    }),
  };

  if (behaviorEvents[action]) {
    behaviorEvents[action]();
  } else {
    trackEvent(action, {
      category: 'user_behavior',
      ...data,
    });
  }
};

// A/B Testing support
export const trackExperiment = (experimentId, variant, data = {}) => {
  trackEvent('experiment_impression', {
    category: 'experiments',
    experiment_id: experimentId,
    variant_id: variant,
    ...data,
  });
};

// Conversion tracking
export const trackConversion = (conversionType, data = {}) => {
  const conversionEvents = {
    'subscription_purchase': () => {
      gtag('event', 'purchase', {
        transaction_id: data.transactionId,
        value: data.value,
        currency: data.currency,
        items: [{
          item_id: data.planId,
          item_name: data.planName,
          category: 'subscription',
          quantity: 1,
          price: data.value,
        }],
      });
    },
    
    'trial_started': () => {
      gtag('event', 'begin_checkout', {
        currency: data.currency,
        value: data.value,
        items: [{
          item_id: data.planId,
          item_name: data.planName,
          category: 'trial',
        }],
      });
    },
    
    'lead_generated': () => {
      gtag('event', 'generate_lead', {
        currency: 'USD',
        value: data.leadValue || 10,
      });
    },
  };

  if (conversionEvents[conversionType]) {
    conversionEvents[conversionType]();
  }

  trackEvent(conversionType, {
    category: 'conversions',
    ...data,
  });
};

// Privacy-compliant analytics
export const setUserProperties = (properties) => {
  if (!ANALYTICS_CONFIG.enabled) return;

  // Only set non-PII properties
  const allowedProperties = {
    user_tier: properties.subscriptionTier,
    signup_date: properties.createdAt,
    last_active: properties.lastActive,
    feature_usage_count: properties.usageCount,
  };

  gtag('config', ANALYTICS_CONFIG.googleAnalyticsId, {
    custom_map: allowedProperties,
  });
};

// Consent management
export const setAnalyticsConsent = (consent) => {
  if (typeof window !== 'undefined' && window.gtag) {
    gtag('consent', 'update', {
      analytics_storage: consent ? 'granted' : 'denied',
      ad_storage: consent ? 'granted' : 'denied',
    });
  }
};

// Initialize analytics on app start
export const initAnalytics = () => {
  if (typeof window === 'undefined') return;

  // Initialize Google Analytics
  initGA();

  // Set default consent (can be updated based on user preference)
  if (window.gtag) {
    gtag('consent', 'default', {
      analytics_storage: 'denied',
      ad_storage: 'denied',
    });
  }

  // Track initial page load
  trackPageView(window.location.href, document.title);
};

export default {
  initAnalytics,
  trackPageView,
  trackEvent,
  trackTextProcessing,
  trackSubscription,
  trackUserRegistration,
  trackFeatureUsage,
  trackError,
  trackPerformance,
  trackUserBehavior,
  trackExperiment,
  trackConversion,
  setUserProperties,
  setAnalyticsConsent,
};
