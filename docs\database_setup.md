# Database Setup with Prisma

## A. Overview

This project uses [Prisma](https://www.prisma.io/) as an ORM (Object-Relational Mapper) to interact with the database. [NextAuth.js](https://next-auth.js.org/) is configured to use Prisma via the `@next-auth/prisma-adapter` for user persistence, meaning user accounts, sessions (if using database strategy), and linked OAuth accounts are stored in the database.

This setup allows for robust user data management, including custom fields on the `User` model like `subscriptionTier` and `usageCredits`.

For development, we start with a simple SQLite database. For production, you would typically use a more robust database like PostgreSQL.

## B. Installation

You'll need to install Prisma CLI as a dev dependency and Prisma Client & the NextAuth Prisma Adapter as regular dependencies.

```bash
# Using npm
npm install @prisma/client @next-auth/prisma-adapter
npm install prisma --save-dev

# Or using yarn
# yarn add @prisma/client @next-auth/prisma-adapter
# yarn add prisma --dev
```

## C. Prisma Initialization

Initialize Prisma in your project. This command sets up the Prisma configuration files. We specify `sqlite` as the default datasource provider for simplicity in development.

```bash
npx prisma init --datasource-provider sqlite
```

This command will:
*   Create a `prisma` directory in your project root.
*   Create a `prisma/schema.prisma` file, pre-configured with a SQLite datasource.
*   Create or update the `.env` file in your project root with a `DATABASE_URL` for SQLite (e.g., `DATABASE_URL="file:./dev.db"`).

## D. Prisma Schema (`prisma/schema.prisma`)

The `schema.prisma` file is the heart of your Prisma setup. It defines your database connection, the Prisma Client generator, and your database models.

Here is the schema used in this project, including standard NextAuth.js models and custom fields on the `User` model:

```prisma
// prisma/schema.prisma

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite" // Start with SQLite for simplicity for MVP/development
  // For production, you would typically switch to a more robust database like PostgreSQL:
  // provider = "postgresql"
  // And update the DATABASE_URL accordingly in your .env file:
  // e.g., DATABASE_URL="postgresql://user:password@host:port/database?schema=public"
  url      = env("DATABASE_URL") // This will be read from the .env file
}

// Standard NextAuth.js models
// Official Prisma adapter schema: https://next-auth.js.org/adapters/prisma

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String // Type of account, e.g., "oauth", "email"
  provider          String // Name of the OAuth provider, e.g., "google", "github"
  providerAccountId String // User's ID as given by the provider
  refresh_token     String? // OAuth refresh token, potentially long
  access_token      String? // OAuth access token, potentially long
  expires_at        Int?    // Expiry timestamp for the access_token
  token_type        String? // Type of token, e.g., "Bearer"
  scope             String? // Scope granted by the user
  id_token          String? // JWT ID token, potentially long
  session_state     String? // Used by some providers

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId]) // Ensures a user can only link each provider account once
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique // The token used to identify the session
  userId       String
  expires      DateTime // When the session will expire
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid()) // Unique ID for the user (CUID is a good default)
  name          String?   // User's name, optional
  email         String?   @unique // User's email, should be unique if provided
  emailVerified DateTime? // Timestamp when the email was verified (for email-based auth)
  image         String?   // URL to the user's profile image

  // Relations to other NextAuth.js models
  accounts Account[] // A user can have multiple linked accounts (e.g., Google, GitHub)
  sessions Session[] // A user can have multiple active sessions

  // --- Application-specific fields ---
  subscriptionTier String @default("free") // e.g., "free", "basic_monthly", "premium_yearly"
  usageCredits     Int    @default(10)    // Example: number of paraphrases or other actions allowed
                                          // This could be reset daily/monthly based on app logic

  stripeCustomerId String? @unique         // For future Stripe (or other payment provider) integration
                                          // Store the customer ID from the payment provider

  // Timestamps
  createdAt DateTime @default(now()) // When the user record was created
  updatedAt DateTime @updatedAt     // When the user record was last updated
}

model VerificationToken {
  identifier String   // Typically the email address for passwordless/email verification
  token      String   @unique // The verification token
  expires    DateTime // When the token will expire

  @@unique([identifier, token]) // Ensures identifier/token pair is unique
}
```

## E. Environment Variable (`DATABASE_URL`)

Prisma requires the `DATABASE_URL` environment variable to know where your database is located. This variable should be set in your `.env` file (or `.env.local`).

*   **For SQLite (default for development):**
    The `npx prisma init` command should have already added this to your `.env` file:
    ```dotenv
    DATABASE_URL="file:./dev.db"
    ```
    This tells Prisma to create and use a SQLite database file named `dev.db` in your `prisma` directory.

*   **For PostgreSQL (example for production):**
    If you switch to PostgreSQL, your `DATABASE_URL` would look something like this:
    ```dotenv
    DATABASE_URL="postgresql://YOUR_USER:YOUR_PASSWORD@YOUR_HOST:YOUR_PORT/YOUR_DATABASE?schema=public"
    ```
    Remember to replace the placeholders with your actual PostgreSQL connection details.

**Important:** Ensure your `.env` or `.env.local` file is listed in your `.gitignore` file to avoid committing sensitive database credentials to version control.

## F. Generate Prisma Client and Create Database Schema

After defining your schema in `prisma/schema.prisma` and configuring your `DATABASE_URL` in `.env`, you need to generate the Prisma Client and create the actual database schema (tables, columns, etc.).

### 1. Generate Prisma Client

Prisma Client is a type-safe query builder that's generated specifically for your schema. To generate it, run the following command in your project root:

```bash
npx prisma generate
```

**What this does:**
*   Reads your `prisma/schema.prisma`.
*   Creates or updates the Prisma Client library in `node_modules/.prisma/client`.
*   Allows you to import `PrismaClient` from `@prisma/client` in your application code.

You should re-run this command every time you make changes to your `schema.prisma` file.

### 2. Create and Apply Migrations

Prisma uses a migration system to manage changes to your database schema over time. For development, the `prisma migrate dev` command is used.

To create your initial database schema based on your `schema.prisma` file and name this first migration "init", run:

```bash
npx prisma migrate dev --name init
```

**What this does:**
*   **Creates a migration file:** It generates a new SQL migration file inside the `prisma/migrations/` directory (e.g., `prisma/migrations/YYYYMMDDHHMMSS_init/migration.sql`). This file contains the SQL commands to create the tables and columns defined in your schema.
*   **Applies the migration:** It executes the SQL migration against your database (the one specified by `DATABASE_URL`).
    *   For SQLite (using `DATABASE_URL="file:./dev.db"`), this will create the `dev.db` file in your `prisma` directory if it doesn't already exist.
    *   It then creates all the tables (User, Account, Session, VerificationToken) as per your schema.
*   **Ensures development database is in sync:** If you make further changes to `schema.prisma`, you run `npx prisma migrate dev` again (providing a new migration name) to update your database. It might prompt you if potentially destructive changes are detected.

After running these commands, your Prisma Client will be ready to use, and your database (e.g., `prisma/dev.db`) will be created with the necessary tables.

## G. Shared Prisma Instance (`src/lib/prisma.js`)

To avoid creating too many database connections, especially during development with Next.js's hot reloading, it's a best practice to use a shared PrismaClient instance. Create the following file:

`src/lib/prisma.js`:
```javascript
// src/lib/prisma.js
import { PrismaClient } from '@prisma/client';

// PrismaClient is attached to the `global` object in development to prevent
// exhausting your database connection limit.
// Learn more: https://pris.ly/d/help/next-js-best-practices

let prisma;

if (process.env.NODE_ENV === 'production') {
  prisma = new PrismaClient();
} else {
  // Ensure the prisma instance is re-used during hot-reloading
  // in development. This prevents creating too many connections.
  if (!global.prisma) {
    global.prisma = new PrismaClient({
      // Optional: log Prisma queries in development
      // log: ['query', 'info', 'warn', 'error'],
    });
  }
  prisma = global.prisma;
}

export default prisma;
```
You then import this `prisma` instance in your API routes or server-side code instead of instantiating `new PrismaClient()` every time.

## H. NextAuth.js Adapter Configuration

The NextAuth.js configuration needs to be updated to use the Prisma Adapter and switch to database sessions. This is done in `src/pages/api/auth/[...nextauth].js`.

Key changes in `authOptions`:
```javascript
// src/pages/api/auth/[...nextauth].js (snippet)
import { PrismaClient } from '@prisma/client';
import { PrismaAdapter } from '@next-auth/prisma-adapter';

const prisma = new PrismaClient(); // Or import the shared instance from src/lib/prisma.js

export const authOptions = {
    adapter: PrismaAdapter(prisma),
    // ... other providers

    session: {
        strategy: 'database', // Changed from 'jwt'
        maxAge: 30 * 24 * 60 * 60, // 30 days
        updateAge: 24 * 60 * 60, // 24 hours
    },
    callbacks: {
        async session({ session, user }) { // 'user' is the user from the database
            if (user) {
                session.user.id = user.id;
                session.user.subscriptionTier = user.subscriptionTier;
                session.user.usageCredits = user.usageCredits;
            }
            return session;
        }
        // jwt callback is less critical for session data when using 'database' strategy
    },
    // ... other options
};
```
Refer to the full `[...nextauth].js` file for the complete configuration. The shared `prisma` instance from `src/lib/prisma.js` should be used here instead of `new PrismaClient()`.

## I. Accessing User Data (Example)

Once Prisma and the NextAuth.js adapter are set up, you can access the extended user data from the database in your API routes.

For example, the `/api/user-profile` route fetches the current user's profile:
```javascript
// src/pages/api/user-profile.js (conceptual snippet)
import { getServerSession } from "next-auth/next";
import { authOptions } from "./auth/[...nextauth]";
import prisma from '../../lib/prisma'; // Using the shared instance

export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session || !session.user || !session.user.id) {
        return res.status(401).json({ error: "Unauthorized" });
    }

    try {
        const userProfile = await prisma.user.findUnique({
            where: { id: session.user.id },
            select: {
                id: true, name: true, email: true, image: true,
                subscriptionTier: true, usageCredits: true, createdAt: true
            }
        });
        // ... return userProfile or error ...
    } catch (error) {
        // ... handle error ...
    }
}
```
The `client-protected.js` page then demonstrates how a client-side component can fetch and display this extended user data.

This setup provides a robust foundation for managing user data and implementing features based on user profiles and subscription tiers.
