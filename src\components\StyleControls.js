/**
 * Style Controls Component
 * Provides interface for selecting style profiles and adjusting style strength
 */

import { useState, useEffect, useCallback } from 'react';
import styles from './StyleControls.module.css';

export default function StyleControls({ 
    selectedProfile, 
    onProfileChange, 
    styleStrength, 
    onStyleStrengthChange,
    onCreateProfile 
}) {
    const [profiles, setProfiles] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Load user's style profiles
    useEffect(() => {
        loadProfiles();
    }, []);

    const loadProfiles = useCallback(async () => {
        try {
            setLoading(true);
            const response = await fetch('/api/style-profiles');
            
            if (!response.ok) {
                throw new Error('Failed to load style profiles');
            }
            
            const profilesData = await response.json();
            setProfiles(profilesData);
            
            // If no profile is selected but there's an active one, select it
            if (!selectedProfile && profilesData.length > 0) {
                const activeProfile = profilesData.find(p => p.isActive) || profilesData[0];
                onProfileChange(activeProfile);
            }
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [selectedProfile, onProfileChange]);

    const handleProfileSelect = useCallback(async (profile) => {
        try {
            // Update the selected profile as active
            await fetch('/api/style-profiles', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: profile.id,
                    isActive: true
                }),
            });
            
            onProfileChange(profile);
            
            // Reload profiles to update active status
            loadProfiles();
        } catch (err) {
            setError('Failed to select profile');
        }
    }, [onProfileChange, loadProfiles]);

    const getStrengthDescription = (strength) => {
        if (strength === 0) return "No personal style applied";
        if (strength < 25) return "Subtle personal touches";
        if (strength < 50) return "Light personal style";
        if (strength < 75) return "Moderate personal style";
        return "Strong personal style";
    };

    const getStrengthColor = (strength) => {
        if (strength === 0) return "#6b7280";
        if (strength < 25) return "#10b981";
        if (strength < 50) return "#3b82f6";
        if (strength < 75) return "#f59e0b";
        return "#ef4444";
    };

    if (loading) {
        return (
            <div className={styles.container}>
                <div className={styles.loading}>Loading style profiles...</div>
            </div>
        );
    }

    return (
        <div className={styles.container}>
            <div className={styles.header}>
                <h3>Writing Style</h3>
                <button 
                    onClick={onCreateProfile}
                    className={styles.createButton}
                    title="Create new style profile"
                >
                    + New Profile
                </button>
            </div>

            {error && (
                <div className={styles.error}>
                    {error}
                </div>
            )}

            {profiles.length === 0 ? (
                <div className={styles.noProfiles}>
                    <p>No style profiles found.</p>
                    <button onClick={onCreateProfile} className={styles.primaryButton}>
                        Create Your First Style Profile
                    </button>
                </div>
            ) : (
                <>
                    <div className={styles.profileSelector}>
                        <label htmlFor="profile-select">Style Profile:</label>
                        <select
                            id="profile-select"
                            value={selectedProfile?.id || ''}
                            onChange={(e) => {
                                const profile = profiles.find(p => p.id === e.target.value);
                                if (profile) {
                                    handleProfileSelect(profile);
                                }
                            }}
                            className={styles.select}
                        >
                            <option value="">No Style (Balanced Humanization Only)</option>
                            {profiles.map(profile => (
                                <option key={profile.id} value={profile.id}>
                                    {profile.name} {profile.isActive ? '(Active)' : ''}
                                </option>
                            ))}
                        </select>
                    </div>

                    {selectedProfile && (
                        <div className={styles.profileInfo}>
                            <div className={styles.profileDetails}>
                                <h4>{selectedProfile.name}</h4>
                                {selectedProfile.description && (
                                    <p className={styles.description}>{selectedProfile.description}</p>
                                )}
                                <div className={styles.profileStats}>
                                    <span>Samples: {selectedProfile.sampleCount}</span>
                                    <span>Words: {selectedProfile.totalSampleWords.toLocaleString()}</span>
                                    <span>Created: {new Date(selectedProfile.createdAt).toLocaleDateString()}</span>
                                </div>
                            </div>
                        </div>
                    )}

                    <div className={styles.strengthControl}>
                        <div className={styles.strengthHeader}>
                            <label htmlFor="style-strength">
                                Style Strength: 
                                <span 
                                    className={styles.strengthValue}
                                    style={{ color: getStrengthColor(styleStrength) }}
                                >
                                    {styleStrength}%
                                </span>
                            </label>
                            <span className={styles.strengthDescription}>
                                {getStrengthDescription(styleStrength)}
                            </span>
                        </div>
                        
                        <div className={styles.sliderContainer}>
                            <input
                                id="style-strength"
                                type="range"
                                min="0"
                                max="100"
                                step="5"
                                value={styleStrength}
                                onChange={(e) => onStyleStrengthChange(parseInt(e.target.value))}
                                className={styles.slider}
                                style={{
                                    background: `linear-gradient(to right, ${getStrengthColor(styleStrength)} 0%, ${getStrengthColor(styleStrength)} ${styleStrength}%, #e5e7eb ${styleStrength}%, #e5e7eb 100%)`
                                }}
                                disabled={!selectedProfile}
                            />
                            <div className={styles.sliderLabels}>
                                <span>0%</span>
                                <span>25%</span>
                                <span>50%</span>
                                <span>75%</span>
                                <span>100%</span>
                            </div>
                        </div>

                        {!selectedProfile && (
                            <div className={styles.disabledNote}>
                                Select a style profile to adjust strength
                            </div>
                        )}
                    </div>

                    {selectedProfile && (
                        <div className={styles.stylePreview}>
                            <h5>Style Characteristics:</h5>
                            <div className={styles.characteristics}>
                                <div className={styles.characteristic}>
                                    <span className={styles.label}>Sentence Style:</span>
                                    <span className={styles.value}>
                                        {selectedProfile.sentencePatterns.complexity} 
                                        ({selectedProfile.sentencePatterns.averageLength} words avg)
                                    </span>
                                </div>
                                <div className={styles.characteristic}>
                                    <span className={styles.label}>Vocabulary:</span>
                                    <span className={styles.value}>
                                        {selectedProfile.vocabularyComplexity.complexityLevel} complexity
                                    </span>
                                </div>
                                <div className={styles.characteristic}>
                                    <span className={styles.label}>Tone:</span>
                                    <span className={styles.value}>
                                        {selectedProfile.toneAnalysis.dominantTone}
                                    </span>
                                </div>
                                {selectedProfile.transitionPhrases.mostUsed.length > 0 && (
                                    <div className={styles.characteristic}>
                                        <span className={styles.label}>Top Transitions:</span>
                                        <span className={styles.value}>
                                            {selectedProfile.transitionPhrases.mostUsed
                                                .slice(0, 3)
                                                .map(t => t.phrase)
                                                .join(', ')}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </>
            )}
        </div>
    );
}
