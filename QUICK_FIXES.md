# 🚀 Quick Fixes for Ghost<PERSON>ay<PERSON>

## 1. Fix Google OAuth "Client Not Found" Error

### Step 1: Set up Google OAuth Credentials
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it
4. Create OAuth 2.0 credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Application type: "Web application"
   - Name: "GhostLayer"
   - Authorized redirect URIs: `http://localhost:3001/api/auth/callback/google`

### Step 2: Update Environment Variables
Create `.env.local` file with:
```env
NEXTAUTH_SECRET=your_super_strong_random_secret_here
NEXTAUTH_URL=http://localhost:3001
GOOGLE_CLIENT_ID=your_google_client_id_from_step_1
GOOGLE_CLIENT_SECRET=your_google_client_secret_from_step_1
```

## 2. Speed Up Text Processing

### Option A: Use Groq (Fastest - Recommended)
1. Sign up at [Groq Console](https://console.groq.com/)
2. Get your API key
3. Add to `.env.local`:
```env
GROQ_API_KEY=your_groq_api_key_here
```

### Option B: Use OpenAI (Fast & Reliable)
1. Sign up at [OpenAI Platform](https://platform.openai.com/)
2. Get your API key
3. Add to `.env.local`:
```env
OPENAI_API_KEY=your_openai_api_key_here
```

### Option C: Use Both (Best Performance)
Add both API keys for automatic fallback and load balancing.

## 3. Database Setup (Required for OAuth)

### Quick SQLite Setup (Development)
```bash
npx prisma generate
npx prisma db push
```

### PostgreSQL Setup (Production)
1. Create PostgreSQL database
2. Update `DATABASE_URL` in `.env.local`
3. Run migrations:
```bash
npx prisma generate
npx prisma db push
```

## 4. Complete .env.local Template

```env
# Application
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_APP_NAME="GhostLayer"

# Authentication
NEXTAUTH_SECRET=generate_with_openssl_rand_base64_32
NEXTAUTH_URL=http://localhost:3001
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Database
DATABASE_URL="file:./prisma/dev.db"

# AI APIs (Choose one or more)
GROQ_API_KEY=your_groq_api_key
OPENAI_API_KEY=your_openai_api_key

# Optional: AI Detection
GPTZERO_API_KEY=your_gptzero_api_key
```

## 5. Start the Application

```bash
npm run dev
```

The app will be available at: http://localhost:3001

## 6. Test the Fixes

1. **OAuth**: Click "Sign in with Google" - should work without errors
2. **Pages**: Visit `/features`, `/about`, `/pricing` - should load properly
3. **Text Processing**: Enter text and click "Humanize" - should process in 2-5 seconds

## 7. Performance Improvements Made

- ✅ **Multiple AI Providers**: Groq (fastest), OpenAI (reliable), local fallback
- ✅ **Automatic Fallbacks**: If one API fails, automatically tries the next
- ✅ **Optimized Prompts**: Shorter, more focused prompts for faster processing
- ✅ **Timeout Handling**: 8-10 second timeouts to prevent hanging
- ✅ **Enhanced Local Processing**: Better synonym replacement and sentence restructuring

## 8. Expected Performance

- **Groq API**: 1-3 seconds (ultra-fast)
- **OpenAI API**: 2-5 seconds (reliable)
- **Local Processing**: <1 second (instant fallback)

## Need Help?

If you encounter any issues:
1. Check the browser console for errors
2. Check the terminal/server logs
3. Verify all environment variables are set correctly
4. Ensure the database is properly initialized
