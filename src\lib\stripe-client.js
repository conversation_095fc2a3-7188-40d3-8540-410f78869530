// src/lib/stripe-client.js
import { loadStripe } from '@stripe/stripe-js';

// This variable will hold the Stripe promise once it's initialized.
// We use a singleton pattern to ensure loadStripe is only called once.
let stripePromise = null;

const getStripePromise = () => {
    const stripePublishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

    if (!stripePromise) {
        if (!stripePublishableKey) {
            const warningMessage = `
****************************************************************************************
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY environment variable is not set.
Client-side Stripe functionality (like redirectToCheckout) will not work correctly.
Please set this in your .env.local file (e.g., pk_test_YOUR_STRIPE_PUBLISHABLE_KEY).
Refer to README.md or docs/subscription_system_design.md for more details.
****************************************************************************************
            `;
            if (process.env.NODE_ENV !== 'production') {
                // In development, log a prominent warning.
                // The app will likely still run, but Stripe.js calls will fail or Stripe will show an error.
                console.warn(warningMessage);
            } else {
                // In production, this is a critical configuration error.
                // Log an error. Client-side Stripe will fail.
                console.error('CRITICAL: NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is not set for production environment.');
            }
            // Even if the key is missing, we call loadStripe with undefined.
            // loadStripe itself will then handle this, usually by returning a Promise
            // that rejects or an instance that shows an error when used.
            // This makes the getStripePromise function still return a Promise as expected.
        }
        stripePromise = loadStripe(stripePublishableKey);
    }
    return stripePromise;
};

export default getStripePromise;
