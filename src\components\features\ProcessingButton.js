import React, { useState } from 'react';
import styles from './ProcessingButton.module.css';

const ProcessingButton = ({ 
  onProcess, 
  isLoading, 
  disabled, 
  inputText,
  className = '' 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (!disabled && !isLoading && inputText.trim()) {
      onProcess();
    }
  };

  const isDisabled = disabled || isLoading || !inputText.trim();

  return (
    <div className={styles.buttonContainer}>
      <button
        className={`${styles.processButton} ${isLoading ? styles.loading : ''} ${isDisabled ? styles.disabled : ''} ${className}`}
        onClick={handleClick}
        disabled={isDisabled}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className={styles.buttonContent}>
          {isLoading ? (
            <>
              <div className={styles.loadingSpinner}></div>
              <span className={styles.buttonText}>Processing...</span>
            </>
          ) : (
            <>
              <svg 
                className={styles.buttonIcon} 
                width="20" 
                height="20" 
                viewBox="0 0 24 24" 
                fill="none"
              >
                <path 
                  d="M12 2L2 7l10 5 10-5-10-5z" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
                <path 
                  d="M2 17l10 5 10-5" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
                <path 
                  d="M2 12l10 5 10-5" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
              <span className={styles.buttonText}>Humanize Text</span>
            </>
          )}
        </div>
        
        {/* Animated background effect */}
        <div className={`${styles.buttonBackground} ${isHovered ? styles.hovered : ''}`}></div>
        
        {/* Ripple effect */}
        {!isDisabled && (
          <div className={styles.rippleContainer}>
            <div className={styles.ripple}></div>
          </div>
        )}
      </button>

      {/* Status indicators */}
      <div className={styles.statusContainer}>
        {isLoading && (
          <div className={styles.statusItem}>
            <div className={styles.statusDot}></div>
            <span className={styles.statusText}>AI is processing your text...</span>
          </div>
        )}
        
        {!inputText.trim() && !isLoading && (
          <div className={styles.statusItem}>
            <svg className={styles.statusIcon} width="16" height="16" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
              <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
              <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2"/>
            </svg>
            <span className={styles.statusText}>Enter text to get started</span>
          </div>
        )}
        
        {inputText.trim() && !isLoading && (
          <div className={styles.statusItem}>
            <svg className={styles.statusIcon} width="16" height="16" viewBox="0 0 24 24" fill="none">
              <polyline points="20,6 9,17 4,12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <span className={styles.statusText}>Ready to process</span>
          </div>
        )}
      </div>

      {/* Processing steps indicator */}
      {isLoading && (
        <div className={styles.stepsContainer}>
          <div className={styles.step}>
            <div className={`${styles.stepDot} ${styles.active}`}></div>
            <span className={styles.stepText}>Analyzing text</span>
          </div>
          <div className={styles.step}>
            <div className={`${styles.stepDot} ${styles.active}`}></div>
            <span className={styles.stepText}>Applying modifications</span>
          </div>
          <div className={styles.step}>
            <div className={`${styles.stepDot} ${styles.active}`}></div>
            <span className={styles.stepText}>Optimizing output</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProcessingButton;
