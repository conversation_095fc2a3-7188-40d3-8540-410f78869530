# Example Commit Messages

These are example commit messages for the initial setup and creation of the project files. They are grouped into logical chunks of work.

1.  **Project Initialization and Core Documentation:**
    *   `docs: Create initial system architecture document`
        *   (Covers `docs/system_architecture.md`)
    *   `docs: Define project structure and directory layout`
        *   (Covers `docs/project_structure.md`)
    *   `docs: Add workflow diagram and explanation`
        *   (Covers `docs/workflow.md`)
    *   `chore: Initialise Next.js project with basic configuration`
        *   (Covers `package.json`, `next.config.js`, `.eslintrc.json`, `.gitignore`, `jsconfig.json` etc. - assuming these are set up by `create-next-app` or similar)
    *   `docs: Create comprehensive README for project overview and setup`
        *   (Covers `README.md`)

2.  **Setup Basic Application Shell & UI Components:**
    *   `feat: Add minimal Layout component and global styles`
        *   (Covers `src/components/layout/Layout.js`, `src/styles/Home.module.css`, `src/styles/globals.css` - Note: `globals.css` was not explicitly created in prior steps but is standard in Next.js and implied by `Layout.js`)
    *   `feat: Implement basic TextEditor component for input and output`
        *   (Covers `src/components/features/TextEditor.js`)
    *   `feat: Create ControlPanel component for triggering actions`
        *   (Covers `src/components/features/ControlPanel.js`)
    *   `feat: Add ResultsDisplay component for showing AI detection feedback`
        *   (Covers `src/components/features/ResultsDisplay.js`)

3.  **Implement Core Frontend Logic for Home Page:**
    *   `feat: Develop HomePage integrating UI components and state management`
        *   (Covers `src/pages/index.js`)

4.  **Setup Backend API and Utility Functions:**
    *   `feat: Create API client utility for frontend-backend communication`
        *   (Covers `src/utils/apiClient.js`)
    *   `feat: Implement initial text modifier utility functions (MVP)`
        *   (Covers `src/utils/textModifiers.js`)
    *   `feat: Setup backend API endpoint for text processing with mock detection`
        *   (Covers `src/pages/api/process.js`)

---

**How to Use These Messages:**

These are suggestions. When committing your actual code:

*   **Commit logically:** Group related changes into a single commit. A commit should represent a complete, working unit of change, however small.
*   **Be descriptive:** The summary line (the first line of the commit message) should be concise (around 50 characters ideally, up to 72 max).
*   **Explain further if needed:** If the change is complex, add a more detailed explanation in the body of the commit message, separated by a blank line from the summary.
*   **Follow conventions:** Many teams use conventions like Conventional Commits (`feat:`, `fix:`, `docs:`, `style:`, `refactor:`, `test:`, `chore:`).

For example, after creating the initial documentation files, you might do:

```bash
git add docs/system_architecture.md docs/project_structure.md docs/workflow.md README.md
git commit -m "docs: Add initial project documentation and README"
```

Or, when adding a set of related components:
```bash
git add src/components/features/TextEditor.js src/components/features/ControlPanel.js src/components/features/ResultsDisplay.js
git commit -m "feat: Add core UI components for text editing and results"
```

This approach is more standard than committing each file individually.
