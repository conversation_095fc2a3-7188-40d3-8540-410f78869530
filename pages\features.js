import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '../src/components/layout/Layout';
import SEOHead from '../src/components/seo/SEOHead';
import InternalLinks from '../src/components/seo/InternalLinks';
import { getSoftwareApplicationSchema, getFAQSchema } from '../src/utils/structuredData';
import styles from '../src/styles/Features.module.css';

export default function Features() {
    const features = [
        {
            icon: '🤖',
            title: 'AI Text Humanization',
            description: 'Transform AI-generated content into natural, human-like text that bypasses detection algorithms.',
            benefits: ['Advanced paraphrasing', 'Natural language patterns', 'Context preservation']
        },
        {
            icon: '⚡',
            title: 'Lightning Fast Processing',
            description: 'Get results in seconds with our optimized AI models and efficient processing pipeline.',
            benefits: ['Sub-5 second processing', 'Multiple AI providers', 'Automatic fallbacks']
        },
        {
            icon: '🔍',
            title: 'AI Detection Bypass',
            description: 'Successfully bypass popular AI detection tools like GPTZero, Originality.ai, and more.',
            benefits: ['95% success rate', 'Multiple detection checks', 'Continuous improvements']
        },
        {
            icon: '📝',
            title: 'Content Quality',
            description: 'Maintain original meaning while improving readability and natural flow.',
            benefits: ['Meaning preservation', 'Grammar enhancement', 'Style consistency']
        },
        {
            icon: '🔒',
            title: 'Privacy & Security',
            description: 'Your content is processed securely and never stored on our servers.',
            benefits: ['No data storage', 'Encrypted processing', 'GDPR compliant']
        },
        {
            icon: '💼',
            title: 'Professional Use',
            description: 'Perfect for content creators, marketers, students, and professionals.',
            benefits: ['Bulk processing', 'API access', 'Team collaboration']
        }
    ];

    return (
        <>
            <SEOHead
                title="AI Text Humanizer Features | Advanced Detection Bypass Technology - GhostLayer"
                description="Discover GhostLayer's advanced AI text humanization features: 95% bypass success rate, style preservation, bulk processing, real-time detection testing, and multi-language support."
                keywords="AI text humanizer features, AI detection bypass technology, text humanization tools, ChatGPT humanizer, GPT detector bypass, AI content tools, undetectable AI writing features"
                canonicalUrl="/features"
                structuredData={getSoftwareApplicationSchema()}
            />

            <Layout>
                <div className={styles.container}>
                    <header className={styles.header}>
                        <h1 className={styles.title}>Powerful Features</h1>
                        <p className={styles.subtitle}>
                            Everything you need to transform AI text into natural, human-like content
                        </p>
                    </header>

                    <div className={styles.featuresGrid}>
                        {features.map((feature, index) => (
                            <div key={index} className={styles.featureCard}>
                                <div className={styles.featureIcon}>{feature.icon}</div>
                                <h3 className={styles.featureTitle}>{feature.title}</h3>
                                <p className={styles.featureDescription}>{feature.description}</p>
                                <ul className={styles.benefitsList}>
                                    {feature.benefits.map((benefit, idx) => (
                                        <li key={idx} className={styles.benefit}>
                                            <span className={styles.checkmark}>✓</span>
                                            {benefit}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        ))}
                    </div>

                    <section className={styles.ctaSection}>
                        <h2 className={styles.ctaTitle}>Ready to Get Started?</h2>
                        <p className={styles.ctaDescription}>
                            Join thousands of users who trust GhostLayer for their content needs.
                        </p>
                        <div className={styles.ctaButtons}>
                            <Link href="/" className={styles.primaryButton}>Try Now - Free</Link>
                            <Link href="/pricing" className={styles.secondaryButton}>View Pricing</Link>
                        </div>
                    </section>

                    {/* Internal Links for SEO */}
                    <InternalLinks currentPage="features" />
                </div>
            </Layout>
        </>
    );
}
