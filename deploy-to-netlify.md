# 🚀 Deploy GhostLayer to Netlify - Quick Start

## 📋 Pre-Deployment Checklist

### ✅ Code Preparation
- [x] Next.js configured for Netlify static export
- [x] Netlify Functions created for API routes
- [x] Build scripts updated in package.json
- [x] netlify.toml configuration optimized
- [x] Security headers configured
- [x] CORS handling implemented

### ✅ External Services Setup Required

#### 1. Database Setup (Choose One - Required)

**Option A: Supabase (Recommended)**
```bash
1. Go to https://supabase.com
2. Create account and new project
3. Go to Settings → Database
4. Copy "Connection pooling" URL
5. Save as DATABASE_URL environment variable
```

**Option B: PlanetScale**
```bash
1. Go to https://planetscale.com
2. Create account and database
3. Create main branch
4. Get connection string
5. Save as DATABASE_URL environment variable
```

#### 2. API Keys Setup (Required)
```bash
# GPTZero (Required for AI detection)
1. Sign up at https://gptzero.me
2. Get API key from dashboard
3. Save as GPTZERO_API_KEY

# OpenAI (Recommended for paraphrasing)
1. Sign up at https://platform.openai.com
2. Create API key
3. Save as OPENAI_API_KEY

# Google OAuth (Required for authentication)
1. Go to Google Cloud Console
2. Create OAuth 2.0 credentials
3. Save GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET
```

## 🚀 Deployment Steps

### Step 1: Commit Your Changes
```bash
git add .
git commit -m "Configure for Netlify deployment"
git push origin main
```

### Step 2: Deploy to Netlify

#### Method A: Netlify Dashboard (Recommended)
1. **Go to [netlify.com](https://netlify.com)**
2. **Sign up/Login** with GitHub account
3. **Click "New site from Git"**
4. **Select GitHub** and authorize Netlify
5. **Choose your repository** (GhostLayer-2)
6. **Configure build settings:**
   - Build command: `npm run build:netlify`
   - Publish directory: `out`
   - Functions directory: `netlify/functions`
7. **Click "Deploy site"**

#### Method B: Netlify CLI (Alternative)
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Initialize site
netlify init

# Deploy
netlify deploy --prod
```

### Step 3: Configure Environment Variables

**In Netlify Dashboard → Site Settings → Environment Variables:**

#### Core Variables (Required)
```bash
NODE_ENV=production
NETLIFY=true
NEXTAUTH_SECRET=your_super_strong_random_secret_here
NEXTAUTH_URL=https://your-site-name.netlify.app
NEXT_PUBLIC_APP_URL=https://your-site-name.netlify.app
NEXT_PUBLIC_APP_NAME=GhostLayer
```

#### Database (Required - Choose One)
```bash
# Supabase
DATABASE_URL=postgresql://postgres:[password]@[host]:5432/postgres?pgbouncer=true

# PlanetScale  
DATABASE_URL=mysql://[username]:[password]@[host]:3306/[database]?sslaccept=strict
```

#### External APIs (Required)
```bash
GPTZERO_API_KEY=your_gptzero_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
GROQ_API_KEY=your_groq_api_key_here
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
```

#### Optional (Premium Features)
```bash
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_key
STRIPE_SECRET_KEY=sk_live_your_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

### Step 4: Trigger Deployment
1. **Save environment variables**
2. **Go to Deploys tab**
3. **Click "Trigger deploy"**
4. **Monitor build logs**

### Step 5: Test Your Deployment

#### Basic Tests
```bash
# Test homepage
curl -I https://your-site-name.netlify.app

# Test health function
curl https://your-site-name.netlify.app/.netlify/functions/health

# Expected responses:
# Homepage: 200 OK
# Health: {"status":"ok","timestamp":"..."}
```

#### Functional Tests
1. **Visit your site URL**
2. **Test user registration/login**
3. **Try text processing feature**
4. **Verify AI detection works**
5. **Check all pages load correctly**

## 🔧 Post-Deployment Configuration

### Custom Domain (Optional)
1. **Go to Site Settings → Domain management**
2. **Add custom domain**
3. **Configure DNS records as instructed**
4. **Update environment variables:**
   ```bash
   NEXTAUTH_URL=https://your-domain.com
   NEXT_PUBLIC_APP_URL=https://your-domain.com
   ```

### Database Migration
```bash
# If using Prisma, run migrations
# This needs to be done manually or via a one-time function
npx prisma migrate deploy
```

## 📊 Monitoring Your Deployment

### Performance Metrics
- **Page Load Time**: < 3 seconds
- **Function Response**: < 2 seconds
- **Build Time**: < 5 minutes
- **Error Rate**: < 1%

### Usage Monitoring
- **Netlify Dashboard**: Site overview
- **Functions**: Execution logs and metrics
- **Analytics**: Traffic and performance data

## 🚨 Troubleshooting

### Common Issues & Solutions

#### Build Failures
```bash
# Check build logs in Netlify dashboard
# Common causes:
1. Missing environment variables
2. Node.js version mismatch (ensure Node 18+)
3. Build command errors
4. Missing dependencies
```

#### Function Errors
```bash
# Check function logs in Netlify dashboard
# Common causes:
1. Missing environment variables
2. Import/require errors
3. Database connection issues
4. External API failures
```

#### Authentication Issues
```bash
# Verify OAuth configuration:
1. NEXTAUTH_URL matches your site URL
2. Google OAuth redirect URIs include your domain
3. NEXTAUTH_SECRET is set and strong
```

## 💰 Cost Optimization

### Free Tier Limits
- **Bandwidth**: 100GB/month
- **Build Minutes**: 300/month  
- **Function Invocations**: 125,000/month
- **Sites**: Unlimited

### Usage Estimation
- **Average user session**: 2-3 function calls
- **Bandwidth per user**: ~2-3MB
- **Monthly capacity**: ~30,000-50,000 users
- **Function calls**: ~100,000/month

### When to Upgrade
- **Pro Plan ($19/month)** when you exceed:
  - 100GB bandwidth
  - 300 build minutes
  - 125K function invocations

## 🎉 Success Checklist

Your deployment is successful when:
- ✅ Site loads without errors
- ✅ All pages are accessible
- ✅ User authentication works
- ✅ Text processing functions correctly
- ✅ AI detection provides results
- ✅ Functions respond within timeout limits
- ✅ No console errors in browser
- ✅ Mobile responsiveness works

## 📞 Need Help?

### Documentation
- [NETLIFY_DEPLOYMENT_GUIDE.md](./NETLIFY_DEPLOYMENT_GUIDE.md) - Complete guide
- [Netlify Docs](https://docs.netlify.com/) - Official documentation

### Support Channels
- [Netlify Community](https://community.netlify.com/)
- [GitHub Issues](https://github.com/your-repo/issues)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/netlify)

---

## 🎯 Ready to Deploy!

Your GhostLayer application is now configured for Netlify deployment with:
- ✅ **Commercial use allowed** on free tier
- ✅ **Optimized performance** with CDN and functions
- ✅ **Scalable architecture** for growth
- ✅ **Security best practices** implemented
- ✅ **Cost-effective hosting** with generous free limits

**Follow the steps above to deploy your commercial AI text humanization service! 🚀**
