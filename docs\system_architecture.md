# System Architecture and Core Logic: AI Text Modification Web App

This document outlines the system architecture and core logic for the AI text modification web app.

## 1. System Architecture

*   **Type:** Hybrid Architecture.
*   **Frontend:**
    *   **Technology:** React with Next.js.
    *   **Responsibilities:**
        *   User interface (text input, controls, output display).
        *   Client-side validation.
        *   Communication with the backend API.
    *   **Hosting:** Can be deployed as a static site (using `next export`) or a Node.js server (default Next.js deployment) on platforms like Vercel, Netlify, or AWS Amplify.
*   **Backend API:**
    *   **Technology Options:**
        *   **Next.js API Routes:** Integrated within the Next.js project. Good for simplicity and unified codebase. Serverless functions when deployed on platforms like Vercel.
        *   **Separate Node.js Server:** (e.g., Express.js, NestJS) if more complex backend logic or microservices are anticipated in the future.
        *   **Python Server:** (e.g., FastAPI, Flask) if Python-based NLP libraries are preferred for text manipulation.
    *   **Responsibilities:**
        *   Receiving text from the frontend.
        *   Implementing the core text modification logic ("Smart Paraphrasing," "Controlled Grammar Mistakes," "Style Optimization," "Human-like Anomalies").
        *   Securely integrating with third-party AI detection APIs.
        *   Returning the processed text and detection results to the frontend.
*   **AI Models & Services:**
    *   **Text Modification (Paraphrasing, etc.):**
        *   **Option A (Paid LLMs - e.g., OpenAI GPT series):** Accessed via the backend. Offers high-quality results but incurs costs.
        *   **Option B (Self-hosted Open Source LLMs - e.g., models from Hugging Face like T5, BART):** Requires infrastructure to host and manage these models (can be complex and costly for compute). The backend would interact with these.
        *   **Option C ("Own Algorithms" - Rule-based/Simpler ML):** Implemented in the backend. For an MVP, this could involve:
            *   Synonym replacement using thesaurus APIs (some free ones exist, like Datamuse API) or pre-built libraries.
            *   Sentence restructuring (e.g., active to passive voice, simple reordering).
            *   Controlled error injection (e.g., common typos, slight grammatical inconsistencies).
            *   Style adjustments (e.g., varying sentence length, using more/less formal vocabulary based on heuristics).
    *   **AI Detection Test:**
        *   **GPTZero:** Has an API with a free tier (subject to limitations). Integration via backend.
        *   **OpenAI Text Classifier:** Requires OpenAI API key. Integration via backend. (Note: OpenAI has deprecated their original AI Text Classifier, but detection capabilities might be part of their newer model APIs or they might offer other tools).
        *   **Turnitin:** Primarily enterprise-focused, API access might be difficult/costly for an MVP. Integration would be backend.
        *   **Important Note on Free APIs:** Truly free, high-accuracy, and unlimited access to AI detection APIs is rare. The MVP will need to work within the constraints of available free tiers or clearly state if a feature relies on a paid service.

## 2. Core Logic Details

*   **Text Input:** User pastes or uploads text.
*   **Preprocessing (Backend):** Basic cleaning of input text (e.g., removing excessive whitespace).
*   **Text Modification Pipeline (Backend - "Own Algorithms" focus for MVP):**
    *   **a. Smart Paraphrasing (Simplified for MVP):**
        *   Break text into sentences.
        *   For each sentence, attempt synonym replacement for key words (nouns, verbs, adjectives) using a thesaurus (e.g., WordNet-based library if using Python, or a free API).
        *   Potentially apply simple sentence re-ordering rules (e.g., "X is Y" to "Y is X").
        *   *Constraint*: True "smart" paraphrasing like LLMs is hard with simple algorithms. This will be a heuristic-based approach.
    *   **b. Controlled Grammar Mistakes & Human-like Anomalies (Applied subtly):**
        *   Randomly introduce a very small number of common typos (e.g., "teh" for "the", "recieve" for "receive") based on a predefined list.
        *   Occasionally replace conjunctions (e.g., "and" with "but" if it makes some sense, or add/remove commas slightly incorrectly).
        *   Introduce slight redundancy: e.g., "The final conclusion is..." -> "The final end conclusion is..." (use sparingly).
        *   Vary sentence length slightly if all sentences are very uniform.
    *   **c. Style Optimization (Heuristic-based):**
        *   Contraction expansion/creation: "it's" -> "it is" or vice-versa, randomly.
        *   Simple active/passive transformations for some sentences.
*   **AI Detection Test (Backend):**
    *   After modifications, the backend sends the processed text to one or more configured AI detection APIs.
    *   It retrieves the scores/classifications.
*   **Output Generation:**
    *   The backend sends the modified text and the detection results (e.g., "Detected as AI: Yes/No" or a probability score) back to the frontend.
*   **Concurrency Handling (Backend):**
    *   **If using Next.js API Routes (Node.js):** Node.js is inherently non-blocking and asynchronous, which handles multiple I/O-bound operations (like API calls) well. For CPU-bound text manipulation, if it becomes very heavy, consider:
        *   Using `worker_threads` in Node.js for CPU-intensive tasks if on a traditional server deployment.
        *   Serverless platforms (like Vercel where Next.js API routes become serverless functions) scale automatically by creating more instances of the function, inherently handling concurrency.
    *   **If using Python (FastAPI/Flask):**
        *   Use an ASGI server like Uvicorn with multiple worker processes for FastAPI.
        *   For Flask, use a production-grade WSGI server like Gunicorn with multiple workers.
        *   Employ asynchronous programming (`async/await`) in Python for I/O-bound tasks (calling external APIs).

## 3. "Own Algorithms" and "Free APIs" - MVP Focus & Limitations

*   **Own Algorithms:** For the MVP, these will be rule-based and heuristic text transformations. They will aim to alter the text structure and vocabulary but won't achieve the sophistication of LLM-based paraphrasing. The goal is to make common AI writing patterns less obvious.
*   **Free APIs:**
    *   **Thesaurus:** Datamuse API is a good option for synonym/related word finding.
    *   **AI Detection:** GPTZero's free tier can be used for the "AI Detection Test" feature. Be mindful of rate limits and usage restrictions. Other "free" options are often very limited or less reliable.
    *   **LLM for Paraphrasing:** Accessing powerful LLMs for free for paraphrasing is generally not possible for a production application. An MVP might simulate this or use a very small, less powerful open-source model if the user is willing to handle its deployment. For this plan, we'll assume the "own algorithms" are the primary method for text modification in the MVP.
