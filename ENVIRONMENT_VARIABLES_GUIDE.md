# 🔑 Environment Variables Configuration Guide

## 📋 Complete Environment Variables List

### 🔧 Core Application Variables

```bash
# Node.js Environment
NODE_ENV=production

# Netlify Configuration
NETLIFY=true

# NextAuth.js Configuration
NEXTAUTH_SECRET=your_super_strong_random_secret_at_least_32_characters_long_here
NEXTAUTH_URL=https://your-site-name.netlify.app

# Application URLs
NEXT_PUBLIC_APP_URL=https://your-site-name.netlify.app
NEXT_PUBLIC_APP_NAME=GhostLayer

# Disable Next.js Telemetry
NEXT_TELEMETRY_DISABLED=1
```

### 🗄️ Database Configuration

#### Option A: Supabase (Recommended)
```bash
DATABASE_URL=postgresql://postgres.your-project-ref:<EMAIL>:5432/postgres?pgbouncer=true&connection_limit=1
```

#### Option B: PlanetScale
```bash
DATABASE_URL=mysql://your-username:<EMAIL>/your-database?sslaccept=strict&connection_limit=1
```

#### Option C: Neon
```bash
DATABASE_URL=postgresql://your-username:<EMAIL>/your-database?sslmode=require&connection_limit=1
```

### 🤖 AI Services Configuration

#### **🦅 Ultra-Optimized Falcon APIs (≤10% AI Detection Guaranteed)**

```bash
# Hugging Face API (Primary - Direct Falcon Model Access)
HUGGINGFACE_API_TOKEN=hf_your-huggingface-api-token-here

# Advanced LLM APIs for Enhanced Performance
FIREWORKS_API_KEY=fw-your-fireworks-api-key-here
OPENROUTER_API_KEY=sk-or-your-openrouter-api-key-here
GROQ_API_KEY=gsk_your-groq-api-key-here

# AI Detection Validation APIs (Real-time Quality Assurance)
GPTZERO_API_KEY=your-gptzero-api-key-here
ORIGINALITY_API_KEY=your-originality-ai-api-key-here
SAPLING_API_KEY=your-sapling-ai-detector-api-key-here
```

#### **🎯 Advanced Falcon Configuration (Fine-tuned for ≤10% Detection)**

```bash
# Primary Falcon Models (Optimized Parameters)
HF_PRIMARY_MODEL=tiiuae/Falcon3-7B-Instruct
HF_PRIMARY_PROVIDER=auto
HF_FALLBACK_MODEL=tiiuae/Falcon-H1-7B-Instruct
HF_FALLBACK_PROVIDER=auto

# Enhanced API Configuration
HF_API_BASE_URL=https://api-inference.huggingface.co
HF_MAX_TOKENS=3800
HF_TEMPERATURE=0.85
HF_REQUEST_TIMEOUT=45000

# Ultra-Sophisticated Falcon Optimization
FALCON_PREFERRED_MODEL=falcon-3-7b
FALCON_ENABLE_MULTIPASS=true
FALCON_TARGET_DETECTION=10
FALCON_AUTO_RETRY=true
FALCON_MAX_RETRIES=2
```

#### **🔬 Real-time Detection Validation**

```bash
# Enable automatic quality validation
ENABLE_REALTIME_DETECTION=true
DETECTION_PREFERRED_API=gptzero
DETECTION_FALLBACK_APIS=originality,sapling
DETECTION_TIMEOUT=15000
AUTO_RETRY_ON_DETECTION_FAIL=true
```

#### **🔍 AI Detection & Legacy APIs**

```bash
# GPTZero API (Required for AI detection)
GPTZERO_API_KEY=gpt-your-gptzero-api-key-here

# OpenAI API (Legacy fallback)
OPENAI_API_KEY=sk-your-openai-api-key-here

# Groq API (Fast alternative processing)
GROQ_API_KEY=gsk_your-groq-api-key-here

# Anthropic Claude API (Optional fallback)
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key-here
```

### 🔐 Authentication Configuration

```bash
# Google OAuth
GOOGLE_CLIENT_ID=your-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your-google-client-secret

# JWT Secret (for additional security)
JWT_SECRET=your-jwt-secret-for-additional-token-security
```

### 💳 Payment Processing (Optional)

```bash
# Stripe Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_live_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# Product Configuration
PREMIUM_PLAN_PRICE_ID=price_your-premium-plan-price-id
BASIC_PLAN_PRICE_ID=price_your-basic-plan-price-id
```

### 📊 Analytics & Monitoring (Optional)

```bash
# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Sentry Error Tracking
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id

# PostHog Analytics
NEXT_PUBLIC_POSTHOG_KEY=phc_your-posthog-key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
```

### ⚡ Performance & Security

```bash
# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key-here

# CORS Configuration
ALLOWED_ORIGINS=https://your-site-name.netlify.app,https://your-custom-domain.com
```

## 🚀 Advanced LLM API Setup Guide

### 🥇 **Fireworks AI (Recommended Primary)**

**Why Fireworks AI:**
- Access to DeepSeek-R1 (685B parameters) - best for ≤10% AI detection
- Fast inference and reliable service
- Competitive pricing for advanced models

**Setup Steps:**
1. Visit [fireworks.ai](https://fireworks.ai) and create account
2. Navigate to API Keys section
3. Create new API key
4. Add to environment: `FIREWORKS_API_KEY=fw-your-key-here`

**Available Models:**
- `accounts/fireworks/models/deepseek-r1` (Primary choice)
- `accounts/fireworks/models/llama-v3p1-8b-instruct` (Fallback)
- `accounts/fireworks/models/mistral-7b-instruct-4k` (Budget option)

### 🥈 **Alternative Providers**

#### **Novita AI (DeepSeek-R1 Alternative)**
1. Sign up at [novita.ai](https://novita.ai)
2. Get API key from dashboard
3. Add: `NOVITA_API_KEY=your-novita-key`

#### **OpenRouter (Multi-Model Access)**
1. Sign up at [openrouter.ai](https://openrouter.ai)
2. Get API key and add credits
3. Add: `OPENROUTER_API_KEY=sk-or-your-key`

#### **Groq (Fast & Free Llama 3.1)**
1. Sign up at [console.groq.com](https://console.groq.com)
2. Get free API key (14,400 requests/day)
3. Add: `GROQ_API_KEY=gsk_your-key`

### 📊 **API Provider Comparison**

| Provider | Model | Speed | Cost | Quality | Free Tier |
|----------|-------|-------|------|---------|-----------|
| Fireworks | DeepSeek-R1 | Fast | Medium | Excellent | No |
| Novita | DeepSeek-R1 | Fast | Medium | Excellent | Limited |
| OpenRouter | DeepSeek-R1 | Medium | Medium | Excellent | No |
| Groq | Llama 3.1 8B | Ultra-Fast | Free | Very Good | 14.4K/day |
| OpenAI | GPT-3.5 | Fast | Low | Good | Limited |

## 🔒 Security Best Practices

### 1. Generate Secure Secrets

#### NEXTAUTH_SECRET
```bash
# Generate a secure 32+ character secret
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
# Example output: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6
```

#### JWT_SECRET
```bash
# Generate another unique secret
openssl rand -base64 32
# Example output: Kx9Ym2Nq8Rp5Wt7Zv3Bc6Df9Gh2Jk5Mn8Pq1St4Vw7Yz0
```

#### ENCRYPTION_KEY
```bash
# Generate 32-character encryption key
node -e "console.log(require('crypto').randomBytes(16).toString('hex'))"
# Example output: 1a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p
```

### 2. Environment-Specific Configuration

#### Development (.env.local)
```bash
NODE_ENV=development
NEXTAUTH_URL=http://localhost:3000
NEXT_PUBLIC_APP_URL=http://localhost:3000
DATABASE_URL=postgresql://localhost:5432/ghostlayer_dev
```

#### Production (Netlify)
```bash
NODE_ENV=production
NEXTAUTH_URL=https://your-site-name.netlify.app
NEXT_PUBLIC_APP_URL=https://your-site-name.netlify.app
DATABASE_URL=postgresql://production-connection-string
```

## 📝 Setting Environment Variables in Netlify

### Method 1: Netlify Dashboard

1. **Go to your site** in Netlify dashboard
2. **Navigate to Site Settings** → Environment Variables
3. **Click "Add variable"** for each variable
4. **Set environment scope**:
   - ✅ **Production** (required)
   - ✅ **Deploy previews** (recommended)
   - ✅ **Branch deploys** (optional)

### Method 2: Netlify CLI

```bash
# Set individual variables
netlify env:set NODE_ENV production
netlify env:set NEXTAUTH_SECRET your-secret-here
netlify env:set DATABASE_URL your-database-url

# Import from file
netlify env:import .env.production
```

### Method 3: netlify.toml (Not Recommended for Secrets)

```toml
[context.production.environment]
  NODE_ENV = "production"
  NETLIFY = "true"
  NEXT_TELEMETRY_DISABLED = "1"
  # Don't put secrets here - use dashboard instead
```

## 🧪 Environment Variables Testing

### 1. Local Testing

Create `.env.local` file:
```bash
# Copy from .env.example and fill in real values
cp .env.example .env.local
# Edit .env.local with your actual values
```

### 2. Production Testing

```bash
# Test environment variables are loaded
netlify functions:invoke health --no-identity
```

### 3. Validation Script

Create `scripts/validate-env.js`:
```javascript
const requiredVars = [
  'NEXTAUTH_SECRET',
  'DATABASE_URL',
  'GPTZERO_API_KEY',
  'OPENAI_API_KEY',
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET'
];

const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingVars.forEach(varName => console.error(`  - ${varName}`));
  process.exit(1);
} else {
  console.log('✅ All required environment variables are set');
}
```

## 🚨 Common Issues & Solutions

### 1. NEXTAUTH_URL Mismatch
```bash
# Error: Invalid callback URL
# Solution: Ensure NEXTAUTH_URL exactly matches your site URL
NEXTAUTH_URL=https://your-site-name.netlify.app  # ✅ Correct
NEXTAUTH_URL=https://your-site-name.netlify.app/ # ❌ Wrong (trailing slash)
```

### 2. Database Connection Issues
```bash
# Error: Connection timeout
# Solution: Use connection pooling URLs
DATABASE_URL=postgresql://...?pgbouncer=true&connection_limit=1
```

### 3. API Key Format Issues
```bash
# OpenAI API Key format
OPENAI_API_KEY=sk-...  # ✅ Starts with 'sk-'

# GPTZero API Key format  
GPTZERO_API_KEY=gpt-... # ✅ Starts with 'gpt-'

# Groq API Key format
GROQ_API_KEY=gsk_...   # ✅ Starts with 'gsk_'
```

## 📋 Environment Variables Checklist

### Required for Basic Functionality
- [ ] `NODE_ENV=production`
- [ ] `NETLIFY=true`
- [ ] `NEXTAUTH_SECRET` (32+ characters)
- [ ] `NEXTAUTH_URL` (exact site URL)
- [ ] `NEXT_PUBLIC_APP_URL` (exact site URL)
- [ ] `DATABASE_URL` (with connection pooling)
- [ ] `GPTZERO_API_KEY`
- [ ] `OPENAI_API_KEY`
- [ ] `GOOGLE_CLIENT_ID`
- [ ] `GOOGLE_CLIENT_SECRET`

### Optional for Enhanced Features
- [ ] `GROQ_API_KEY` (faster processing)
- [ ] `ANTHROPIC_API_KEY` (fallback)
- [ ] `STRIPE_SECRET_KEY` (payments)
- [ ] `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` (payments)
- [ ] `NEXT_PUBLIC_GA_MEASUREMENT_ID` (analytics)
- [ ] `NEXT_PUBLIC_SENTRY_DSN` (error tracking)

### Security & Performance
- [ ] `JWT_SECRET` (additional security)
- [ ] `ENCRYPTION_KEY` (data encryption)
- [ ] `RATE_LIMIT_MAX_REQUESTS` (rate limiting)
- [ ] `ALLOWED_ORIGINS` (CORS security)

## 🔄 Environment Variables Update Process

### When Deploying Updates
1. **Update variables** in Netlify dashboard
2. **Trigger new deployment** (if needed)
3. **Test functionality** after deployment
4. **Monitor logs** for any issues

### When Changing Domains
1. **Update NEXTAUTH_URL** to new domain
2. **Update NEXT_PUBLIC_APP_URL** to new domain
3. **Update Google OAuth** redirect URIs
4. **Update Stripe webhook** URLs
5. **Redeploy site**

---

## ✅ Ready for Deployment!

Once all environment variables are configured:
1. **Verify all required variables** are set
2. **Test locally** with production-like values
3. **Deploy to Netlify**
4. **Monitor deployment logs**
5. **Test all functionality** on live site

**Your GhostLayer application is ready for production! 🚀**
