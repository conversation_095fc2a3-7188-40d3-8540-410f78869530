// Structured Data Schemas for SEO and AI Optimization

export const getWebsiteSchema = () => ({
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "GhostLayer",
  "alternateName": "GhostLayer AI Text Humanizer",
  "url": process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai",
  "description": "AI text humanization tool that makes AI-generated content undetectable by AI detection systems",
  "potentialAction": {
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": `${process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai"}/?q={search_term_string}`
    },
    "query-input": "required name=search_term_string"
  }
});

export const getSoftwareApplicationSchema = () => ({
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "GhostLayer AI Text Humanizer",
  "description": "Advanced AI text humanization tool that transforms AI-generated content into human-like text, bypassing AI detection systems with 95% success rate",
  "url": process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai",
  "applicationCategory": "ProductivityApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD",
    "availability": "https://schema.org/InStock"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "ratingCount": "2847",
    "bestRating": "5",
    "worstRating": "1"
  },
  "author": {
    "@type": "Organization",
    "name": "GhostLayer",
    "url": process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai"
  },
  "datePublished": "2024-01-01",
  "dateModified": new Date().toISOString().split('T')[0],
  "keywords": "AI text humanizer, bypass AI detection, make AI text undetectable, humanize ChatGPT text, AI content detector bypass",
  "featureList": [
    "AI Detection Bypass",
    "Text Humanization",
    "Style Preservation",
    "Bulk Processing",
    "Real-time Processing",
    "Multiple AI Model Support"
  ]
});

export const getOrganizationSchema = () => ({
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "GhostLayer",
  "url": process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai",
  "logo": `${process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai"}/images/logo.png`,
  "description": "Leading provider of AI text humanization technology, helping users create undetectable AI content",
  "foundingDate": "2024",
  "sameAs": [
    "https://twitter.com/GhostLayerAI",
    "https://linkedin.com/company/ghostlayer",
    "https://github.com/ghostlayer"
  ]
});

export const getFAQSchema = (faqs) => ({
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": faqs.map(faq => ({
    "@type": "Question",
    "name": faq.question,
    "acceptedAnswer": {
      "@type": "Answer",
      "text": faq.answer
    }
  }))
});

export const getHowToSchema = () => ({
  "@context": "https://schema.org",
  "@type": "HowTo",
  "name": "How to Make AI Text Undetectable",
  "description": "Step-by-step guide to humanize AI-generated text and bypass AI detection systems",
  "image": `${process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai"}/images/how-to-humanize-ai-text.png`,
  "totalTime": "PT2M",
  "estimatedCost": {
    "@type": "MonetaryAmount",
    "currency": "USD",
    "value": "0"
  },
  "supply": [
    {
      "@type": "HowToSupply",
      "name": "AI-generated text"
    }
  ],
  "tool": [
    {
      "@type": "HowToTool",
      "name": "GhostLayer AI Text Humanizer"
    }
  ],
  "step": [
    {
      "@type": "HowToStep",
      "name": "Paste AI Text",
      "text": "Copy and paste your AI-generated text into the input field",
      "image": `${process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai"}/images/step-1-paste-text.png`
    },
    {
      "@type": "HowToStep",
      "name": "Select Humanization Level",
      "text": "Choose your desired humanization strength from Conservative to Aggressive",
      "image": `${process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai"}/images/step-2-select-level.png`
    },
    {
      "@type": "HowToStep",
      "name": "Click Humanize",
      "text": "Press the Humanize button to transform your text",
      "image": `${process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai"}/images/step-3-humanize.png`
    },
    {
      "@type": "HowToStep",
      "name": "Copy Results",
      "text": "Copy your humanized, undetectable text and use it anywhere",
      "image": `${process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai"}/images/step-4-copy-results.png`
    }
  ]
});

export const getArticleSchema = (title, description, datePublished, dateModified) => ({
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": title,
  "description": description,
  "author": {
    "@type": "Organization",
    "name": "GhostLayer"
  },
  "publisher": {
    "@type": "Organization",
    "name": "GhostLayer",
    "logo": {
      "@type": "ImageObject",
      "url": `${process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai"}/images/logo.png`
    }
  },
  "datePublished": datePublished,
  "dateModified": dateModified || datePublished,
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": process.env.NEXT_PUBLIC_APP_URL || "https://ghostlayer.ai"
  }
});

// Combined schema for homepage
export const getHomepageSchema = () => {
  return [
    getWebsiteSchema(),
    getSoftwareApplicationSchema(),
    getOrganizationSchema(),
    getHowToSchema()
  ];
};
