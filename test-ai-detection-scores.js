/**
 * Test AI detection scores with enhanced humanization
 */

import { balancedHumanization } from './src/utils/balancedHumanizer.js';
import { advancedHumanization } from './src/utils/advancedHumanizer.js';
import { checkWithGPTZero } from './src/services/gptzeroClient.js';

// High AI detection content (typical AI-generated patterns)
const highAIContent = `Artificial intelligence represents a transformative technology that fundamentally revolutionizes modern business operations. Organizations across various industries are increasingly implementing AI-driven solutions to optimize their operational efficiency and enhance competitive advantages.

The implementation of artificial intelligence systems requires comprehensive strategic planning and substantial financial investment. However, numerous studies demonstrate that companies utilizing AI technologies experience significant improvements in productivity metrics and operational performance indicators.

Furthermore, artificial intelligence facilitates enhanced decision-making processes through advanced data analytics and predictive modeling capabilities. These sophisticated systems enable organizations to leverage vast amounts of data to generate actionable insights and optimize business outcomes.`;

console.log('=== AI DETECTION SCORE TEST ===');
console.log('Testing enhanced humanization against AI detection...\n');

async function testAIDetection() {
    try {
        // Test original content
        console.log('1. Testing Original Content:');
        try {
            const originalResult = await checkWithGPTZero(highAIContent);
            const originalScore = Math.round((originalResult.ai_probability || 0) * 100);
            console.log(`   Original AI Detection: ${originalScore}%`);
        } catch (error) {
            console.log(`   Original AI Detection: ⚠️ ${error.message}`);
        }

        // Test enhanced humanization
        console.log('\n2. Testing Enhanced Humanization:');
        
        const humanized = balancedHumanization(highAIContent, null, 0, {
            useAdvanced: true,
            aggressiveness: 0.8,
            maintainTone: true
        });
        
        console.log('Enhanced Output:');
        console.log(humanized);
        console.log('\nParagraphs preserved:', highAIContent.split('\n\n').length === humanized.split('\n\n').length ? '✅' : '❌');
        
        // Test AI detection on humanized content
        try {
            const humanizedResult = await checkWithGPTZero(humanized);
            const humanizedScore = Math.round((humanizedResult.ai_probability || 0) * 100);
            console.log(`\n   Enhanced AI Detection: ${humanizedScore}%`);
            
            const improvement = originalScore - humanizedScore;
            console.log(`   Improvement: ${improvement}% reduction`);
            console.log(`   Target Achievement: ${humanizedScore <= 20 ? '✅ ACHIEVED' : '❌ NEEDS IMPROVEMENT'}`);
            
            if (humanizedScore <= 10) {
                console.log('   🎉 EXCELLENT: ≤10% AI detection achieved!');
            } else if (humanizedScore <= 20) {
                console.log('   ✅ GOOD: ≤20% AI detection achieved!');
            } else {
                console.log('   ⚠️ NEEDS WORK: >20% AI detection, need more aggressive humanization');
            }
            
        } catch (error) {
            console.log(`   Enhanced AI Detection: ⚠️ ${error.message}`);
        }

        // Test multiple runs for consistency
        console.log('\n3. Testing Consistency (3 runs):');
        for (let i = 1; i <= 3; i++) {
            const run = balancedHumanization(highAIContent, null, 0, {
                useAdvanced: true,
                aggressiveness: 0.8,
                maintainTone: true
            });
            
            try {
                const result = await checkWithGPTZero(run);
                const score = Math.round((result.ai_probability || 0) * 100);
                console.log(`   Run ${i}: ${score}% AI detection`);
            } catch (error) {
                console.log(`   Run ${i}: ⚠️ ${error.message}`);
            }
        }

    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

// Test different aggressiveness levels
async function testAggressivenessLevels() {
    console.log('\n=== AGGRESSIVENESS LEVEL COMPARISON ===');
    
    const levels = [
        { name: 'Conservative', aggressiveness: 0.5 },
        { name: 'Balanced', aggressiveness: 0.7 },
        { name: 'Aggressive', aggressiveness: 0.9 }
    ];
    
    for (const level of levels) {
        console.log(`\n${level.name} (${level.aggressiveness}):`);
        
        const result = advancedHumanization(highAIContent, { 
            aggressiveness: level.aggressiveness 
        });
        
        console.log(`Sample: "${result.substring(0, 100)}..."`);
        console.log(`Paragraphs preserved: ${highAIContent.split('\n\n').length === result.split('\n\n').length ? '✅' : '❌'}`);
        
        try {
            const detection = await checkWithGPTZero(result);
            const score = Math.round((detection.ai_probability || 0) * 100);
            console.log(`AI Detection: ${score}%`);
        } catch (error) {
            console.log(`AI Detection: ⚠️ ${error.message}`);
        }
    }
}

// Run tests
async function runAllTests() {
    await testAIDetection();
    await testAggressivenessLevels();
    
    console.log('\n=== SUMMARY ===');
    console.log('Enhanced humanization system tested with:');
    console.log('✅ Automatic AI pattern detection');
    console.log('✅ Dynamic aggressiveness adjustment');
    console.log('✅ Aggressive word and structure transformation');
    console.log('✅ Paragraph structure preservation');
    console.log('\nTarget: Achieve ≤20% AI detection scores while preserving formatting');
}

runAllTests().catch(console.error);
