/**
 * Test with high AI detection content to establish baseline
 */

import { balancedHumanization } from './src/utils/balancedHumanizer.js';
import { advancedHumanization } from './src/utils/advancedHumanizer.js';

// High AI detection content (typical AI-generated patterns)
const highAIContent = `Artificial intelligence represents a transformative technology that fundamentally revolutionizes modern business operations. Organizations across various industries are increasingly implementing AI-driven solutions to optimize their operational efficiency and enhance competitive advantages.

The implementation of artificial intelligence systems requires comprehensive strategic planning and substantial financial investment. However, numerous studies demonstrate that companies utilizing AI technologies experience significant improvements in productivity metrics and operational performance indicators.

Furthermore, artificial intelligence facilitates enhanced decision-making processes through advanced data analytics and predictive modeling capabilities. These sophisticated systems enable organizations to leverage vast amounts of data to generate actionable insights and optimize business outcomes.`;

console.log('=== HIGH AI DETECTION CONTENT TEST ===');
console.log('Original (High AI Detection Content):');
console.log(highAIContent);
console.log('\nOriginal character count:', highAIContent.length);
console.log('Original paragraphs:', highAIContent.split('\n\n').length);

// Test current system
console.log('\n=== TESTING CURRENT SYSTEM ===');

console.log('\n1. Advanced Humanization (0.7 aggressiveness):');
const advanced07 = advancedHumanization(highAIContent, { aggressiveness: 0.7 });
console.log(advanced07);
console.log('Paragraphs preserved:', highAIContent.split('\n\n').length === advanced07.split('\n\n').length ? '✅' : '❌');

console.log('\n2. Advanced Humanization (0.9 aggressiveness):');
const advanced09 = advancedHumanization(highAIContent, { aggressiveness: 0.9 });
console.log(advanced09);
console.log('Paragraphs preserved:', highAIContent.split('\n\n').length === advanced09.split('\n\n').length ? '✅' : '❌');

console.log('\n3. Balanced Humanization (API-level):');
const balanced = balancedHumanization(highAIContent, null, 0, {
    useAdvanced: true,
    aggressiveness: 0.8,
    maintainTone: true
});
console.log(balanced);
console.log('Paragraphs preserved:', highAIContent.split('\n\n').length === balanced.split('\n\n').length ? '✅' : '❌');

// Analyze changes
console.log('\n=== CHANGE ANALYSIS ===');
function analyzeChanges(original, modified, label) {
    const originalWords = original.toLowerCase().split(/\s+/);
    const modifiedWords = modified.toLowerCase().split(/\s+/);
    
    let changedWords = 0;
    const minLength = Math.min(originalWords.length, modifiedWords.length);
    
    for (let i = 0; i < minLength; i++) {
        if (originalWords[i] !== modifiedWords[i]) {
            changedWords++;
        }
    }
    
    const changePercentage = (changedWords / originalWords.length * 100).toFixed(1);
    console.log(`${label}: ${changedWords}/${originalWords.length} words changed (${changePercentage}%)`);
    
    return changePercentage;
}

analyzeChanges(highAIContent, advanced07, 'Advanced 0.7');
analyzeChanges(highAIContent, advanced09, 'Advanced 0.9');
analyzeChanges(highAIContent, balanced, 'Balanced API');

console.log('\n=== RECOMMENDATIONS ===');
console.log('Current system appears too conservative for high AI detection content.');
console.log('Need more aggressive transformations while preserving structure.');
