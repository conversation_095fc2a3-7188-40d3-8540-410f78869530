import logger from './logger';

// Custom error classes
export class AppError extends Error {
  constructor(message, statusCode = 500, isOperational = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.name = this.constructor.name;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message, field = null) {
    super(message, 400);
    this.field = field;
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message = 'Authentication required') {
    super(message, 401);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message = 'Insufficient permissions') {
    super(message, 403);
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404);
    this.name = 'NotFoundError';
  }
}

export class RateLimitError extends AppError {
  constructor(message = 'Rate limit exceeded') {
    super(message, 429);
    this.name = 'RateLimitError';
  }
}

export class ExternalServiceError extends AppError {
  constructor(service, message = 'External service error') {
    super(`${service}: ${message}`, 502);
    this.service = service;
    this.name = 'ExternalServiceError';
  }
}

// Error handler middleware for API routes
export const errorHandler = (error, req, res) => {
  let statusCode = 500;
  let message = 'Internal Server Error';
  let details = {};

  // Log the error
  logger.logError(error, {
    url: req.url,
    method: req.method,
    userAgent: req.headers['user-agent'],
    ip: req.ip || req.connection.remoteAddress,
  });

  // Handle known error types
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    
    if (error instanceof ValidationError && error.field) {
      details.field = error.field;
    }
  } else if (error.name === 'PrismaClientKnownRequestError') {
    // Handle Prisma errors
    statusCode = 400;
    message = 'Database operation failed';
    
    if (error.code === 'P2002') {
      message = 'A record with this information already exists';
    } else if (error.code === 'P2025') {
      message = 'Record not found';
      statusCode = 404;
    }
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
  } else if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
    statusCode = 400;
    message = 'Invalid JSON in request body';
  }

  // Don't expose internal errors in production
  if (process.env.NODE_ENV === 'production' && statusCode === 500) {
    message = 'Something went wrong';
    details = {};
  }

  // Send error response
  const errorResponse = {
    success: false,
    error: {
      message,
      statusCode,
      ...(Object.keys(details).length > 0 && { details }),
      ...(process.env.NODE_ENV === 'development' && { 
        stack: error.stack,
        name: error.name 
      }),
    },
  };

  res.status(statusCode).json(errorResponse);
};

// Async error wrapper for API routes
export const asyncHandler = (fn) => {
  return async (req, res, next) => {
    try {
      await fn(req, res, next);
    } catch (error) {
      if (res.headersSent) {
        logger.logError(error, { 
          message: 'Error occurred after headers were sent',
          url: req.url,
          method: req.method 
        });
        return;
      }
      errorHandler(error, req, res);
    }
  };
};

// Global error handler for unhandled promise rejections and uncaught exceptions
export const setupGlobalErrorHandlers = () => {
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    // Don't exit the process in production, just log the error
    if (process.env.NODE_ENV !== 'production') {
      process.exit(1);
    }
  });

  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    // Exit the process for uncaught exceptions
    process.exit(1);
  });
};

// Validation helpers
export const validateRequired = (value, fieldName) => {
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    throw new ValidationError(`${fieldName} is required`, fieldName);
  }
};

export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new ValidationError('Invalid email format', 'email');
  }
};

export const validateLength = (value, fieldName, min = 0, max = Infinity) => {
  if (value.length < min) {
    throw new ValidationError(`${fieldName} must be at least ${min} characters`, fieldName);
  }
  if (value.length > max) {
    throw new ValidationError(`${fieldName} must be no more than ${max} characters`, fieldName);
  }
};
