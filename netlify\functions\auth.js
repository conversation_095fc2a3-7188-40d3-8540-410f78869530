// netlify/functions/auth.js
// NextAuth.js handler for Netlify Functions

const NextAuth = require('next-auth').default;
const GoogleProvider = require('next-auth/providers/google').default;
const { PrismaAdapter } = require('@next-auth/prisma-adapter');
const { PrismaClient } = require('@prisma/client');

// Initialize Prisma client
const prisma = new PrismaClient();

const authOptions = {
    adapter: PrismaAdapter(prisma),
    providers: [
        GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        }),
    ],
    callbacks: {
        async session({ session, user }) {
            // Add user ID and subscription info to session
            if (session?.user && user) {
                session.user.id = user.id;
                session.user.subscriptionTier = user.subscriptionTier || 'free';
                session.user.usageCredits = user.usageCredits || 10;
            }
            return session;
        },
        async jwt({ token, user }) {
            if (user) {
                token.id = user.id;
                token.subscriptionTier = user.subscriptionTier || 'free';
                token.usageCredits = user.usageCredits || 10;
            }
            return token;
        },
    },
    pages: {
        signIn: '/auth/signin',
        error: '/auth/error',
    },
    session: {
        strategy: 'database',
    },
    secret: process.env.NEXTAUTH_SECRET,
};

// Create the NextAuth handler
const handler = NextAuth(authOptions);

// Export the handler for Netlify Functions
exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    try {
        // Convert Netlify event to Next.js request format
        const req = {
            method: event.httpMethod,
            url: event.path,
            headers: event.headers,
            body: event.body,
            query: event.queryStringParameters || {},
        };

        const res = {
            statusCode: 200,
            headers: {},
            body: '',
            setHeader: function(key, value) {
                this.headers[key] = value;
            },
            status: function(code) {
                this.statusCode = code;
                return this;
            },
            json: function(data) {
                this.headers['Content-Type'] = 'application/json';
                this.body = JSON.stringify(data);
                return this;
            },
            send: function(data) {
                this.body = data;
                return this;
            },
            redirect: function(url) {
                this.statusCode = 302;
                this.headers['Location'] = url;
                return this;
            },
        };

        // Call NextAuth handler
        await handler(req, res);

        return {
            statusCode: res.statusCode,
            headers: { ...headers, ...res.headers },
            body: res.body,
        };

    } catch (error) {
        console.error("Error in auth function:", error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                error: 'Authentication error',
                message: error.message,
            }),
        };
    }
};
