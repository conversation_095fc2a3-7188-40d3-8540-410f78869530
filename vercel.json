{"version": 2, "name": "ghostlayer", "alias": ["ghostlayer"], "regions": ["iad1", "sfo1", "lhr1"], "build": {"env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}}, "buildCommand": "npm run vercel-build", "functions": {"pages/api/**/*.js": {"maxDuration": 30, "memory": 1024}, "pages/api/stripe/webhook.js": {"maxDuration": 10, "memory": 512}, "pages/api/process.js": {"maxDuration": 60, "memory": 1024}, "pages/api/test-detection.js": {"maxDuration": 30, "memory": 512}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}, {"key": "X-DNS-Prefetch-Control", "value": "on"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://ghostlayer.vercel.app"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With"}, {"key": "Cache-Control", "value": "no-store, max-age=0"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/old-page", "destination": "/", "permanent": true}], "rewrites": [{"source": "/health", "destination": "/api/health"}], "crons": [{"path": "/api/cron/cleanup", "schedule": "0 2 * * *"}], "github": {"silent": true}}