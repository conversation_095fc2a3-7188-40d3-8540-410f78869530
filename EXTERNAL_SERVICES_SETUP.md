# 🔧 External Services Setup Guide for GhostLayer

## 🗄️ Database Setup (Choose One)

### Option 1: Supabase (Recommended)

#### Why Supabase?
- ✅ **500MB free storage**
- ✅ **PostgreSQL compatible**
- ✅ **Built-in connection pooling**
- ✅ **Real-time features**
- ✅ **Easy dashboard management**

#### Setup Steps:
1. **Go to [supabase.com](https://supabase.com)**
2. **Sign up** with GitHub/Google
3. **Create new project**:
   - Project name: `ghostlayer`
   - Database password: Generate strong password
   - Region: Choose closest to your users
4. **Wait for setup** (2-3 minutes)
5. **Get connection string**:
   - Go to Settings → Database
   - Copy "Connection pooling" URL (not direct connection)
   - Format: `postgresql://postgres:[password]@[host]:5432/postgres?pgbouncer=true`
6. **Set as DATABASE_URL** in Netlify environment variables

#### Database Schema Setup:
```sql
-- Run in Supabase SQL Editor
-- User table for authentication
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  image TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Subscription table for premium features
CREATE TABLE subscriptions (
  id TEXT PRIMARY KEY,
  user_id TEXT REFERENCES users(id),
  status TEXT NOT NULL,
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Option 2: PlanetScale

#### Why PlanetScale?
- ✅ **1GB free storage**
- ✅ **MySQL compatible**
- ✅ **Branching like Git**
- ✅ **No migration downtime**
- ✅ **Built-in connection pooling**

#### Setup Steps:
1. **Go to [planetscale.com](https://planetscale.com)**
2. **Sign up** with GitHub
3. **Create database**:
   - Database name: `ghostlayer`
   - Region: Choose closest to your users
4. **Create main branch**
5. **Get connection string**:
   - Go to database → Connect
   - Select "Prisma" format
   - Copy connection string
   - Format: `mysql://[username]:[password]@[host]:3306/[database]?sslaccept=strict`
6. **Set as DATABASE_URL** in Netlify environment variables

## 🤖 AI Services Setup

### GPTZero API (Required for AI Detection)

#### Setup Steps:
1. **Go to [gptzero.me](https://gptzero.me)**
2. **Sign up** for an account
3. **Go to API section** or dashboard
4. **Generate API key**
5. **Copy API key** (starts with `gpt-`)
6. **Set as GPTZERO_API_KEY** in Netlify environment variables

#### Pricing:
- **Free tier**: 2,500 words/month
- **Paid plans**: Start at $10/month for 50,000 words

### OpenAI API (Recommended for Text Processing)

#### Setup Steps:
1. **Go to [platform.openai.com](https://platform.openai.com)**
2. **Sign up** or login
3. **Go to API Keys** section
4. **Create new secret key**
5. **Copy API key** (starts with `sk-`)
6. **Set as OPENAI_API_KEY** in Netlify environment variables
7. **Add billing method** (required for API usage)

#### Pricing:
- **GPT-3.5-turbo**: $0.0015/1K tokens (input), $0.002/1K tokens (output)
- **GPT-4**: $0.03/1K tokens (input), $0.06/1K tokens (output)
- **Typical cost**: $0.01-0.05 per text processing request

### Groq API (Ultra-Fast Alternative)

#### Setup Steps:
1. **Go to [console.groq.com](https://console.groq.com)**
2. **Sign up** with Google/GitHub
3. **Go to API Keys** section
4. **Create new API key**
5. **Copy API key** (starts with `gsk_`)
6. **Set as GROQ_API_KEY** in Netlify environment variables

#### Benefits:
- **Ultra-fast inference** (10x faster than OpenAI)
- **Free tier available**
- **Llama models** (open source)

## 🔐 Authentication Setup

### Google OAuth (Required for User Authentication)

#### Setup Steps:
1. **Go to [console.cloud.google.com](https://console.cloud.google.com)**
2. **Create new project** or select existing
3. **Enable Google+ API**:
   - Go to APIs & Services → Library
   - Search "Google+ API"
   - Click Enable
4. **Create OAuth 2.0 credentials**:
   - Go to APIs & Services → Credentials
   - Click "Create Credentials" → OAuth 2.0 Client IDs
   - Application type: Web application
   - Name: `GhostLayer`
5. **Configure redirect URIs**:
   - Authorized JavaScript origins: `https://your-site-name.netlify.app`
   - Authorized redirect URIs: `https://your-site-name.netlify.app/api/auth/callback/google`
6. **Copy credentials**:
   - Client ID: Set as `GOOGLE_CLIENT_ID`
   - Client Secret: Set as `GOOGLE_CLIENT_SECRET`

#### Important Notes:
- Replace `your-site-name` with your actual Netlify site name
- Update URIs when you get a custom domain
- Keep client secret secure and never expose in frontend code

## 💳 Payment Processing (Optional)

### Stripe Setup

#### Setup Steps:
1. **Go to [stripe.com](https://stripe.com)**
2. **Create account** and complete verification
3. **Get API keys**:
   - Go to Developers → API keys
   - Copy Publishable key: Set as `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`
   - Copy Secret key: Set as `STRIPE_SECRET_KEY`
4. **Create products and prices**:
   - Go to Products → Add product
   - Create "Premium Plan" with monthly/yearly pricing
   - Copy Price ID: Set as `PREMIUM_PLAN_PRICE_ID`
5. **Set up webhooks**:
   - Go to Developers → Webhooks
   - Add endpoint: `https://your-site-name.netlify.app/api/stripe/webhook`
   - Select events: `customer.subscription.created`, `customer.subscription.updated`, `customer.subscription.deleted`
   - Copy webhook secret: Set as `STRIPE_WEBHOOK_SECRET`

## 🔑 Environment Variables Summary

### Complete List for Netlify
```bash
# Core Application
NODE_ENV=production
NETLIFY=true
NEXTAUTH_SECRET=your_32_character_random_string_here
NEXTAUTH_URL=https://your-site-name.netlify.app
NEXT_PUBLIC_APP_URL=https://your-site-name.netlify.app
NEXT_PUBLIC_APP_NAME=GhostLayer

# Database (choose one)
DATABASE_URL=postgresql://postgres:[password]@[host]:5432/postgres?pgbouncer=true

# AI Services
GPTZERO_API_KEY=gpt-your_gptzero_api_key
OPENAI_API_KEY=sk-your_openai_api_key
GROQ_API_KEY=gsk_your_groq_api_key

# Authentication
GOOGLE_CLIENT_ID=your_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Payment (optional)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
PREMIUM_PLAN_PRICE_ID=price_your_plan_id
```

## 🧪 Testing External Services

### Database Connection Test
```bash
# Test in Netlify function logs
console.log('Testing database connection...');
await prisma.$queryRaw`SELECT 1`;
console.log('Database connected successfully!');
```

### API Services Test
```bash
# Test each API endpoint individually
curl -X POST https://your-site-name.netlify.app/.netlify/functions/test-detection \
  -H "Content-Type: application/json" \
  -d '{"text":"This is a test sentence."}'
```

### Authentication Test
1. Visit your deployed site
2. Click "Sign in with Google"
3. Complete OAuth flow
4. Verify user is logged in

## 🚨 Security Best Practices

### API Key Security
- ✅ **Never commit** API keys to git
- ✅ **Use environment variables** only
- ✅ **Rotate keys** regularly
- ✅ **Monitor usage** for unusual activity
- ✅ **Set up billing alerts** for paid services

### Database Security
- ✅ **Use connection pooling** URLs
- ✅ **Enable SSL** connections
- ✅ **Limit connection** count for serverless
- ✅ **Regular backups** (automatic with managed services)

## 💰 Cost Estimation

### Monthly Costs (Estimated)
- **Database**: $0 (free tiers)
- **GPTZero**: $10-50 (depending on usage)
- **OpenAI**: $20-100 (depending on usage)
- **Groq**: $0-20 (generous free tier)
- **Google OAuth**: $0 (free)
- **Stripe**: 2.9% + $0.30 per transaction

### Total: $30-170/month for moderate usage (1000-5000 users)

---

## ✅ Setup Checklist

- [ ] Database created and connection string obtained
- [ ] GPTZero API key generated
- [ ] OpenAI API key generated and billing set up
- [ ] Groq API key generated (optional)
- [ ] Google OAuth credentials created
- [ ] Stripe account set up (optional)
- [ ] All environment variables added to Netlify
- [ ] Services tested individually

**Once all services are set up, proceed to deployment! 🚀**
