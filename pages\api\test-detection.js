import { checkWithGPTZero } from '../../src/services/gptzeroClient';

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    const { text } = req.body;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return res.status(400).json({ message: 'Input text is required and must be a non-empty string.' });
    }

    try {
        // Call the GPTZero client directly with the provided text
        const detectionResult = await checkWithGPTZero(text);

        // The checkWithGPTZero function returns a comprehensive object,
        // including error flags, messages, status, score, and potentially raw response.
        // If detectionResult.error is true, it means something went wrong either with
        // API key configuration, the GPTZero API itself, or network issues.
        if (detectionResult.error) {
            // Send a status code that reflects the nature of the error if possible,
            // e.g., 500 for server-side issues, 400 for bad input (though input is checked above),
            // or a generic 502 Bad Gateway if it's an upstream API issue.
            // For simplicity, using 500 for most client errors here.
            // The client already has detailed error info in detectionResult.message.
            console.warn("GPTZero test-detection error:", detectionResult.message);
            return res.status(500).json(detectionResult);
        }

        // If successful, send the full result from checkWithGPTZero
        return res.status(200).json(detectionResult);

    } catch (error) {
        // This catches unexpected errors within this handler itself
        console.error("Error in /api/test-detection:", error);
        res.status(500).json({
            error: true,
            message: error.message || 'An unexpected server error occurred.',
            status: "Server Error",
            score: null
        });
    }
}
