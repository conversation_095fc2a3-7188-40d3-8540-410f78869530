// src/lib/stripe.js
import Stripe from 'stripe';

const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
    const warningMessage = `
****************************************************************************************
STRIPE_SECRET_KEY environment variable is not set.
Stripe functionality will not work.
Please set this in your .env.local file (e.g., sk_test_your_stripe_secret_key).
Refer to docs/README.md or docs/subscription_system_design.md for more details.
****************************************************************************************
    `;
    if (process.env.NODE_ENV === 'production') {
        // In production, failing to load the secret key is a critical error that should stop the server.
        throw new Error(warningMessage.replace(/\*/g, '').trim()); // Cleaner error message for production logs
    } else {
        // In development, log a prominent warning. The app might start, but Stripe calls will fail.
        // This prevents the dev server from crashing if keys are temporarily missing during setup,
        // but clearly indicates the problem.
        console.warn(warningMessage);
    }
}

// Initialize Stripe with the secret key and a specific API version.
// Pinning the API version is recommended for stability, as Stripe updates its API.
// Check the Stripe documentation for the latest stable API version when implementing.
// Example API version: '2023-10-16' (used at the time of writing this example)
// See: https://stripe.com/docs/api/versioning
const stripe = new Stripe(stripeSecretKey, {
    apiVersion: '2023-10-16', // Replace with the API version you are developing against
    // Optionally, add metadata about your application for Stripe's logs
    // appInfo: {
    //   name: "StealthWriterAI (Clone)", // Your application's name
    //   version: "0.1.0" // Your application's version
    // }
    // TypeScript users: you might need to add `// @ts-ignore` above if Stripe types and your config don't perfectly align.
});

export default stripe;
