
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Only use static export for Netlify builds, not development
  ...(process.env.NETLIFY === 'true' ? {
    output: 'export',
    distDir: 'out',
    trailingSlash: true,
  } : {}),

  images: {
    unoptimized: process.env.NETLIFY === 'true',
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  experimental: {
    esmExternals: true,
  },
  poweredByHeader: false,
  reactStrictMode: true,
  swcMinify: true,
};

module.exports = nextConfig;
