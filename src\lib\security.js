import helmet from 'helmet';
import cors from 'cors';
import logger from './logger';

// Security middleware configuration
export const securityConfig = {
  // Helmet configuration for security headers
  helmet: {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: [
          "'self'",
          "'unsafe-inline'",
          'https://fonts.googleapis.com',
          'https://cdn.jsdelivr.net',
        ],
        fontSrc: [
          "'self'",
          'https://fonts.gstatic.com',
          'https://cdn.jsdelivr.net',
        ],
        scriptSrc: [
          "'self'",
          "'unsafe-eval'", // Required for Next.js development
          "'unsafe-inline'", // Required for Next.js
          'https://js.stripe.com',
          'https://www.googletagmanager.com',
          'https://www.google-analytics.com',
        ],
        imgSrc: [
          "'self'",
          'data:',
          'https:',
          'https://lh3.googleusercontent.com', // Google profile images
          'https://avatars.githubusercontent.com',
        ],
        connectSrc: [
          "'self'",
          'https://api.stripe.com',
          'https://api.gptzero.me',
          'https://www.google-analytics.com',
          process.env.NEXT_PUBLIC_PARAPHRASE_API_URL,
        ].filter(Boolean),
        frameSrc: [
          "'self'",
          'https://js.stripe.com',
          'https://hooks.stripe.com',
        ],
        objectSrc: ["'none'"],
        baseUri: ["'self'"],
        formAction: ["'self'"],
        frameAncestors: ["'none'"],
        upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null,
      },
    },
    crossOriginEmbedderPolicy: false, // Disable for Stripe compatibility
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
  },

  // CORS configuration
  cors: {
    origin: function (origin, callback) {
      // Allow requests with no origin (mobile apps, curl, etc.)
      if (!origin) return callback(null, true);

      const allowedOrigins = [
        process.env.NEXT_PUBLIC_APP_URL,
        'http://localhost:3000',
        'http://localhost:3001',
        'https://stealthwriter-ai.vercel.app',
        'https://stealthwriter-ai.netlify.app',
      ].filter(Boolean);

      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        logger.warn('CORS blocked request from origin:', origin);
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept',
      'Origin',
    ],
    exposedHeaders: [
      'X-RateLimit-Limit',
      'X-RateLimit-Remaining',
      'X-RateLimit-Reset',
    ],
  },
};

// Input sanitization
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  // Remove potentially dangerous characters
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
};

// Validate file uploads
export const validateFileUpload = (file, options = {}) => {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB default
    allowedTypes = ['text/plain', 'application/pdf', 'application/msword'],
    allowedExtensions = ['.txt', '.pdf', '.doc', '.docx'],
  } = options;

  // Check file size
  if (file.size > maxSize) {
    throw new Error(`File size exceeds ${maxSize / 1024 / 1024}MB limit`);
  }

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    throw new Error(`File type ${file.type} not allowed`);
  }

  // Check file extension
  const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
  if (!allowedExtensions.includes(extension)) {
    throw new Error(`File extension ${extension} not allowed`);
  }

  return true;
};

// IP-based security checks
export const checkIPSecurity = (req) => {
  const ip = req.ip || req.connection.remoteAddress;
  const userAgent = req.headers['user-agent'];

  // Log suspicious activity
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
  ];

  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(userAgent)
  );

  if (isSuspicious && req.url !== '/api/health') {
    logger.warn('Suspicious request detected', {
      ip,
      userAgent,
      url: req.url,
      method: req.method,
    });
  }

  return { ip, userAgent, isSuspicious };
};

// Session security
export const validateSession = (session) => {
  if (!session || !session.user) {
    return false;
  }

  // Check session expiry
  if (session.expires && new Date(session.expires) < new Date()) {
    return false;
  }

  // Validate user data
  if (!session.user.id || !session.user.email) {
    return false;
  }

  return true;
};

// API key validation
export const validateApiKey = (apiKey, expectedKey) => {
  if (!apiKey || !expectedKey) {
    return false;
  }

  // Use constant-time comparison to prevent timing attacks
  if (apiKey.length !== expectedKey.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < apiKey.length; i++) {
    result |= apiKey.charCodeAt(i) ^ expectedKey.charCodeAt(i);
  }

  return result === 0;
};

// Webhook signature validation
export const validateWebhookSignature = (payload, signature, secret) => {
  const crypto = require('crypto');
  
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload, 'utf8')
    .digest('hex');

  const providedSignature = signature.replace('sha256=', '');

  return crypto.timingSafeEqual(
    Buffer.from(expectedSignature, 'hex'),
    Buffer.from(providedSignature, 'hex')
  );
};

// Security middleware factory
export const createSecurityMiddleware = (options = {}) => {
  return (req, res, next) => {
    // Apply security headers
    helmet(securityConfig.helmet)(req, res, (err) => {
      if (err) return next(err);

      // Apply CORS
      cors(securityConfig.cors)(req, res, (err) => {
        if (err) return next(err);

        // IP security check
        const { isSuspicious } = checkIPSecurity(req);
        
        // Block suspicious requests (optional)
        if (options.blockSuspicious && isSuspicious) {
          return res.status(403).json({ error: 'Access denied' });
        }

        // Sanitize query parameters
        if (req.query) {
          Object.keys(req.query).forEach(key => {
            if (typeof req.query[key] === 'string') {
              req.query[key] = sanitizeInput(req.query[key]);
            }
          });
        }

        // Sanitize body parameters
        if (req.body && typeof req.body === 'object') {
          Object.keys(req.body).forEach(key => {
            if (typeof req.body[key] === 'string') {
              req.body[key] = sanitizeInput(req.body[key]);
            }
          });
        }

        next();
      });
    });
  };
};

// Authentication middleware
export const requireAuth = (req, res, next) => {
  const session = req.session;

  if (!validateSession(session)) {
    return res.status(401).json({
      error: 'Authentication required',
      code: 'UNAUTHORIZED',
    });
  }

  req.user = session.user;
  next();
};

// Premium feature middleware
export const requirePremium = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      error: 'Authentication required',
      code: 'UNAUTHORIZED',
    });
  }

  const isPremium = req.user.subscriptionTier?.includes('premium');
  
  if (!isPremium) {
    return res.status(403).json({
      error: 'Premium subscription required',
      code: 'PREMIUM_REQUIRED',
    });
  }

  next();
};

export default {
  securityConfig,
  sanitizeInput,
  validateFileUpload,
  checkIPSecurity,
  validateSession,
  validateApiKey,
  validateWebhookSignature,
  createSecurityMiddleware,
  requireAuth,
  requirePremium,
};
