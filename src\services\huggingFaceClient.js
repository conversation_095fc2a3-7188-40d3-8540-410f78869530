/**
 * Hugging Face Inference Providers Client
 * Handles communication with HF Inference Providers API for text humanization
 * Supports multiple models and providers with fallback mechanisms
 */

import { HfInference } from '@huggingface/inference';

/**
 * Configuration for Hugging Face models and providers
 */
const HF_CONFIG = {
    apiToken: process.env.HUGGINGFACE_API_TOKEN,
    baseUrl: process.env.HF_API_BASE_URL || 'https://router.huggingface.co/v1',
    maxTokens: parseInt(process.env.HF_MAX_TOKENS) || 2048,
    temperature: parseFloat(process.env.HF_TEMPERATURE) || 0.7,
    requestTimeout: parseInt(process.env.HF_REQUEST_TIMEOUT) || 30000,
    
    // Primary model configuration
    primaryModel: process.env.HF_PRIMARY_MODEL || 'deepseek-ai/DeepSeek-R1',
    primaryProvider: process.env.HF_PRIMARY_PROVIDER || 'fireworks-ai',
    
    // Fallback model configuration
    fallbackModel: process.env.HF_FALLBACK_MODEL || 'meta-llama/Llama-3.1-8B-Instruct',
    fallbackProvider: process.env.HF_FALLBACK_PROVIDER || 'featherless-ai',
};

/**
 * Rate limiting configuration
 */
const RATE_LIMIT = {
    maxRequestsPerMinute: 60,
    requestQueue: [],
    lastRequestTime: 0,
    minRequestInterval: 1000, // 1 second between requests
};

/**
 * Initialize Hugging Face Inference client
 */
let hfClient = null;

function initializeClient() {
    if (!HF_CONFIG.apiToken) {
        throw new Error('HUGGINGFACE_API_TOKEN is required but not provided');
    }
    
    if (!hfClient) {
        hfClient = new HfInference(HF_CONFIG.apiToken);
    }
    
    return hfClient;
}

/**
 * Rate limiting helper
 */
async function enforceRateLimit() {
    const now = Date.now();
    const timeSinceLastRequest = now - RATE_LIMIT.lastRequestTime;
    
    if (timeSinceLastRequest < RATE_LIMIT.minRequestInterval) {
        const waitTime = RATE_LIMIT.minRequestInterval - timeSinceLastRequest;
        await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    RATE_LIMIT.lastRequestTime = Date.now();
}

/**
 * Generate humanization prompt based on content and parameters
 */
function generateHumanizationPrompt(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        contentType = 'general'
    } = options;

    // Determine transformation level based on aggressiveness
    let transformationLevel = 'moderate';
    if (aggressiveness >= 0.9) transformationLevel = 'extensive';
    else if (aggressiveness >= 0.7) transformationLevel = 'significant';
    else if (aggressiveness >= 0.5) transformationLevel = 'moderate';
    else transformationLevel = 'light';

    // Build the prompt
    const prompt = `You are an expert text humanization specialist. Your task is to rewrite the following text to make it sound more natural and human-written while maintaining its core meaning and quality.

REQUIREMENTS:
- Target AI detection score: ≤${targetDetection}%
- Transformation level: ${transformationLevel}
- Maintain ${maintainTone ? 'original tone and style' : 'professional tone'}
- Content type: ${contentType}
- Preserve paragraph structure and formatting
- Keep the same length approximately
- Maintain factual accuracy

HUMANIZATION TECHNIQUES TO APPLY:
1. Vary sentence structures and lengths naturally
2. Replace formal/robotic language with natural expressions
3. Add subtle human inconsistencies and flow variations
4. Use more conversational transitions
5. Avoid repetitive patterns and predictable structures
6. Include natural hesitations and qualifiers (sparingly)
7. Make vocabulary choices more varied and contextual

ORIGINAL TEXT:
${text}

HUMANIZED VERSION:`;

    return prompt;
}

/**
 * Enhanced error classification and handling
 */
function classifyError(error) {
    const errorMessage = error.message.toLowerCase();

    if (errorMessage.includes('rate limit') || errorMessage.includes('429')) {
        return { type: 'rate_limit', retryable: true, delay: 5000 };
    }

    if (errorMessage.includes('timeout') || errorMessage.includes('network')) {
        return { type: 'network', retryable: true, delay: 2000 };
    }

    if (errorMessage.includes('unauthorized') || errorMessage.includes('401')) {
        return { type: 'auth', retryable: false, delay: 0 };
    }

    if (errorMessage.includes('not found') || errorMessage.includes('404')) {
        return { type: 'not_found', retryable: false, delay: 0 };
    }

    if (errorMessage.includes('server error') || errorMessage.includes('500')) {
        return { type: 'server_error', retryable: true, delay: 3000 };
    }

    if (errorMessage.includes('quota') || errorMessage.includes('billing')) {
        return { type: 'quota', retryable: false, delay: 0 };
    }

    return { type: 'unknown', retryable: true, delay: 1000 };
}

/**
 * Enhanced logging system
 */
function logRequest(model, provider, attempt, status, details = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
        timestamp,
        model,
        provider,
        attempt,
        status,
        ...details
    };

    if (status === 'success') {
        console.log(`[HF-SUCCESS] ${timestamp} - Model: ${model}, Provider: ${provider}, Attempt: ${attempt}`, details);
    } else if (status === 'error') {
        console.error(`[HF-ERROR] ${timestamp} - Model: ${model}, Provider: ${provider}, Attempt: ${attempt}`, details);
    } else {
        console.log(`[HF-${status.toUpperCase()}] ${timestamp} - Model: ${model}, Provider: ${provider}, Attempt: ${attempt}`, details);
    }
}

/**
 * Make API request to Hugging Face with enhanced error handling and retries
 */
async function makeHFRequest(model, prompt, provider = 'auto', retryCount = 0) {
    const maxRetries = 3; // Increased retry count
    const requestId = Math.random().toString(36).substring(7);

    try {
        await enforceRateLimit();

        const client = initializeClient();

        // Prepare the request
        const requestOptions = {
            model: model,
            messages: [
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: HF_CONFIG.maxTokens,
            temperature: HF_CONFIG.temperature,
            provider: provider !== 'auto' ? provider : undefined
        };

        logRequest(model, provider, retryCount + 1, 'start', {
            requestId,
            promptLength: prompt.length,
            maxTokens: HF_CONFIG.maxTokens
        });

        // Make the request using chat completion
        const startTime = Date.now();
        const response = await client.chatCompletion(requestOptions);
        const responseTime = Date.now() - startTime;

        // Validate response structure
        if (!response) {
            throw new Error('No response received from Hugging Face API');
        }

        if (!response.choices || !Array.isArray(response.choices) || response.choices.length === 0) {
            throw new Error('Invalid response format: missing or empty choices array');
        }

        if (!response.choices[0].message || !response.choices[0].message.content) {
            throw new Error('Invalid response format: missing message content');
        }

        const humanizedText = response.choices[0].message.content.trim();

        if (!humanizedText) {
            throw new Error('Empty response content from Hugging Face API');
        }

        // Validate response quality
        if (humanizedText.length < 10) {
            throw new Error('Response too short, likely incomplete');
        }

        logRequest(model, provider, retryCount + 1, 'success', {
            requestId,
            responseTime,
            inputLength: prompt.length,
            outputLength: humanizedText.length,
            usage: response.usage
        });

        return {
            success: true,
            text: humanizedText,
            model: model,
            provider: provider,
            usage: response.usage || {},
            responseTime,
            requestId
        };

    } catch (error) {
        const errorClassification = classifyError(error);

        logRequest(model, provider, retryCount + 1, 'error', {
            requestId,
            error: error.message,
            errorType: errorClassification.type,
            retryable: errorClassification.retryable
        });

        // Retry logic with enhanced error handling
        if (retryCount < maxRetries && errorClassification.retryable) {
            const delay = errorClassification.delay * Math.pow(1.5, retryCount); // Progressive backoff

            logRequest(model, provider, retryCount + 1, 'retry', {
                requestId,
                delay,
                nextAttempt: retryCount + 2
            });

            await new Promise(resolve => setTimeout(resolve, delay));
            return makeHFRequest(model, prompt, provider, retryCount + 1);
        }

        // Enhance error message with context
        const enhancedError = new Error(
            `HF API request failed after ${retryCount + 1} attempts: ${error.message} (Type: ${errorClassification.type}, Model: ${model}, Provider: ${provider})`
        );
        enhancedError.originalError = error;
        enhancedError.errorType = errorClassification.type;
        enhancedError.model = model;
        enhancedError.provider = provider;
        enhancedError.attempts = retryCount + 1;

        throw enhancedError;
    }
}

/**
 * Enhanced main humanization function using Hugging Face models
 */
export async function humanizeTextWithHF(text, options = {}) {
    // Input validation
    if (!text || typeof text !== 'string') {
        throw new Error('Text is required and must be a string');
    }

    if (text.trim().length === 0) {
        throw new Error('Text cannot be empty or only whitespace');
    }

    if (text.length > 15000) { // Increased limit
        throw new Error('Text is too long. Maximum length is 15,000 characters.');
    }

    const startTime = Date.now();
    const sessionId = Math.random().toString(36).substring(7);

    console.log(`[HF-SESSION-${sessionId}] Starting humanization for ${text.length} characters`);

    try {
        // Generate the humanization prompt
        const prompt = generateHumanizationPrompt(text, options);

        // Define Falcon-prioritized fallback strategy for ≤10% AI detection
        const fallbackModels = getFalconPrioritizedModels(options.targetDetection);

        console.log(`[HF-SESSION-${sessionId}] Using Falcon-enhanced model strategy for ≤${options.targetDetection || 10}% AI detection`);

        let lastError = null;

        // Try each model in sequence
        for (const modelConfig of fallbackModels) {
            try {
                console.log(`[HF-SESSION-${sessionId}] Attempting ${modelConfig.name}: ${modelConfig.model}`);

                const result = await makeHFRequest(
                    modelConfig.model,
                    prompt,
                    modelConfig.provider
                );

                result.processingTime = Date.now() - startTime;
                result.modelUsed = modelConfig.name;
                result.sessionId = sessionId;

                // Validate result quality
                if (validateHumanizationResult(text, result.text)) {
                    console.log(`[HF-SESSION-${sessionId}] Success with ${modelConfig.name}. Processing time: ${result.processingTime}ms`);
                    return result;
                } else {
                    console.warn(`[HF-SESSION-${sessionId}] ${modelConfig.name} produced low-quality result, trying next model`);
                    continue;
                }

            } catch (modelError) {
                console.warn(`[HF-SESSION-${sessionId}] ${modelConfig.name} failed: ${modelError.message}`);
                lastError = modelError;

                // If this is an auth error, don't try other models
                if (modelError.errorType === 'auth' || modelError.errorType === 'quota') {
                    break;
                }

                continue;
            }
        }

        // All models failed
        throw lastError || new Error('All humanization models failed');

    } catch (error) {
        console.error(`[HF-SESSION-${sessionId}] All HF models failed:`, error.message);

        return {
            success: false,
            error: error.message,
            errorType: error.errorType || 'unknown',
            processingTime: Date.now() - startTime,
            modelUsed: 'none',
            sessionId,
            attempts: error.attempts || 0
        };
    }
}

/**
 * Get Falcon-prioritized model list based on target detection
 */
function getFalconPrioritizedModels(targetDetection = 10) {
    if (targetDetection <= 10) {
        // For ≤10% AI detection, prioritize Falcon models
        return [
            // Primary Falcon models
            { model: 'tiiuae/Falcon3-7B-Instruct', provider: 'auto', name: 'falcon-3-7b' },
            { model: 'tiiuae/Falcon-H1-7B-Instruct', provider: 'auto', name: 'falcon-h1-7b' },
            { model: 'tiiuae/Falcon3-10B-Instruct', provider: 'auto', name: 'falcon-3-10b' },

            // Fallback to configured models
            { model: HF_CONFIG.primaryModel, provider: HF_CONFIG.primaryProvider, name: 'primary' },
            { model: HF_CONFIG.fallbackModel, provider: HF_CONFIG.fallbackProvider, name: 'fallback' },

            // Additional fallbacks
            { model: 'meta-llama/Llama-3.1-8B-Instruct', provider: 'auto', name: 'llama-auto' },
            { model: 'mistralai/Mistral-7B-Instruct-v0.3', provider: 'auto', name: 'mistral-auto' }
        ];
    } else {
        // For higher detection targets, use standard model order
        return [
            { model: HF_CONFIG.primaryModel, provider: HF_CONFIG.primaryProvider, name: 'primary' },
            { model: HF_CONFIG.fallbackModel, provider: HF_CONFIG.fallbackProvider, name: 'fallback' },
            { model: 'tiiuae/Falcon3-7B-Instruct', provider: 'auto', name: 'falcon-3-7b' },
            { model: 'meta-llama/Llama-3.1-8B-Instruct', provider: 'auto', name: 'llama-auto' },
            { model: 'mistralai/Mistral-7B-Instruct-v0.3', provider: 'auto', name: 'mistral-auto' }
        ];
    }
}

/**
 * Validate humanization result quality
 */
function validateHumanizationResult(originalText, humanizedText) {
    // Basic quality checks
    if (!humanizedText || humanizedText.trim().length === 0) {
        return false;
    }

    // Check if result is too short (likely incomplete)
    if (humanizedText.length < originalText.length * 0.5) {
        return false;
    }

    // Check if result is too long (likely hallucinated)
    if (humanizedText.length > originalText.length * 2) {
        return false;
    }

    // Check for obvious errors
    if (humanizedText.includes('[ERROR]') || humanizedText.includes('I cannot') || humanizedText.includes('I apologize')) {
        return false;
    }

    // Check for repetitive content
    const words = humanizedText.split(' ');
    const uniqueWords = new Set(words);
    if (words.length > 20 && uniqueWords.size / words.length < 0.3) {
        return false; // Too repetitive
    }

    return true;
}

/**
 * Test connection to Hugging Face API
 */
export async function testHFConnection() {
    try {
        const client = initializeClient();
        
        // Test with a simple request
        const response = await client.chatCompletion({
            model: HF_CONFIG.fallbackModel, // Use fallback model for testing
            messages: [
                {
                    role: 'user',
                    content: 'Hello, please respond with "Connection successful"'
                }
            ],
            max_tokens: 50,
            provider: HF_CONFIG.fallbackProvider
        });

        return {
            success: true,
            message: 'Hugging Face API connection successful',
            model: HF_CONFIG.fallbackModel,
            response: response.choices[0].message.content
        };
        
    } catch (error) {
        return {
            success: false,
            message: 'Hugging Face API connection failed',
            error: error.message
        };
    }
}

/**
 * Get available models and their status
 */
export function getHFConfiguration() {
    return {
        primaryModel: HF_CONFIG.primaryModel,
        primaryProvider: HF_CONFIG.primaryProvider,
        fallbackModel: HF_CONFIG.fallbackModel,
        fallbackProvider: HF_CONFIG.fallbackProvider,
        maxTokens: HF_CONFIG.maxTokens,
        temperature: HF_CONFIG.temperature,
        hasApiToken: !!HF_CONFIG.apiToken,
        baseUrl: HF_CONFIG.baseUrl
    };
}

/**
 * Batch humanization for multiple texts
 */
export async function batchHumanizeWithHF(texts, options = {}) {
    if (!Array.isArray(texts)) {
        throw new Error('Texts must be an array');
    }

    const results = [];
    const batchSize = 5; // Process in batches to avoid rate limits
    
    for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        const batchPromises = batch.map((text, index) => 
            humanizeTextWithHF(text, { ...options, batchIndex: i + index })
        );
        
        const batchResults = await Promise.allSettled(batchPromises);
        results.push(...batchResults.map(result => 
            result.status === 'fulfilled' ? result.value : { success: false, error: result.reason.message }
        ));
        
        // Add delay between batches
        if (i + batchSize < texts.length) {
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    return results;
}
