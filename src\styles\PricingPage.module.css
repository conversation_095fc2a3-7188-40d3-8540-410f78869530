/* src/styles/PricingPage.module.css */
.pricingContainer {
    max-width: 960px; /* Wider container for potentially multiple cards */
    margin: 2rem auto;
    padding: 2rem 1.5rem; /* Consistent padding */
    text-align: center;
}

.pageTitle {
    font-size: 2.8rem; /* Match Home.module.css title */
    font-weight: 600;
    color: #2c3e50; /* Consistent title color */
    margin-bottom: 0.75rem;
}

.pageSubtitle {
    font-size: 1.25rem; /* Slightly larger subtitle */
    color: #555e68; /* Consistent subtitle color */
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.plansGrid {
    display: grid;
    /* grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); */
    grid-template-columns: 1fr; /* Default to single column for smaller screens */
    gap: 2rem;
    margin-top: 2rem;
    align-items: stretch; /* Make cards of same height if they are in the same row */
}

@media (min-width: 768px) { /* Two columns for medium screens */
    .plansGrid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* @media (min-width: 992px) { // Optional: Three columns for large screens if more plans
    .plansGrid {
        grid-template-columns: repeat(3, 1fr);
    }
} */

.planCard {
    border: 1px solid #e0e0e0;
    border-radius: 10px; /* Slightly more rounded */
    padding: 2rem 1.5rem; /* Adjust padding */
    background-color: #fff;
    box-shadow: 0 4px 15px rgba(0,0,0,0.07);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    display: flex;
    flex-direction: column; /* Ensure content within card flows vertically */
    height: 100%; /* For grid alignment, cards will take full height of their row */
}

.planCard:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.planCard.currentPlan {
    border: 2px solid #0070f3; /* More prominent border for current plan */
    box-shadow: 0 8px 25px rgba(0, 112, 243, 0.2);
    transform: scale(1.02); /* Slightly larger */
}

.planTitle {
    font-size: 1.9rem; /* Slightly larger plan title */
    margin-bottom: 1rem;
    color: #333;
}

.planPrice {
    font-size: 2.5rem; /* Larger price */
    font-weight: bold;
    margin-bottom: 0.25rem;
    color: #0070f3; /* Accent color for price */
}

.pricePer {
    font-size: 1rem;
    color: #666;
    font-weight: normal;
}

.featuresList {
    list-style: none;
    padding: 0;
    margin: 1.5rem 0;
    text-align: left;
    color: #555;
    flex-grow: 1; /* Allows list to take available space, pushing button down */
}

.featuresList li {
    margin-bottom: 0.85rem; /* More spacing */
    padding-left: 1.75rem; /* More padding for icon */
    position: relative;
    font-size: 0.95rem;
}

.featuresList li::before {
    content: '✓'; /* Checkmark */
    color: #28a745; /* Green checkmark */
    position: absolute;
    left: 0;
    font-size: 1.1rem; /* Slightly larger checkmark */
    font-weight: bold;
}

.planButton {
    display: inline-block;
    padding: 0.9rem 1.8rem; /* Larger button padding */
    font-size: 1.05rem; /* Slightly larger button text */
    font-weight: 600;
    color: #fff;
    background-color: #0070f3;
    border: none;
    border-radius: 6px; /* Consistent border radius */
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.2s;
    width: 100%;
    margin-top: auto; /* Pushes button to the bottom of the card */
}

.planButton:hover:not(:disabled) {
    background-color: #005bb5;
}

.planButton:disabled {
    background-color: #a0c7e8;
    cursor: not-allowed;
    opacity: 0.7;
}

.upgradeButton {
    background-color: #28a745; /* Green for upgrade */
}

.upgradeButton:hover:not(:disabled) {
    background-color: #218838;
}

.loadingMessage {
    text-align: center;
    padding: 2rem;
    font-size: 1.2rem;
    color: #555e68;
}

.errorMessage {
    color: #c0392b; /* Darker red */
    text-align: center;
    margin-bottom: 1.5rem; /* Increased margin */
    background-color: #fdecea; /* Lighter red background */
    padding: 12px 15px; /* Adjusted padding */
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    font-weight: 500;
}

.warningMessage {
    color: #e67e22; /* Orange color for warnings */
    text-align: center;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    background-color: #fff8e1;
    padding: 8px;
    border-radius: 4px;
}
