/**
 * Ultra-Optimized Falcon System Test Suite
 * Tests all enhancements for consistent ≤10% AI detection
 */

import { humanizeWithAdvancedLLM, isAdvancedLLMAvailable } from './src/services/falconService.js';
import { validateWithRealTimeDetection, isRealTimeDetectionAvailable, getDetectionAPIStatus } from './src/services/aiDetectionService.js';
import { advancedHumanization } from './src/utils/advancedHumanizer.js';
import { humanizeText } from './src/services/humaneyesService.js';

// Ultra-challenging test content with high AI detection risk
const testSamples = {
    extremeAI: `
        Furthermore, it is important to note that the comprehensive implementation of artificial intelligence systems has consequently led to significant transformations across numerous industries. Moreover, the extensive utilization of machine learning algorithms has therefore resulted in substantial improvements in operational efficiency. Additionally, organizations must systematically evaluate their technological infrastructure to ensure optimal performance and maximize return on investment. In conclusion, the strategic deployment of AI technologies represents a fundamental paradigm shift in contemporary business operations.
    `,
    
    highAI: `
        The implementation of advanced AI systems requires comprehensive analysis and systematic evaluation. Organizations must therefore consider various factors including technological infrastructure, operational efficiency, and strategic alignment. Furthermore, the integration of machine learning algorithms necessitates substantial investment in both human resources and technological capabilities. Consequently, businesses must develop robust frameworks to ensure successful deployment and maximize organizational benefits.
    `,
    
    technicalAI: `
        The RESTful API architecture implementation requires comprehensive consideration of endpoint design patterns, authentication mechanisms, and data serialization formats. Furthermore, developers must systematically implement proper error handling protocols and establish rate limiting mechanisms to maintain system stability. Additionally, the optimization of database queries and caching strategies is essential for ensuring optimal performance across distributed systems.
    `,
    
    academicAI: `
        This research study presents a comprehensive analysis of machine learning methodologies applied to natural language processing tasks. The investigation systematically evaluates various algorithmic approaches and their respective performance metrics. Furthermore, the findings demonstrate significant improvements in accuracy and efficiency when compared to traditional computational methods. Consequently, these results contribute substantially to the existing body of knowledge in artificial intelligence research.
    `
};

/**
 * Test ultra-optimized Falcon parameters
 */
async function testUltraOptimizedParameters() {
    console.log('\n🎯 Testing Ultra-Optimized Falcon Parameters...\n');
    
    const testConfigs = [
        { 
            model: 'falcon-3-7b', 
            aggressiveness: 0.9, 
            targetDetection: 10, 
            label: 'Falcon 3-7B Ultra-Aggressive (≤10%)' 
        },
        { 
            model: 'falcon-h1-7b', 
            aggressiveness: 0.8, 
            targetDetection: 10, 
            label: 'Falcon-H1-7B High-Aggressive (≤10%)' 
        },
        { 
            model: 'falcon-3-10b', 
            aggressiveness: 0.7, 
            targetDetection: 10, 
            label: 'Falcon 3-10B Moderate-Aggressive (≤10%)' 
        }
    ];
    
    for (const config of testConfigs) {
        console.log(`\n--- Testing ${config.label} ---`);
        
        try {
            const result = await humanizeWithAdvancedLLM(testSamples.extremeAI, {
                aggressiveness: config.aggressiveness,
                maintainTone: true,
                targetDetection: config.targetDetection,
                preferredModel: config.model
            });
            
            if (result.success) {
                console.log(`✅ Success with ${result.modelName} (${result.provider})`);
                console.log(`⏱️  Processing time: ${result.processingTime}ms`);
                console.log(`🔄 Multi-pass: ${result.multiPass ? 'Yes' : 'No'}`);
                console.log(`🎯 Target: ≤${config.targetDetection}%`);
                
                if (result.detectionValidation) {
                    console.log(`🔍 Detection: ${result.detectionValidation.score.toFixed(1)}% (${result.detectionValidation.apiName})`);
                    console.log(`✅ Meets target: ${result.detectionValidation.meetsTarget ? 'Yes' : 'No'}`);
                }
                
                // Show transformation quality
                const originalLength = testSamples.extremeAI.length;
                const newLength = result.text.length;
                const changeRatio = ((newLength - originalLength) / originalLength * 100).toFixed(1);
                console.log(`📊 Length change: ${changeRatio}%`);
                
                // Show preview
                const preview = result.text.substring(0, 120) + '...';
                console.log(`📄 Preview: ${preview}`);
            } else {
                console.log(`❌ Failed: ${result.error}`);
            }
        } catch (error) {
            console.log(`❌ Error: ${error.message}`);
        }
    }
}

/**
 * Test real-time detection validation
 */
async function testRealTimeDetection() {
    console.log('\n🔍 Testing Real-time AI Detection Validation...\n');
    
    const detectionAvailable = isRealTimeDetectionAvailable();
    console.log(`Real-time detection available: ${detectionAvailable ? '✅' : '❌'}`);
    
    if (detectionAvailable) {
        const apiStatus = getDetectionAPIStatus();
        console.log('\nAPI Status:');
        Object.entries(apiStatus).forEach(([key, status]) => {
            const icon = status.configured ? '✅' : '❌';
            console.log(`  ${icon} ${status.name}: ${status.configured ? 'Configured' : 'Not configured'}`);
        });
        
        // Test with sample text
        try {
            console.log('\nTesting detection validation with sample text...');
            const validation = await validateWithRealTimeDetection(testSamples.highAI, {
                targetDetection: 10,
                preferredAPI: 'gptzero',
                fallbackAPIs: ['originality', 'sapling']
            });
            
            if (validation.success) {
                console.log(`✅ Detection successful: ${validation.score.toFixed(1)}% (${validation.apiName})`);
                console.log(`🎯 Meets ≤10% target: ${validation.meetsTarget ? 'Yes' : 'No'}`);
                console.log(`💡 Recommendation: ${validation.recommendation.message}`);
                
                if (validation.recommendation.shouldRetry) {
                    console.log(`🔄 Suggested aggressiveness increase: +${validation.recommendation.suggestedAggressiveness}`);
                }
            } else {
                console.log(`❌ Detection failed: ${validation.error}`);
            }
        } catch (error) {
            console.log(`❌ Detection error: ${error.message}`);
        }
    } else {
        console.log('⚠️  No detection APIs configured. Using heuristic validation.');
    }
}

/**
 * Test intelligent model selection
 */
async function testIntelligentModelSelection() {
    console.log('\n🧠 Testing Intelligent Model Selection...\n');
    
    const testCases = [
        { 
            sample: testSamples.extremeAI, 
            label: 'Extreme AI Content',
            expectedModel: 'falcon-180b or falcon-3-10b'
        },
        { 
            sample: testSamples.technicalAI, 
            label: 'Technical AI Content',
            expectedModel: 'falcon-3-10b or falcon-h1-7b'
        },
        { 
            sample: testSamples.academicAI, 
            label: 'Academic AI Content',
            expectedModel: 'falcon-3-7b or falcon-h1-7b'
        }
    ];
    
    for (const testCase of testCases) {
        console.log(`\n--- Testing ${testCase.label} ---`);
        console.log(`Expected model selection: ${testCase.expectedModel}`);
        
        try {
            const result = await humanizeText(testCase.sample, {
                aggressiveness: 0.8,
                maintainTone: true,
                targetDetection: 10,
                method: 'auto'
            });
            
            if (result.success) {
                console.log(`✅ Selected model: ${result.modelName || result.model || 'Unknown'}`);
                console.log(`🏢 Provider: ${result.provider || 'Unknown'}`);
                console.log(`🔧 Method: ${result.actualMethod || result.method}`);
                console.log(`⏱️  Processing time: ${result.totalProcessingTime || result.processingTime || 'N/A'}ms`);
                
                if (result.detectionValidation) {
                    console.log(`🔍 Detection score: ${result.detectionValidation.score.toFixed(1)}%`);
                }
            } else {
                console.log(`❌ Failed: ${result.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.log(`❌ Error: ${error.message}`);
        }
    }
}

/**
 * Test multi-pass processing effectiveness
 */
async function testMultiPassProcessing() {
    console.log('\n🔄 Testing Multi-Pass Processing...\n');
    
    try {
        console.log('Testing with extreme AI content requiring multi-pass refinement...');
        
        const result = await advancedHumanization(testSamples.extremeAI, {
            aggressiveness: 0.9,
            maintainTone: true,
            targetDetection: 10,
            useModelBased: true,
            fallbackToPatterns: false
        });
        
        if (result.success) {
            console.log(`✅ Multi-pass processing successful!`);
            console.log(`🔧 Method: ${result.method}`);
            console.log(`🤖 Model: ${result.model || 'N/A'}`);
            console.log(`⏱️  Total processing time: ${result.processingTime}ms`);
            console.log(`🎯 Detection target: ≤${result.detectionTarget || 10}%`);
            
            // Check for multi-pass indicators
            if (result.method && result.method.includes('multipass')) {
                console.log(`🔄 Multi-pass refinement: Applied`);
            }
            
            // Quality metrics
            const originalLength = testSamples.extremeAI.length;
            const transformationRatio = ((result.newLength - originalLength) / originalLength * 100).toFixed(1);
            console.log(`📊 Transformation ratio: ${transformationRatio}%`);
            
            // Show quality preview
            const preview = result.text.substring(0, 150) + '...';
            console.log(`📄 Quality preview: ${preview}`);
        } else {
            console.log(`❌ Multi-pass processing failed: ${result.error}`);
        }
    } catch (error) {
        console.log(`❌ Multi-pass error: ${error.message}`);
    }
}

/**
 * Performance benchmark for ≤10% detection
 */
async function benchmarkUltraPerformance() {
    console.log('\n⚡ Ultra-Performance Benchmark (≤10% Detection)...\n');
    
    const iterations = 3;
    const testText = testSamples.highAI;
    const results = [];
    
    for (let i = 0; i < iterations; i++) {
        console.log(`Running ultra-optimization iteration ${i + 1}/${iterations}...`);
        
        try {
            const startTime = Date.now();
            const result = await humanizeText(testText, {
                aggressiveness: 0.8,
                targetDetection: 10,
                method: 'auto'
            });
            const endTime = Date.now();
            
            if (result.success) {
                const totalTime = endTime - startTime;
                results.push({
                    time: totalTime,
                    model: result.modelName || result.model,
                    method: result.actualMethod || result.method,
                    detectionScore: result.detectionValidation?.score || null
                });
                
                console.log(`✅ Iteration ${i + 1}: ${totalTime}ms, Model: ${result.modelName || 'Unknown'}`);
                if (result.detectionValidation) {
                    console.log(`   Detection: ${result.detectionValidation.score.toFixed(1)}%`);
                }
            } else {
                console.log(`❌ Iteration ${i + 1} failed`);
            }
        } catch (error) {
            console.log(`❌ Iteration ${i + 1} error: ${error.message}`);
        }
    }
    
    if (results.length > 0) {
        const avgTime = results.reduce((sum, r) => sum + r.time, 0) / results.length;
        const minTime = Math.min(...results.map(r => r.time));
        const maxTime = Math.max(...results.map(r => r.time));
        const avgDetection = results.filter(r => r.detectionScore).reduce((sum, r) => sum + r.detectionScore, 0) / results.filter(r => r.detectionScore).length;
        
        console.log(`\n📊 Ultra-Performance Summary:`);
        console.log(`   Average time: ${avgTime.toFixed(0)}ms`);
        console.log(`   Min time: ${minTime}ms`);
        console.log(`   Max time: ${maxTime}ms`);
        if (!isNaN(avgDetection)) {
            console.log(`   Average detection: ${avgDetection.toFixed(1)}%`);
            console.log(`   ≤10% target met: ${avgDetection <= 10 ? '✅' : '❌'}`);
        }
        console.log(`   Success rate: ${(results.length / iterations * 100).toFixed(0)}%`);
    }
}

/**
 * Main test runner for ultra-optimized Falcon system
 */
async function runUltraFalconTests() {
    console.log('🦅 ULTRA-OPTIMIZED FALCON SYSTEM TEST SUITE');
    console.log('===========================================');
    console.log('Target: Consistent ≤10% AI Detection Scores\n');
    
    try {
        // Test 1: Ultra-optimized parameters
        await testUltraOptimizedParameters();
        
        // Test 2: Real-time detection validation
        await testRealTimeDetection();
        
        // Test 3: Intelligent model selection
        await testIntelligentModelSelection();
        
        // Test 4: Multi-pass processing
        await testMultiPassProcessing();
        
        // Test 5: Performance benchmark
        await benchmarkUltraPerformance();
        
        console.log('\n🎉 Ultra-Optimized Falcon Test Suite Complete!');
        console.log('\n📋 Enhancement Summary:');
        console.log('   ✅ Fine-tuned Falcon model parameters');
        console.log('   ✅ Ultra-sophisticated prompting strategy');
        console.log('   ✅ Multi-pass processing for ≤10% detection');
        console.log('   ✅ Real-time AI detection validation');
        console.log('   ✅ Intelligent model selection with content analysis');
        console.log('   ✅ Optimized environment configuration');
        
        console.log('\n🎯 Expected Results:');
        console.log('   • Consistent ≤10% AI detection scores');
        console.log('   • Automatic model selection based on content complexity');
        console.log('   • Real-time quality validation with auto-retry');
        console.log('   • Multi-pass refinement for challenging content');
        console.log('   • Enhanced natural language flow and authenticity');
        
    } catch (error) {
        console.error('\n❌ Test suite error:', error);
    }
}

// Run the ultra-optimized tests
runUltraFalconTests().catch(console.error);
