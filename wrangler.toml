# Cloudflare Pages configuration for GhostLayer
name = "ghostlayer"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

[build.environment]
NODE_VERSION = "18"
NODE_ENV = "production"
NEXT_TELEMETRY_DISABLED = "1"

# Environment variables (set these in Cloudflare dashboard)
[env.production]
name = "ghostlayer"

[env.preview]
name = "ghostlayer-preview"

# Pages configuration
[[pages_build_output_dir]]
directory = ".next"

# Custom headers
[[headers]]
for = "/*"
[headers.values]
X-Frame-Options = "DENY"
X-Content-Type-Options = "nosniff"
Referrer-Policy = "strict-origin-when-cross-origin"
Permissions-Policy = "camera=(), microphone=(), geolocation=()"
Strict-Transport-Security = "max-age=31536000; includeSubDomains"

[[headers]]
for = "/api/*"
[headers.values]
Access-Control-Allow-Origin = "https://ghostlayer.pages.dev"
Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With"
Cache-Control = "no-store, max-age=0"

[[headers]]
for = "/_next/static/*"
[headers.values]
Cache-Control = "public, max-age=31536000, immutable"

# Redirects
[[redirects]]
from = "/home"
to = "/"
status = 301

[[redirects]]
from = "/old-page"
to = "/"
status = 301

# Rewrites
[[redirects]]
from = "/health"
to = "/api/health"
status = 200

# Worker configuration for API routes
[durable_objects]
bindings = []

[vars]
ENVIRONMENT = "production"
