/**
 * Simple test page to verify Next.js is working
 */

import React from 'react';
import Head from 'next/head';
import Link from 'next/link';

export default function TestPage() {
    return (
        <>
            <Head>
                <title>Test Page - GhostLayer</title>
            </Head>
            <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
                <h1>🎉 GhostLayer Test Page</h1>
                <p>If you can see this page, Next.js is working correctly!</p>
                <div style={{ 
                    background: '#f0f9ff', 
                    border: '1px solid #0ea5e9', 
                    padding: '16px', 
                    borderRadius: '8px',
                    marginTop: '20px'
                }}>
                    <h3>✅ System Status</h3>
                    <ul>
                        <li>✅ Next.js server running</li>
                        <li>✅ React components rendering</li>
                        <li>✅ Page routing working</li>
                        <li>✅ CSS styling applied</li>
                    </ul>
                </div>
                <div style={{ marginTop: '20px' }}>
                    <Link
                        href="/"
                        style={{
                            background: '#3b82f6',
                            color: 'white',
                            padding: '10px 20px',
                            textDecoration: 'none',
                            borderRadius: '6px',
                            display: 'inline-block'
                        }}
                    >
                        ← Back to Home
                    </Link>
                </div>
            </div>
        </>
    );
}
