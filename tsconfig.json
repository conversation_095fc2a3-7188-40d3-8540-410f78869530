{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/pages/*": ["./src/pages/*"], "@/utils/*": ["./src/utils/*"], "@/lib/*": ["./src/lib/*"], "@/services/*": ["./src/services/*"], "@/hooks/*": ["./src/hooks/*"], "@/contexts/*": ["./src/contexts/*"], "@/styles/*": ["./src/styles/*"], "@/types/*": ["./src/types/*"]}, "types": ["jest", "@testing-library/jest-dom"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "out", "dist", "build"]}