# AI Text Modification Web App Workflow

The following diagram illustrates the user journey and data flow from text input to receiving the modified output and AI detection results.

```mermaid
sequenceDiagram
    participant User
    participant Frontend (Next.js)
    participant Backend API (Next.js API Route)
    participant Text Modifiers (Own Algorithms)
    participant External AI Detection API (e.g., GPTZero)

    User->>Frontend: 1. Pastes or uploads text
    User->>Frontend: 2. Clicks "Process Text" button
    Frontend->>Backend API: 3. Sends text to /api/process
    activate Backend API

    Backend API->>Text Modifiers: 4. Applies text modifications (paraphrasing, error injection, style changes)
    activate Text Modifiers
    Text Modifiers-->>Backend API: 5. Returns modified text
    deactivate Text Modifiers

    alt Optional: AI Detection Check
        Backend API->>External AI Detection API: 6. Sends modified text for detection
        activate External AI Detection API
        External AI Detection API-->>Backend API: 7. Returns detection score/status
        deactivate External AI Detection API
        Backend API->>Backend API: 8. Combines modified text & detection results
    end

    Backend API-->>Frontend: 9. Sends processed text (and detection results)
    deactivate Backend API

    Frontend->>User: 10. Displays modified text and AI detection score
    User->>Frontend: 11. Copies or downloads modified text
```

## Workflow Steps Explained:

1.  **User Input:** The user provides the initial AI-generated text via the frontend.
2.  **Initiate Processing:** The user triggers the modification process (e.g., by clicking a button).
3.  **Frontend to Backend:** The frontend sends the raw text to the designated backend API endpoint (e.g., `/api/process`).
4.  **Text Modification:** The backend API calls internal modules (referred to as "Text Modifiers" or "Own Algorithms") which apply various transformations like:
    *   Smart Paraphrasing (simplified for MVP)
    *   Controlled Grammar Mistakes
    *   Human-like Anomalies
    *   Style Optimization
5.  **Return Modified Text:** The Text Modifiers module processes the text and returns the altered version to the API handler.
6.  **Optional AI Detection:** The backend, using the modified text, makes a request to an external AI detection service (e.g., GPTZero API).
7.  **Receive Detection Results:** The external AI detection service analyzes the text and returns a score or classification (e.g., "likely AI-generated" with a probability).
8.  **Combine Results:** The backend API handler takes the modified text and the detection results, preparing them for the response.
9.  **Backend to Frontend:** The backend API sends a response to the frontend containing the modified text and, if the detection step was performed, the AI detection score/status.
10. **Display to User:** The frontend updates the UI to show the user the "humanized" version of their text and the results from the AI detection test.
11. **User Action:** The user can then choose to copy the modified text to their clipboard or download it as a file.
