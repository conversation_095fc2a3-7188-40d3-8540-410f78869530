.resultsDisplay {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin: 2rem auto;
    max-width: 800px;
    overflow: hidden;
}

.header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.25rem 1.5rem;
    background: linear-gradient(135deg, #f0fdf4, #f0f9ff);
    border-bottom: 1px solid #e5e7eb;
}

.icon {
    color: #16a34a;
    flex-shrink: 0;
}

.header h4 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.content {
    padding: 1.5rem;
}

.loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 2rem;
    text-align: center;
}

.loadingSpinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #0ea5e9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.statusSection {
    margin-bottom: 1.25rem;
}

.statusItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.75rem;
}

.statusBadge {
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
}

.statusHuman {
    background: #f0fdf4;
    color: #15803d;
    border: 1px solid #bbf7d0;
}

.statusAI {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.statusMixed {
    background: #fffbeb;
    color: #d97706;
    border: 1px solid #fed7aa;
}

.scoreSection {
    margin-bottom: 1.25rem;
}

.scoreItem {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.scoreDisplay {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.scoreValue {
    font-size: 1.5rem;
    font-weight: 700;
    color: #0ea5e9;
    min-width: 80px;
}

.scoreBar {
    flex: 1;
    height: 8px;
    background: #e5e7eb;
    border-radius: 0.25rem;
    overflow: hidden;
}

.scoreProgress {
    height: 100%;
    background: linear-gradient(90deg, #22c55e, #f59e0b, #ef4444);
    border-radius: 0.25rem;
    transition: width 0.3s ease;
}

.messageSection {
    margin-bottom: 1.25rem;
}

.message {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: #f9fafb;
    border-radius: 0.5rem;
    color: #374151;
    line-height: 1.6;
}

.summary {
    padding: 1rem;
    background: #f0fdf4;
    border-radius: 0.75rem;
    border: 1px solid #bbf7d0;
}

.summaryText {
    margin: 0;
    color: #15803d;
    font-weight: 500;
    text-align: center;
}

.errorMessage {
    color: #dc2626;
    background: #fef2f2;
    padding: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid #fecaca;
}

/* Responsive Design */
@media (max-width: 768px) {
    .resultsDisplay {
        margin: 1.5rem 1rem;
    }
    
    .header {
        padding: 1rem 1.25rem;
    }
    
    .content {
        padding: 1.25rem;
    }
    
    .statusItem {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .scoreDisplay {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .scoreValue {
        min-width: auto;
    }
}
