/* src/components/ads/AdBanner.module.css */
.adBanner {
    display: flex; /* Using flex to center content like placeholders */
    justify-content: center;
    align-items: center;
    width: 100%; /* Take full available width by default */
    min-height: 50px; /* Minimum height, actual height determined by ad content */
    margin: 1.5rem auto; /* Centered with top/bottom margin */
    /* background-color: #f9f9f9; */ /* Light background, useful for debugging placement */
    /* border: 1px solid #eee; */ /* Light border, useful for debugging placement */
    text-align: center;
    overflow: hidden; /* Prevent ad content from unexpectedly overflowing */
}

.adPlaceholder {
   background-color: #e9ecef; /* Light grey, distinct from page background */
   border: 1px dashed #adb5bd; /* Dashed border for placeholder */
   padding: 20px; /* More padding for visibility */
   color: #6c757d; /* Muted text color */
   font-size: 0.9rem;
   width: 100%; /* Ensure placeholder takes up banner width */
   box-sizing: border-box;
   display: flex;
   justify-content: center;
   align-items: center;
}

.adPlaceholder p {
    margin: 0;
}

/* Styling for the <ins> tag often used by AdSense, if not handled by provider's CSS */
/* This ensures it behaves as a block and can be styled/sized by parent or ad script. */
.adBanner ins.adsbygoogle {
   display: block !important; /* Often needed for AdSense to correctly fill space */
   background-color: transparent !important; /* AdSense sometimes adds its own background */
   text-align: center !important; /* Ensure ad content within ins is centered */
}

/* Add styles for specific ad sizes if you plan to use fixed-size ad units and want to ensure space is reserved */
/* Example:
.leaderboard {
    min-height: 90px;
    max-width: 728px;
}
.mediumRectangle {
    min-height: 250px;
    max-width: 300px;
}
*/
