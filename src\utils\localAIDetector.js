/**
 * Local AI Detection System
 * Provides AI detection scoring based on pattern analysis when external APIs are unavailable
 * This is a fallback system for testing purposes
 */

/**
 * Analyze text for AI-like patterns and provide a detection score
 * @param {string} text - Text to analyze
 * @returns {Object} Detection result with score and analysis
 */
export function analyzeAIPatterns(text) {
    const analysis = {
        score: 0,
        confidence: 0,
        patterns: {},
        recommendations: []
    };

    // Pattern categories with weights and detection logic
    const patternCategories = {
        formalLanguageOveruse: {
            weight: 25,
            patterns: [
                /\b(furthermore|moreover|additionally|consequently|therefore|thus|hence|accordingly|subsequently|nevertheless|nonetheless|ultimately|essentially|fundamentally)\b/gi,
                /\b(comprehensive|substantial|significant|considerable|extensive|thorough|detailed|systematic|methodical|strategic|effective|efficient|successful|optimal|ideal|perfect)\b/gi,
                /\b(utilize|implement|demonstrate|facilitate|optimize|enhance|leverage|establish|maintain|ensure|provide|develop|create|generate|produce|achieve|accomplish)\b/gi
            ],
            description: 'Overuse of formal vocabulary and transitions'
        },
        
        repetitiveStructures: {
            weight: 20,
            patterns: [
                /^(It is important to note that|It should be noted that|It is worth mentioning that|It is essential to understand that|It is crucial to recognize that)\s/gmi,
                /^(The|This|These|Those)\s+(fact|reality|truth|issue|problem|challenge|opportunity|benefit|advantage|disadvantage|aspect|element|component|factor|consideration)\s+(is|are)\s+that\s+/gmi,
                /\b(first|second|third|fourth|fifth|finally)\b.*?\b(first|second|third|fourth|fifth|finally)\b/gi
            ],
            description: 'Repetitive sentence starters and structures'
        },
        
        passiveVoiceExcess: {
            weight: 15,
            patterns: [
                /\b(is|are|was|were|been|being)\s+(being\s+)?(analyzed|examined|studied|investigated|explored|considered|evaluated|assessed|reviewed|discussed|addressed|handled|managed|processed|implemented|executed|performed|conducted)\b/gi,
                /\b(can be|will be|should be|must be|may be|might be)\s+(achieved|accomplished|attained|obtained|acquired|gained|secured|established|created|developed|designed|constructed|built)\b/gi
            ],
            description: 'Excessive use of passive voice constructions'
        },
        
        intensifierClusters: {
            weight: 18,
            patterns: [
                /\b(extremely|incredibly|remarkably|exceptionally|extraordinarily|tremendously|enormously|immensely|vastly|hugely|massively)\b/gi,
                /\b(absolutely|completely|entirely|fully|totally|wholly|utterly|thoroughly|comprehensively|extensively|perfectly|ideally|optimally)\b/gi,
                /\b(significantly|substantially|considerably|notably|particularly|especially|specifically|precisely|exactly)\b/gi
            ],
            description: 'Clustering of intensifiers and superlatives'
        },
        
        hedgingOveruse: {
            weight: 12,
            patterns: [
                /\b(might|could|would|should|may|can|possibly|probably|likely|perhaps|maybe|potentially)\b.*?\b(might|could|would|should|may|can|possibly|probably|likely|perhaps|maybe|potentially)\b/gi,
                /\b(seems?|appears?|suggests?|indicates?|implies?|tends?)\s+to\b.*?\b(seems?|appears?|suggests?|indicates?|implies?|tends?)\s+to\b/gi
            ],
            description: 'Excessive hedging and uncertainty markers'
        },
        
        structuralPredictability: {
            weight: 22,
            patterns: [
                /^.{50,80}\.\s+.{50,80}\.\s+.{50,80}\./gm, // Similar sentence lengths
                /\b(In conclusion|To summarize|In summary|To conclude|Finally|Lastly|In the final analysis|Ultimately|All things considered)\b/gi,
                /\w+ represents a \w+ that/gi,
                /organizations across various industries/gi,
                /increasingly implementing/gi
            ],
            description: 'Predictable structural patterns and conclusions'
        },
        
        technicalJargonDensity: {
            weight: 8,
            patterns: [
                /\b(operational efficiency|competitive advantages|strategic planning|performance indicators|decision-making processes|advanced analytics|predictive modeling|sophisticated systems|actionable insights|business outcomes)\b/gi,
                /\b(comprehensive strategic planning|substantial financial investment|numerous studies demonstrate|significant improvements|productivity metrics)\b/gi
            ],
            description: 'High density of technical jargon and business speak'
        }
    };

    const wordCount = text.split(/\s+/).length;
    let totalPatternScore = 0;

    // Analyze each pattern category
    Object.entries(patternCategories).forEach(([category, config]) => {
        let categoryMatches = 0;
        
        config.patterns.forEach(pattern => {
            const matches = text.match(pattern);
            if (matches) {
                categoryMatches += matches.length;
            }
        });
        
        if (categoryMatches > 0) {
            const categoryScore = Math.min(config.weight, (categoryMatches / wordCount) * 1000 * config.weight);
            analysis.patterns[category] = {
                matches: categoryMatches,
                score: categoryScore,
                description: config.description
            };
            totalPatternScore += categoryScore;
        }
    });

    // Calculate additional metrics
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    const sentenceLengthVariation = calculateSentenceLengthVariation(sentences);
    
    // Sentence length uniformity penalty (AI tends to write uniform sentences)
    if (avgSentenceLength > 80 && sentenceLengthVariation < 0.3) {
        totalPatternScore += 15;
        analysis.patterns.sentenceUniformity = {
            matches: sentences.length,
            score: 15,
            description: 'Uniform sentence lengths indicate AI writing'
        };
    }

    // Paragraph structure analysis
    const paragraphs = text.split('\n\n').filter(p => p.trim().length > 0);
    if (paragraphs.length > 1) {
        const paragraphLengths = paragraphs.map(p => p.length);
        const paragraphVariation = calculateVariation(paragraphLengths);
        
        if (paragraphVariation < 0.2) {
            totalPatternScore += 10;
            analysis.patterns.paragraphUniformity = {
                matches: paragraphs.length,
                score: 10,
                description: 'Uniform paragraph lengths suggest AI generation'
            };
        }
    }

    // Calculate final score (0-100 scale)
    analysis.score = Math.min(100, Math.round(totalPatternScore));
    
    // Determine confidence level
    const patternCount = Object.keys(analysis.patterns).length;
    if (patternCount >= 5) {
        analysis.confidence = 0.9;
    } else if (patternCount >= 3) {
        analysis.confidence = 0.7;
    } else if (patternCount >= 1) {
        analysis.confidence = 0.5;
    } else {
        analysis.confidence = 0.3;
    }

    // Generate recommendations
    if (analysis.score > 70) {
        analysis.recommendations.push('High AI detection risk - requires aggressive humanization');
    } else if (analysis.score > 40) {
        analysis.recommendations.push('Moderate AI detection risk - requires standard humanization');
    } else if (analysis.score > 20) {
        analysis.recommendations.push('Low AI detection risk - light humanization recommended');
    } else {
        analysis.recommendations.push('Very low AI detection risk - minimal processing needed');
    }

    return analysis;
}

/**
 * Calculate sentence length variation coefficient
 */
function calculateSentenceLengthVariation(sentences) {
    if (sentences.length < 2) return 1;
    
    const lengths = sentences.map(s => s.trim().length);
    const mean = lengths.reduce((a, b) => a + b, 0) / lengths.length;
    const variance = lengths.reduce((sum, length) => sum + Math.pow(length - mean, 2), 0) / lengths.length;
    const stdDev = Math.sqrt(variance);
    
    return stdDev / mean; // Coefficient of variation
}

/**
 * Calculate variation coefficient for any array of numbers
 */
function calculateVariation(values) {
    if (values.length < 2) return 1;
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    return stdDev / mean;
}

/**
 * Compare two texts and calculate improvement in AI detection score
 */
export function compareAIDetection(originalText, humanizedText) {
    const originalAnalysis = analyzeAIPatterns(originalText);
    const humanizedAnalysis = analyzeAIPatterns(humanizedText);
    
    const improvement = originalAnalysis.score - humanizedAnalysis.score;
    const improvementPercentage = originalAnalysis.score > 0 ? (improvement / originalAnalysis.score) * 100 : 0;
    
    return {
        original: originalAnalysis,
        humanized: humanizedAnalysis,
        improvement: {
            absolute: improvement,
            percentage: improvementPercentage,
            success: humanizedAnalysis.score <= 20 // Target threshold
        }
    };
}

/**
 * Batch analyze multiple texts
 */
export function batchAnalyze(texts) {
    return texts.map((text, index) => ({
        index,
        text: text.substring(0, 100) + '...',
        analysis: analyzeAIPatterns(text)
    }));
}

/**
 * Generate detailed report for AI detection analysis
 */
export function generateDetectionReport(analysis) {
    let report = `AI Detection Analysis Report\n`;
    report += `================================\n\n`;
    report += `Overall Score: ${analysis.score}% (${analysis.score <= 10 ? 'EXCELLENT' : analysis.score <= 20 ? 'GOOD' : analysis.score <= 40 ? 'MODERATE' : 'HIGH'} AI detection risk)\n`;
    report += `Confidence: ${(analysis.confidence * 100).toFixed(0)}%\n\n`;
    
    if (Object.keys(analysis.patterns).length > 0) {
        report += `Detected Patterns:\n`;
        report += `------------------\n`;
        Object.entries(analysis.patterns).forEach(([pattern, data]) => {
            report += `• ${pattern}: ${data.matches} matches (${data.score.toFixed(1)} points)\n`;
            report += `  ${data.description}\n\n`;
        });
    }
    
    if (analysis.recommendations.length > 0) {
        report += `Recommendations:\n`;
        report += `----------------\n`;
        analysis.recommendations.forEach(rec => {
            report += `• ${rec}\n`;
        });
    }
    
    return report;
}
