// src/pages/api/stripe/create-checkout-session.js
import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth]"; // Adjust path relative to `pages/api/stripe`
import stripe from "../../../src/lib/stripe";       // Initialized Stripe instance
import prisma from "../../../src/lib/prisma";       // Initialized Prisma instance

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        res.setHeader('Allow', 'POST');
        return res.status(405).end('Method Not Allowed');
    }

    try {
        const session = await getServerSession(req, res, authOptions);
        if (!session || !session.user || !session.user.id) {
            return res.status(401).json({ error: "Unauthorized", message: "You must be signed in to create a checkout session." });
        }

        const { priceId } = req.body; // The ID of the Stripe Price object for the selected plan

        if (!priceId || typeof priceId !== 'string') {
            return res.status(400).json({ error: "Bad Request", message: "Price ID (priceId) is required in the request body and must be a string." });
        }

        const userId = session.user.id;
        let appUser = await prisma.user.findUnique({
            where: { id: userId },
        });

        if (!appUser) {
            // This should not happen if the session is valid and user.id is from our database
            console.error(`User with ID ${userId} found in session but not in database.`);
            return res.status(404).json({ error: "User not found", message: "Authenticated user not found in our database." });
        }

        let stripeCustomerId = appUser.stripeCustomerId;

        if (!stripeCustomerId) {
            // Create a new Stripe Customer for this user
            console.log(`Creating Stripe customer for user ID: ${userId}, email: ${session.user.email}`);
            const customer = await stripe.customers.create({
                email: session.user.email, // Ensure email is in session or appUser
                name: session.user.name || appUser.name,   // Ensure name is in session or appUser
                metadata: {
                    userId: userId, // Link Stripe customer to your internal application user ID
                },
            });
            stripeCustomerId = customer.id;

            // Update our user record with the new Stripe Customer ID
            appUser = await prisma.user.update({
                where: { id: userId },
                data: { stripeCustomerId: stripeCustomerId },
            });
            console.log(`Stripe customer ID ${stripeCustomerId} created and saved for user ID: ${userId}`);
        } else {
            console.log(`Using existing Stripe customer ID ${stripeCustomerId} for user ID: ${userId}`);
        }

        const appBaseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';

        // Create a Stripe Checkout Session
        // For more details: https://stripe.com/docs/api/checkout/sessions/create
        const checkoutSessionParams = {
            customer: stripeCustomerId,
            payment_method_types: ['card'],
            line_items: [{ price: priceId, quantity: 1 }],
            mode: 'subscription',
            allow_promotion_codes: true, // Set to true if you want to allow discount codes
            success_url: `${appBaseUrl}/profile?subscribe=success&session_id={CHECKOUT_SESSION_ID}`, // Stripe replaces {CHECKOUT_SESSION_ID}
            cancel_url: `${appBaseUrl}/pricing?subscribe=cancel`,
            client_reference_id: userId, // Helps identify the session with your internal user ID in webhooks
            // To prefill email on Stripe Checkout page (optional, as Stripe customer object might already handle this)
            // customer_email: appUser.email,
            // You can also pass subscription metadata if needed:
            // subscription_data: {
            //   metadata: {
            //     userId: userId
            //   }
            // }
        };

        console.log("Creating Stripe Checkout session with params:", checkoutSessionParams);
        const checkoutSession = await stripe.checkout.sessions.create(checkoutSessionParams);

        res.status(200).json({ sessionId: checkoutSession.id });

    } catch (error) {
        console.error("Error creating Stripe Checkout session:", error.message);
        // Check for specific Stripe error types if needed
        // e.g. if (error instanceof Stripe.errors.StripeCardError) { ... }
        res.status(500).json({
            error: "Internal Server Error",
            message: error.message || "Could not create Stripe Checkout session due to an unexpected error."
        });
    }
}
