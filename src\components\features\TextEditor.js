import React, { useState, useRef } from 'react';
import styles from './TextEditor.module.css';

const TextEditor = ({ inputText, onInputChange, outputText, isLoading, onCopy, onDownload }) => {
    const [inputFocused, setInputFocused] = useState(false);
    const [outputFocused, setOutputFocused] = useState(false);
    const [copySuccess, setCopySuccess] = useState(false);
    const inputRef = useRef(null);
    const outputRef = useRef(null);

    const handleCopy = async (text, type) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopySuccess(type);
            setTimeout(() => setCopySuccess(false), 2000);
            if (onCopy) onCopy(text, type);
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
    };

    const handleClear = () => {
        if (onInputChange) {
            onInputChange({ target: { value: '' } });
        }
    };

    const getWordCount = (text) => {
        return text.trim() ? text.trim().split(/\s+/).length : 0;
    };

    const getCharCount = (text) => {
        return text.length;
    };

    return (
        <div className={styles.editorContainer}>
            {/* Input Editor */}
            <div className={styles.editorSection}>
                <div className={styles.editorHeader}>
                    <div className={styles.headerLeft}>
                        <h3 className={styles.editorTitle}>
                            <svg className={styles.titleIcon} width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                <polyline points="10,9 9,9 8,9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            Original Text
                        </h3>
                        <div className={styles.stats}>
                            <span className={styles.stat}>
                                {getWordCount(inputText)} words
                            </span>
                            <span className={styles.stat}>
                                {getCharCount(inputText)} characters
                            </span>
                        </div>
                    </div>
                    <div className={styles.headerActions}>
                        {inputText && (
                            <>
                                <button
                                    className={styles.actionButton}
                                    onClick={() => handleCopy(inputText, 'input')}
                                    title="Copy original text"
                                >
                                    {copySuccess === 'input' ? (
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <polyline points="20,6 9,17 4,12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    ) : (
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    )}
                                </button>
                                <button
                                    className={styles.actionButton}
                                    onClick={handleClear}
                                    title="Clear text"
                                >
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <polyline points="3,6 5,6 21,6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                </button>
                            </>
                        )}
                    </div>
                </div>
                <div className={`${styles.editorWrapper} ${inputFocused ? styles.focused : ''}`}>
                    <textarea
                        ref={inputRef}
                        className={styles.textarea}
                        value={inputText}
                        onChange={onInputChange}
                        onFocus={() => setInputFocused(true)}
                        onBlur={() => setInputFocused(false)}
                        placeholder="Paste your AI-generated text here to make it more human-like..."
                        disabled={isLoading}
                    />
                    {isLoading && (
                        <div className={styles.loadingOverlay}>
                            <div className={styles.loadingSpinner}></div>
                        </div>
                    )}
                </div>
            </div>

            {/* Arrow Separator */}
            <div className={styles.separator}>
                <div className={styles.arrowContainer}>
                    <svg className={styles.arrow} width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <line x1="5" y1="12" x2="19" y2="12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <polyline points="12,5 19,12 12,19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </div>
            </div>

            {/* Output Editor */}
            <div className={styles.editorSection}>
                <div className={styles.editorHeader}>
                    <div className={styles.headerLeft}>
                        <h3 className={styles.editorTitle}>
                            <svg className={styles.titleIcon} width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                <circle cx="11" cy="14" r="2" stroke="currentColor" strokeWidth="2"/>
                                <path d="M21 15c-1-1-3-1-3-1s-2 0-3 1" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            Humanized Text
                        </h3>
                        <div className={styles.stats}>
                            <span className={styles.stat}>
                                {getWordCount(outputText)} words
                            </span>
                            <span className={styles.stat}>
                                {getCharCount(outputText)} characters
                            </span>
                        </div>
                    </div>
                    <div className={styles.headerActions}>
                        {outputText && (
                            <>
                                <button
                                    className={styles.actionButton}
                                    onClick={() => handleCopy(outputText, 'output')}
                                    title="Copy humanized text"
                                >
                                    {copySuccess === 'output' ? (
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <polyline points="20,6 9,17 4,12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    ) : (
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                            <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    )}
                                </button>
                                <button
                                    className={styles.actionButton}
                                    onClick={() => onDownload && onDownload(outputText)}
                                    title="Download as text file"
                                >
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        <polyline points="7,10 12,15 17,10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                </button>
                            </>
                        )}
                    </div>
                </div>
                <div className={`${styles.editorWrapper} ${outputFocused ? styles.focused : ''}`}>
                    <textarea
                        ref={outputRef}
                        className={`${styles.textarea} ${styles.textareaOutput}`}
                        value={outputText}
                        onFocus={() => setOutputFocused(true)}
                        onBlur={() => setOutputFocused(false)}
                        placeholder="Your humanized text will appear here..."
                        readOnly
                    />
                    {!outputText && !isLoading && (
                        <div className={styles.emptyState}>
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" className={styles.emptyIcon}>
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                <polyline points="14,2 14,8 20,8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                                <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                            <p>Enter text and click &quot;Humanize Text&quot; to see results</p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default TextEditor;
