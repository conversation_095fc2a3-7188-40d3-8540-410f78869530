// netlify/functions/health.js
// Health check function for Netlify

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json',
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    // Allow GET method for health checks
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ message: `Method ${event.httpMethod} Not Allowed` }),
        };
    }

    try {
        const healthData = {
            status: 'ok',
            timestamp: new Date().toISOString(),
            service: 'GhostLayer API',
            version: '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            platform: 'Netlify Functions'
        };

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(healthData),
        };

    } catch (error) {
        console.error("Error in health check:", error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                status: 'error',
                message: 'Health check failed',
                timestamp: new Date().toISOString()
            }),
        };
    }
};
