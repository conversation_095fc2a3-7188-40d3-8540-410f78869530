# GhostLayer Vercel Deployment Guide

## 🚀 Why Vercel is the Best Choice

**Vercel is the recommended platform for GhostLayer** because:
- **100GB bandwidth/month** (handles 300K+ users easily)
- **Perfect Next.js integration** (zero configuration needed)
- **Automatic serverless functions** from your API routes
- **Global Edge Network** with 40+ regions
- **Free SSL certificates** and custom domains
- **Built-in analytics** and performance monitoring

## 📋 Pre-Deployment Checklist

### 1. Required Environment Variables

Set these in your Vercel dashboard (Project Settings → Environment Variables):

#### Core Application
```bash
NODE_ENV=production
NEXTAUTH_SECRET=your_super_strong_random_nextauth_secret_here
NEXTAUTH_URL=https://your-domain.vercel.app
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
NEXT_PUBLIC_APP_NAME=GhostLayer
```

#### Database (Required)
```bash
# For PostgreSQL (recommended for production)
DATABASE_URL=****************************************/database_name?schema=public

# Alternative: PlanetScale (MySQL-compatible, free tier available)
# DATABASE_URL=mysql://username:password@host:3306/database_name
```

#### External APIs (Required for full functionality)
```bash
# GPTZero API for AI detection
GPTZERO_API_KEY=your_gptzero_api_key_here

# AI Paraphrasing Services (at least one required)
OPENAI_API_KEY=your_openai_api_key_here
GROQ_API_KEY=your_groq_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

#### OAuth (Required for user authentication)
```bash
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
```

#### Payment Processing (Optional - for premium features)
```bash
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
PREMIUM_PLAN_PRICE_ID=price_your_premium_plan_id
```

#### Security & Performance (Optional but recommended)
```bash
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000
```

#### Analytics & Monitoring (Optional)
```bash
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_here
```

### 2. Database Setup Options

#### Option A: Vercel Postgres (Recommended)
1. Go to Vercel Dashboard → Storage → Create Database
2. Select "Postgres" 
3. Copy the connection string to `DATABASE_URL`

#### Option B: PlanetScale (MySQL-compatible)
1. Create account at planetscale.com
2. Create database and get connection string
3. Set as `DATABASE_URL`

#### Option C: Supabase (PostgreSQL)
1. Create account at supabase.com
2. Create project and get connection string
3. Set as `DATABASE_URL`

### 3. External API Setup

#### GPTZero API (Required for AI detection)
1. Sign up at gptzero.me
2. Get API key from dashboard
3. Set as `GPTZERO_API_KEY`

#### OpenAI API (Recommended for paraphrasing)
1. Sign up at platform.openai.com
2. Create API key
3. Set as `OPENAI_API_KEY`

#### Groq API (Ultra-fast inference)
1. Sign up at console.groq.com
2. Create API key
3. Set as `GROQ_API_KEY`

## 🚀 Deployment Steps

### Step 1: Prepare Your Repository
```bash
# Ensure your code is committed and pushed to GitHub
git add .
git commit -m "Prepare for Vercel deployment"
git push origin main
```

### Step 2: Deploy to Vercel
1. Go to [vercel.com](https://vercel.com) and sign up/login
2. Click "New Project"
3. Import your GitHub repository
4. Configure project:
   - **Project Name**: `ghostlayer`
   - **Framework Preset**: Next.js (auto-detected)
   - **Build Command**: `npm run vercel-build` (already configured)
   - **Output Directory**: `.next` (auto-detected)

### Step 3: Configure Environment Variables
1. In Vercel dashboard, go to Project Settings → Environment Variables
2. Add all required environment variables from the checklist above
3. Set environment for: Production, Preview, and Development

### Step 4: Configure Custom Domain (Optional)
1. Go to Project Settings → Domains
2. Add your custom domain
3. Follow DNS configuration instructions
4. Update `NEXTAUTH_URL` and `NEXT_PUBLIC_APP_URL` to use your domain

### Step 5: Deploy
1. Vercel will automatically deploy on every push to main branch
2. Monitor deployment in Vercel dashboard
3. Check deployment logs for any issues

## 🔧 Post-Deployment Configuration

### Database Migration
After first deployment, run database migrations:
1. Go to Vercel dashboard → Functions
2. Create a one-time function or use Vercel CLI:
```bash
npx vercel env pull .env.local
npm run db:migrate:deploy
```

### Test Your Deployment
1. Visit your deployed URL
2. Test user registration/login
3. Test text processing functionality
4. Test AI detection feature
5. Verify all API endpoints work

## 📊 Monitoring & Analytics

### Built-in Vercel Analytics
- Automatically enabled for all deployments
- View in Vercel dashboard → Analytics

### Performance Monitoring
- Monitor function execution times
- Check bandwidth usage
- Review error logs

### Custom Analytics (Optional)
- Google Analytics: Set `NEXT_PUBLIC_GA_MEASUREMENT_ID`
- Sentry Error Tracking: Set `NEXT_PUBLIC_SENTRY_DSN`

## 🔄 Continuous Deployment

Vercel automatically deploys when you:
1. Push to main branch (production)
2. Create pull requests (preview deployments)
3. Push to other branches (branch deployments)

## 🚨 Troubleshooting

### Common Issues:
1. **Build Failures**: Check environment variables are set
2. **Database Connection**: Verify `DATABASE_URL` is correct
3. **API Errors**: Ensure external API keys are valid
4. **Function Timeouts**: Check function duration limits

### Getting Help:
- Vercel Documentation: vercel.com/docs
- Vercel Community: github.com/vercel/vercel/discussions
- Project Issues: Check deployment logs in Vercel dashboard

## 💰 Cost Estimation

### Vercel Free Tier Limits:
- **Bandwidth**: 100GB/month (300K+ users)
- **Function Executions**: 100/day
- **Build Minutes**: 6,000/month
- **Serverless Functions**: Unlimited

### When to Upgrade:
- Upgrade to Pro ($20/month) when you exceed free tier limits
- Pro tier includes: 1TB bandwidth, unlimited functions, advanced analytics

## 🎯 Performance Optimization

Your deployment is already optimized with:
- ✅ Global CDN distribution
- ✅ Automatic image optimization
- ✅ Static asset caching
- ✅ Serverless function optimization
- ✅ Bundle size optimization
- ✅ Security headers configured

## 🔐 Security Features

Configured security features:
- ✅ HTTPS/SSL certificates
- ✅ Security headers (HSTS, CSP, etc.)
- ✅ CORS configuration
- ✅ Rate limiting ready
- ✅ Environment variable encryption
