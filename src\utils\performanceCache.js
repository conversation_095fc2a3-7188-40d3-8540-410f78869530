/**
 * Performance Cache and Monitoring System
 * Implements caching, rate limiting, and performance monitoring for the humanization system
 */

import crypto from 'crypto';

/**
 * In-memory cache for humanization results
 */
class HumanizationCache {
    constructor(maxSize = 1000, ttl = 3600000) { // 1 hour TTL
        this.cache = new Map();
        this.maxSize = maxSize;
        this.ttl = ttl;
        this.stats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            totalRequests: 0
        };
    }

    /**
     * Generate cache key from text and options
     */
    generateKey(text, options = {}) {
        const normalizedOptions = {
            aggressiveness: options.aggressiveness || 0.7,
            maintainTone: options.maintainTone !== false,
            targetDetection: options.targetDetection || 10,
            method: options.method || 'auto'
        };
        
        const keyData = JSON.stringify({ text, options: normalizedOptions });
        return crypto.createHash('sha256').update(keyData).digest('hex').substring(0, 16);
    }

    /**
     * Get cached result
     */
    get(text, options = {}) {
        this.stats.totalRequests++;
        
        const key = this.generateKey(text, options);
        const cached = this.cache.get(key);
        
        if (!cached) {
            this.stats.misses++;
            return null;
        }
        
        // Check TTL
        if (Date.now() - cached.timestamp > this.ttl) {
            this.cache.delete(key);
            this.stats.misses++;
            return null;
        }
        
        this.stats.hits++;
        
        // Move to end (LRU)
        this.cache.delete(key);
        this.cache.set(key, cached);
        
        return {
            ...cached.result,
            fromCache: true,
            cacheKey: key
        };
    }

    /**
     * Set cache entry
     */
    set(text, options, result) {
        const key = this.generateKey(text, options);
        
        // Evict oldest entries if at max size
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
            this.stats.evictions++;
        }
        
        this.cache.set(key, {
            result: { ...result, fromCache: false },
            timestamp: Date.now()
        });
    }

    /**
     * Clear cache
     */
    clear() {
        this.cache.clear();
        this.stats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            totalRequests: 0
        };
    }

    /**
     * Get cache statistics
     */
    getStats() {
        const hitRate = this.stats.totalRequests > 0 ? 
            (this.stats.hits / this.stats.totalRequests) * 100 : 0;
        
        return {
            ...this.stats,
            hitRate: hitRate.toFixed(2) + '%',
            size: this.cache.size,
            maxSize: this.maxSize,
            memoryUsage: this.estimateMemoryUsage()
        };
    }

    /**
     * Estimate memory usage (rough calculation)
     */
    estimateMemoryUsage() {
        let totalSize = 0;
        for (const [key, value] of this.cache) {
            totalSize += key.length * 2; // UTF-16
            totalSize += JSON.stringify(value).length * 2;
        }
        return Math.round(totalSize / 1024) + ' KB';
    }
}

/**
 * Performance monitoring system
 */
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            requests: {
                total: 0,
                successful: 0,
                failed: 0,
                cached: 0
            },
            timing: {
                totalTime: 0,
                averageTime: 0,
                minTime: Infinity,
                maxTime: 0
            },
            models: {},
            errors: {},
            hourlyStats: new Map()
        };
        
        this.startTime = Date.now();
    }

    /**
     * Record a request
     */
    recordRequest(result) {
        const now = Date.now();
        const hour = new Date(now).getHours();
        
        // Update request counts
        this.metrics.requests.total++;
        
        if (result.success) {
            this.metrics.requests.successful++;
        } else {
            this.metrics.requests.failed++;
            
            // Track error types
            const errorType = result.errorType || 'unknown';
            this.metrics.errors[errorType] = (this.metrics.errors[errorType] || 0) + 1;
        }
        
        if (result.fromCache) {
            this.metrics.requests.cached++;
        }
        
        // Update timing metrics
        if (result.processingTime || result.totalProcessingTime) {
            const time = result.processingTime || result.totalProcessingTime;
            this.metrics.timing.totalTime += time;
            this.metrics.timing.averageTime = this.metrics.timing.totalTime / this.metrics.requests.total;
            this.metrics.timing.minTime = Math.min(this.metrics.timing.minTime, time);
            this.metrics.timing.maxTime = Math.max(this.metrics.timing.maxTime, time);
        }
        
        // Track model usage
        if (result.model) {
            if (!this.metrics.models[result.model]) {
                this.metrics.models[result.model] = {
                    requests: 0,
                    successful: 0,
                    failed: 0,
                    totalTime: 0,
                    averageTime: 0
                };
            }
            
            const modelStats = this.metrics.models[result.model];
            modelStats.requests++;
            
            if (result.success) {
                modelStats.successful++;
            } else {
                modelStats.failed++;
            }
            
            if (result.processingTime) {
                modelStats.totalTime += result.processingTime;
                modelStats.averageTime = modelStats.totalTime / modelStats.requests;
            }
        }
        
        // Update hourly stats
        if (!this.metrics.hourlyStats.has(hour)) {
            this.metrics.hourlyStats.set(hour, {
                requests: 0,
                successful: 0,
                failed: 0,
                cached: 0
            });
        }
        
        const hourlyStats = this.metrics.hourlyStats.get(hour);
        hourlyStats.requests++;
        
        if (result.success) {
            hourlyStats.successful++;
        } else {
            hourlyStats.failed++;
        }
        
        if (result.fromCache) {
            hourlyStats.cached++;
        }
    }

    /**
     * Get performance metrics
     */
    getMetrics() {
        const uptime = Date.now() - this.startTime;
        const requestsPerMinute = this.metrics.requests.total > 0 ? 
            (this.metrics.requests.total / (uptime / 60000)).toFixed(2) : 0;
        
        return {
            ...this.metrics,
            uptime: {
                milliseconds: uptime,
                seconds: Math.round(uptime / 1000),
                minutes: Math.round(uptime / 60000),
                hours: Math.round(uptime / 3600000)
            },
            rates: {
                requestsPerMinute: parseFloat(requestsPerMinute),
                successRate: this.metrics.requests.total > 0 ? 
                    ((this.metrics.requests.successful / this.metrics.requests.total) * 100).toFixed(2) + '%' : '0%',
                cacheHitRate: this.metrics.requests.total > 0 ? 
                    ((this.metrics.requests.cached / this.metrics.requests.total) * 100).toFixed(2) + '%' : '0%'
            }
        };
    }

    /**
     * Reset metrics
     */
    reset() {
        this.metrics = {
            requests: {
                total: 0,
                successful: 0,
                failed: 0,
                cached: 0
            },
            timing: {
                totalTime: 0,
                averageTime: 0,
                minTime: Infinity,
                maxTime: 0
            },
            models: {},
            errors: {},
            hourlyStats: new Map()
        };
        
        this.startTime = Date.now();
    }
}

/**
 * Rate limiter
 */
class RateLimiter {
    constructor(maxRequests = 60, windowMs = 60000) { // 60 requests per minute
        this.maxRequests = maxRequests;
        this.windowMs = windowMs;
        this.requests = new Map();
    }

    /**
     * Check if request is allowed
     */
    isAllowed(identifier = 'default') {
        const now = Date.now();
        const windowStart = now - this.windowMs;
        
        if (!this.requests.has(identifier)) {
            this.requests.set(identifier, []);
        }
        
        const userRequests = this.requests.get(identifier);
        
        // Remove old requests
        while (userRequests.length > 0 && userRequests[0] < windowStart) {
            userRequests.shift();
        }
        
        // Check if under limit
        if (userRequests.length >= this.maxRequests) {
            return {
                allowed: false,
                resetTime: userRequests[0] + this.windowMs,
                remaining: 0
            };
        }
        
        // Add current request
        userRequests.push(now);
        
        return {
            allowed: true,
            resetTime: now + this.windowMs,
            remaining: this.maxRequests - userRequests.length
        };
    }

    /**
     * Get rate limit status
     */
    getStatus(identifier = 'default') {
        const now = Date.now();
        const windowStart = now - this.windowMs;
        
        if (!this.requests.has(identifier)) {
            return {
                requests: 0,
                remaining: this.maxRequests,
                resetTime: now + this.windowMs
            };
        }
        
        const userRequests = this.requests.get(identifier);
        const recentRequests = userRequests.filter(time => time > windowStart);
        
        return {
            requests: recentRequests.length,
            remaining: Math.max(0, this.maxRequests - recentRequests.length),
            resetTime: recentRequests.length > 0 ? recentRequests[0] + this.windowMs : now + this.windowMs
        };
    }
}

// Global instances
export const humanizationCache = new HumanizationCache();
export const performanceMonitor = new PerformanceMonitor();
export const rateLimiter = new RateLimiter();

/**
 * Middleware function to add caching and monitoring
 */
export function withCacheAndMonitoring(humanizationFunction) {
    return async function(text, options = {}) {
        const startTime = Date.now();
        
        // Check rate limit
        const rateLimitStatus = rateLimiter.isAllowed();
        if (!rateLimitStatus.allowed) {
            const result = {
                success: false,
                error: 'Rate limit exceeded',
                errorType: 'rate_limit',
                resetTime: rateLimitStatus.resetTime,
                remaining: rateLimitStatus.remaining,
                processingTime: Date.now() - startTime
            };
            
            performanceMonitor.recordRequest(result);
            return result;
        }
        
        // Check cache first
        const cached = humanizationCache.get(text, options);
        if (cached) {
            cached.totalProcessingTime = Date.now() - startTime;
            performanceMonitor.recordRequest(cached);
            return cached;
        }
        
        // Call the actual humanization function
        try {
            const result = await humanizationFunction(text, options);
            result.totalProcessingTime = Date.now() - startTime;
            
            // Cache successful results
            if (result.success) {
                humanizationCache.set(text, options, result);
            }
            
            performanceMonitor.recordRequest(result);
            return result;
            
        } catch (error) {
            const result = {
                success: false,
                error: error.message,
                errorType: 'exception',
                processingTime: Date.now() - startTime,
                totalProcessingTime: Date.now() - startTime
            };
            
            performanceMonitor.recordRequest(result);
            throw error;
        }
    };
}

/**
 * Get system performance report
 */
export function getPerformanceReport() {
    return {
        cache: humanizationCache.getStats(),
        performance: performanceMonitor.getMetrics(),
        rateLimit: rateLimiter.getStatus(),
        timestamp: new Date().toISOString()
    };
}
