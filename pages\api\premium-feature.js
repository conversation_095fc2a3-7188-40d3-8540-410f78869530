// src/pages/api/premium-feature.js
import { getServerSession } from "next-auth/next";
import { authOptions } from "./auth/[...nextauth]";
// import prisma from '../../lib/prisma'; // Only needed if re-fetching user for utmost freshness

export default async function handler(req, res) {
    if (req.method !== 'GET') { // Assuming a GET request for this example
        res.setHeader('Allow', ['GET']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }

    const session = await getServerSession(req, res, authOptions);

    if (!session || !session.user) {
        return res.status(401).json({
            error: "Unauthorized",
            message: "You must be signed in to access this resource."
        });
    }

    // Check subscription tier from the session object.
    // This relies on the `subscriptionTier` being correctly populated in the
    // session callback within `[...nextauth].js`.
    const userSubscriptionTier = session.user.subscriptionTier;

    // For critical operations, or if there's a concern that the session data might be stale
    // (e.g., user upgraded/downgraded very recently and session hasn't been refreshed yet),
    // you might re-fetch the user directly from the database:
    // try {
    //     const currentUser = await prisma.user.findUnique({ where: { id: session.user.id } });
    //     if (!currentUser) {
    //         return res.status(404).json({ error: "User not found in database." });
    //     }
    //     userSubscriptionTier = currentUser.subscriptionTier;
    // } catch (dbError) {
    //     console.error("Database error fetching user for tier check:", dbError);
    //     return res.status(500).json({ error: "Internal server error while verifying tier." });
    // }

    if (userSubscriptionTier !== 'premium') {
        // Consider also checking for other valid premium tiers, e.g., "premium_yearly", "premium_monthly"
        // if ( !['premium', 'premium_monthly', 'premium_yearly'].includes(userSubscriptionTier) ) {
        return res.status(403).json({
            error: "Forbidden",
            message: "This feature is available for premium users only. Your current tier: " + userSubscriptionTier
        });
    }

    // --- Premium Feature Logic ---
    // This is where you would implement the actual premium functionality.
    // For example, access a more powerful AI model, provide data with higher limits, etc.
    res.status(200).json({
        message: "Successfully accessed premium feature!",
        data: "Here is your exclusive premium data. Thanks for being a premium member!",
        user: { // Send back some user info from session for confirmation
            id: session.user.id,
            email: session.user.email,
            currentTier: userSubscriptionTier
        }
    });
}
