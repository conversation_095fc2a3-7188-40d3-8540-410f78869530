# Project Structure: AI Text Modifier App

This document outlines the project structure for the AI text modification web app. The primary focus is on a Next.js monorepo structure, which can contain both the frontend and backend (via API routes).

## Primary Structure: Next.js Monorepo

```
ai-text-modifier-app/
├── .git/                      # Git version control
├── .gitignore                 # Specifies intentionally untracked files that G<PERSON> should ignore
├── .eslintrc.json             # ESLint configuration for code linting
├── .prettierrc.json           # Prettier configuration for code formatting
├── node_modules/              # Project dependencies
├── package.json               # Lists project dependencies and scripts
├── next.config.js             # Next.js configuration file
├── jsconfig.json              # (or tsconfig.json for TypeScript) JS/TS compiler options
├── README.md                  # Project overview, setup, and usage instructions
├── public/                    # Static assets (images, fonts, robots.txt, etc.)
│   ├── images/
│   └── favicon.ico
├── src/                       # Source code (alternative to root-level pages, components, etc.)
│   ├── pages/                 # Next.js pages (routes)
│   │   ├── index.js           # Homepage (text input/output)
│   │   └── api/               # API routes (backend logic if using Next.js backend)
│   │       └── process.js     # API endpoint for text processing
│   │       └── test-detection.js # API endpoint for AI detection test
│   ├── components/            # Reusable React components
│   │   ├── layout/            # Layout components (e.g., Navbar, Footer)
│   │   │   └── Layout.js
│   │   ├── common/            # Common UI elements (e.g., Button, TextArea)
│   │   │   ├── Button.js
│   │   │   └── TextArea.js
│   │   └── features/          # Components specific to features
│   │       ├── TextEditor.js      # Main text input and output area
│   │       ├── ControlPanel.js    # Buttons for processing, options
│   │       └── ResultsDisplay.js  # To show modification suggestions & detection scores
│   ├── contexts/              # React Context API for global state (if needed)
│   │   └── AppContext.js
│   ├── hooks/                 # Custom React hooks
│   │   └── useTextProcessor.js
│   ├── services/              # For external API calls (non-Next.js API routes)
│   │   └── gptzeroClient.js   # Example: client for GPTZero API
│   ├── styles/                # Global styles and CSS modules
│   │   ├── globals.css
│   │   └── Home.module.css
│   ├── utils/                 # Utility functions
│   │   ├── textModifiers.js   # Core logic for text manipulation ("own algorithms")
│   │   ├── apiHelpers.js      # Helper functions for API route handlers
│   │   └── constants.js       # Application-wide constants
│   └── lib/                   # General-purpose libraries or modules (less common in Next.js)
├── docs/                      # Project documentation
│   ├── system_architecture.md # (Generated in previous step)
│   └── project_structure.md   # This document
│   └── workflow.md            # For the Mermaid diagram (to be created)
```

### Notes on Structure:

*   **`src/` directory:** Using a `src` directory is a common convention to separate your source code from configuration files at the root. Next.js supports this.
*   **`pages/api/`:** If using Next.js API routes, this is where your backend serverless functions will reside.
    *   `process.js`: Would handle the main text modification requests.
    *   `test-detection.js`: Could be a separate endpoint specifically for running detection tests.
*   **`components/`:**
    *   **`layout/`:** For overall page structure.
    *   **`common/`:** For generic, reusable UI elements.
    *   **`features/`:** For more complex components tied to specific functionalities of your app.
*   **`utils/textModifiers.js`:** This is a crucial file for the MVP, as it will house the "own algorithms" for text manipulation (paraphrasing, error injection, style changes).
*   **`services/`:** If you need to call external APIs from the client-side (not recommended for APIs requiring secret keys) or from a separate backend service (if not using Next.js API routes), you might place client SDKs or wrappers here. For secure API calls (like to OpenAI or paid detection services), these should be done from your backend (`pages/api/*` or a dedicated backend server).
*   **`docs/`:** To keep all documentation files organized.

## Alternative Backend Structure

If a separate backend (e.g., Python with FastAPI) is preferred, it would live in a parallel directory at the root or as a separate repository:

```
ai-text-modifier-app-frontend/  # (The Next.js app as described above)
ai-text-modifier-app-backend/ # (Example: Python FastAPI backend)
├── app/
│   ├── __init__.py
│   ├── main.py               # FastAPI app definition
│   ├── routers/
│   │   └── process.py        # Processing endpoints
│   ├── core/
│   │   └── config.py         # Configuration
│   └── services/
│       └── text_modifier_service.py
│       └── ai_detection_service.py
├── requirements.txt
├── Dockerfile
└── README.md
```
For this plan, we will assume the integrated Next.js API routes approach for simplicity in the MVP, so the first structure is primary.
