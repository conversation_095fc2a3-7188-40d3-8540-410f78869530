from flask import Flask, request, jsonify
from transformers import PegasusForConditionalGeneration, PegasusTokenizer
import torch
import os

# Initialize Flask App
app = Flask(__name__)

# --- Model Loading ---
MODEL_NAME = 'tuner007/pegasus_paraphrase'
tokenizer = None
model = None
device = None  # Will be set to 'cuda' or 'cpu'

def load_model():
    global tokenizer, model, device
    try:
        print(f"Loading tokenizer for {MODEL_NAME}...")
        # Ensure sentencepiece is installed if not already: pip install sentencepiece
        tokenizer = PegasusTokenizer.from_pretrained(MODEL_NAME)
        print(f"Loading model {MODEL_NAME}...")
        model = PegasusForConditionalGeneration.from_pretrained(MODEL_NAME)

        if torch.cuda.is_available():
            device = torch.device("cuda")
            model.to(device)
            print("Model moved to GPU.")
        else:
            device = torch.device("cpu")
            model.to(device)  # Explicitly move to CPU if not already
            print("Model using CPU.")

        model.eval()  # Set to evaluation mode
        print("Model and tokenizer loaded successfully.")
    except Exception as e:
        print(f"Error loading model or tokenizer: {e}")
        # Allow app to start, but endpoint will fail.
        # For a critical model, you might want to raise an error to prevent app start:
        # raise RuntimeError(f"Failed to load model: {e}") from e

# Call load_model() when the application starts.
# This ensures the model is loaded once, not on every request.
load_model()

# --- API Endpoint ---
@app.route('/paraphrase', methods=['POST'])
def paraphrase_text_endpoint(): # Renamed to avoid conflict with any potential 'paraphrase_text' utility function
    if not model or not tokenizer:  # Check if model loading failed
        return jsonify({"error": "Model not loaded. Check server logs."}), 500

    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400

        input_text = data.get('text')

        if not input_text:
            return jsonify({"error": "No text provided in JSON payload (expected key 'text')"}), 400

        if not isinstance(input_text, str):
            return jsonify({"error": "Field 'text' must be a string"}), 400

        # Prepare the text for the model
        # Pegasus expects a list of strings for batching, even if it's just one.
        batch = tokenizer(
            [input_text],
            truncation=True,      # Truncate to max model length if input is too long
            padding='longest',    # Pad to the longest sequence in the batch (or max_length)
            max_length=128,       # Max input length for Pegasus for this task (can be tuned)
            return_tensors="pt"   # Return PyTorch tensors
        ).to(device)              # Move batch to the same device as the model

        # Generate paraphrased text
        # Adjust generation parameters as needed for desired output
        generated_ids = model.generate(
            **batch,
            max_length=128,          # Max length of the generated paraphrase
            num_beams=5,             # Number of beams for beam search. Higher can improve quality but is slower.
            num_return_sequences=1,  # We only want one best paraphrase here
            temperature=1.5,         # Controls randomness. Higher values (e.g., >1) make output more random.
                                     # Lower values (e.g., <1) make it more deterministic.
            # early_stopping=True    # Can be useful to stop generation when end-of-sentence token is produced
        )

        # Decode the generated IDs to text
        paraphrased_texts = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)

        # Since num_return_sequences=1, we expect one result in the list
        result_text = paraphrased_texts[0] if paraphrased_texts else "No paraphrase generated."

        return jsonify({"paraphrased_text": result_text})

    except Exception as e:
        app.logger.error(f"Error during paraphrasing: {e}", exc_info=True) # Log the full exception info
        return jsonify({"error": "An unexpected error occurred during paraphrasing."}), 500

# --- Run Flask App ---
if __name__ == '__main__':
    # Port can be configured via an environment variable or default to 5001
    port = int(os.environ.get('FLASK_PORT', 5001))
    # Note: debug=True is for development. Set to False in production.
    # Use a production-ready WSGI server like Gunicorn or uWSGI in production.
    app.run(host='0.0.0.0', port=port, debug=True)
