import React from 'react';
import Navbar from './Navbar';
import Footer from './Footer';
// import styles from './Layout.module.css'; // Optional: if Layout itself needs styles

const Layout = ({ children }) => {
    return (
        <>
            <Navbar />
            <main style={{ padding: '0 20px', minHeight: '70vh' /* Ensure footer is pushed down a bit */ }}>
                {/* The main container styling from Home.module.css will apply to children if HomePage is the child */}
                {children}
            </main>
            <Footer />
        </>
    );
};

export default Layout;
