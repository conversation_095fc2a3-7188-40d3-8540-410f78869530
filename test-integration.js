/**
 * Integration Testing for Enhanced Humanization
 * Tests API endpoints, compatibility with existing architecture, edge cases, and error handling
 */

import { balancedHumanization } from './src/utils/balancedHumanizer.js';
import { multiPassHumanization } from './src/utils/multiPassHumanizer.js';
import { advancedHumanization } from './src/utils/advancedHumanizer.js';

// Test cases for integration testing
const integrationTests = [
    {
        name: "Balanced Humanizer Integration",
        test: async () => {
            const text = "This is a very important document that shows significant benefits.";
            const result = balancedHumanization(text, null, 0, { useAdvanced: true });
            return {
                success: typeof result === 'string' && result.length > 0,
                result: result,
                message: "Balanced humanizer should use advanced engine"
            };
        }
    },
    {
        name: "Multi-Pass Humanizer Integration", 
        test: async () => {
            const text = "The implementation requires careful planning and provides good results.";
            const result = multiPassHumanization(text, { useAdvancedEngine: true });
            return {
                success: typeof result === 'string' && result.length > 0,
                result: result,
                message: "Multi-pass humanizer should integrate with advanced engine"
            };
        }
    },
    {
        name: "Direct Advanced Humanization",
        test: async () => {
            const text = "Artificial intelligence technology demonstrates excellent performance.";
            const result = advancedHumanization(text, { aggressiveness: 0.5 });
            return {
                success: typeof result === 'string' && result.length > 0,
                result: result,
                message: "Direct advanced humanization should work correctly"
            };
        }
    },
    {
        name: "Empty Text Handling",
        test: async () => {
            try {
                const result = advancedHumanization("", { aggressiveness: 0.7 });
                return {
                    success: result === "",
                    result: result,
                    message: "Should handle empty text gracefully"
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    message: "Should not throw error on empty text"
                };
            }
        }
    },
    {
        name: "Very Short Text Handling",
        test: async () => {
            const text = "Hi.";
            const result = advancedHumanization(text, { aggressiveness: 0.7 });
            return {
                success: typeof result === 'string' && result.length >= text.length,
                result: result,
                message: "Should handle very short text without errors"
            };
        }
    },
    {
        name: "Very Long Text Handling",
        test: async () => {
            const longText = "This is a test sentence. ".repeat(100); // 2500+ characters
            const startTime = Date.now();
            const result = advancedHumanization(longText, { aggressiveness: 0.7 });
            const processingTime = Date.now() - startTime;
            
            return {
                success: typeof result === 'string' && result.length > 0 && processingTime < 5000,
                result: `${result.substring(0, 100)}... (${result.length} chars, ${processingTime}ms)`,
                message: "Should handle long text efficiently (< 5 seconds)"
            };
        }
    },
    {
        name: "Special Characters Handling",
        test: async () => {
            const text = "This text contains special characters: @#$%^&*()[]{}|\\:;\"'<>,.?/~`";
            const result = advancedHumanization(text, { aggressiveness: 0.7 });
            return {
                success: typeof result === 'string' && result.includes("@#$%"),
                result: result,
                message: "Should preserve special characters"
            };
        }
    },
    {
        name: "Unicode and Emoji Handling",
        test: async () => {
            const text = "This text has unicode: café, naïve, résumé and emojis: 😀 🚀 ✅";
            const result = advancedHumanization(text, { aggressiveness: 0.7 });
            return {
                success: typeof result === 'string' && result.includes("café") && result.includes("😀"),
                result: result,
                message: "Should preserve unicode characters and emojis"
            };
        }
    },
    {
        name: "Extreme Aggressiveness Values",
        test: async () => {
            const text = "This is a test of extreme aggressiveness values.";
            
            // Test with 0 aggressiveness
            const result1 = advancedHumanization(text, { aggressiveness: 0 });
            
            // Test with maximum aggressiveness
            const result2 = advancedHumanization(text, { aggressiveness: 1 });
            
            // Test with invalid aggressiveness
            const result3 = advancedHumanization(text, { aggressiveness: -1 });
            const result4 = advancedHumanization(text, { aggressiveness: 2 });
            
            return {
                success: typeof result1 === 'string' && typeof result2 === 'string' && 
                        typeof result3 === 'string' && typeof result4 === 'string',
                result: `0: ${result1.substring(0, 30)}..., 1: ${result2.substring(0, 30)}...`,
                message: "Should handle extreme and invalid aggressiveness values"
            };
        }
    },
    {
        name: "Concurrent Processing",
        test: async () => {
            const text = "This is a test of concurrent processing capabilities.";
            const promises = [];
            
            // Create 10 concurrent humanization requests
            for (let i = 0; i < 10; i++) {
                promises.push(
                    new Promise(resolve => {
                        const result = advancedHumanization(text, { aggressiveness: 0.5 });
                        resolve(result);
                    })
                );
            }
            
            const results = await Promise.all(promises);
            const allSuccessful = results.every(r => typeof r === 'string' && r.length > 0);
            
            return {
                success: allSuccessful,
                result: `${results.length} concurrent requests completed`,
                message: "Should handle concurrent processing without issues"
            };
        }
    }
];

/**
 * Run integration tests
 */
async function runIntegrationTests() {
    console.log('🔗 INTEGRATION TESTING SUITE');
    console.log('═'.repeat(60));
    
    const results = {
        passed: 0,
        failed: 0,
        details: []
    };
    
    for (const test of integrationTests) {
        console.log(`\n🧪 ${test.name}`);
        console.log('─'.repeat(40));
        
        try {
            const result = await test.test();
            results.details.push({
                name: test.name,
                ...result
            });
            
            if (result.success) {
                results.passed++;
                console.log(`✅ PASSED: ${result.message}`);
                if (result.result) {
                    console.log(`   Result: ${result.result.substring(0, 100)}${result.result.length > 100 ? '...' : ''}`);
                }
            } else {
                results.failed++;
                console.log(`❌ FAILED: ${result.message}`);
                if (result.error) {
                    console.log(`   Error: ${result.error}`);
                }
                if (result.result) {
                    console.log(`   Result: ${result.result}`);
                }
            }
            
        } catch (error) {
            results.failed++;
            console.log(`❌ ERROR: ${error.message}`);
            results.details.push({
                name: test.name,
                success: false,
                error: error.message,
                message: "Unexpected error during test execution"
            });
        }
    }
    
    generateIntegrationReport(results);
}

/**
 * Test API endpoint integration (if server is running)
 */
async function testAPIIntegration() {
    console.log('\n🌐 API ENDPOINT TESTING');
    console.log('─'.repeat(40));
    
    const testData = {
        text: "This is a test of the API integration with enhanced humanization."
    };
    
    try {
        const response = await fetch('http://localhost:3000/api/process', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(testData)
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ API Integration: SUCCESS');
            console.log(`   Original: ${testData.text}`);
            console.log(`   Processed: ${data.modifiedText || data.text}`);
            return true;
        } else {
            console.log(`❌ API Integration: HTTP ${response.status}`);
            return false;
        }
        
    } catch (error) {
        console.log('⚠️  API Integration: Server not running or not accessible');
        console.log('   This is expected if the development server is not started');
        return null; // Not a failure, just not testable
    }
}

/**
 * Generate integration test report
 */
function generateIntegrationReport(results) {
    console.log('\n' + '═'.repeat(60));
    console.log('📊 INTEGRATION TEST SUMMARY');
    console.log('═'.repeat(60));
    
    console.log(`\n🎯 Test Results:`);
    console.log(`   ✅ Passed: ${results.passed}`);
    console.log(`   ❌ Failed: ${results.failed}`);
    console.log(`   📈 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
    
    console.log(`\n📋 Detailed Results:`);
    results.details.forEach(result => {
        const status = result.success ? '✅' : '❌';
        console.log(`   ${status} ${result.name}: ${result.message}`);
        if (result.error) {
            console.log(`      Error: ${result.error}`);
        }
    });
    
    const overallSuccess = results.passed >= Math.ceil((results.passed + results.failed) * 0.8);
    console.log(`\n🏆 Overall Assessment: ${overallSuccess ? '✅ INTEGRATION SUCCESSFUL' : '❌ INTEGRATION ISSUES DETECTED'}`);
    
    if (!overallSuccess) {
        console.log('\n⚠️  Recommendations:');
        console.log('   - Review failed test cases and fix integration issues');
        console.log('   - Ensure error handling is robust');
        console.log('   - Verify compatibility with existing architecture');
    } else {
        console.log('\n🎉 All integration tests passed! Enhanced humanization is properly integrated.');
    }
}

/**
 * Run comprehensive integration testing
 */
async function runComprehensiveIntegrationTests() {
    await runIntegrationTests();
    await testAPIIntegration();
}

// Run integration tests
if (import.meta.url === `file://${process.argv[1]}`) {
    runComprehensiveIntegrationTests().catch(console.error);
}

export { runIntegrationTests, testAPIIntegration, runComprehensiveIntegrationTests };
