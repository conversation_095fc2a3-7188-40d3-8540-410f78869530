import { useSession, signIn, signOut } from 'next-auth/react';
import React from 'react';
import styles from './AuthButtons.module.css';

export default function AuthButtons() {
    const { data: session, status } = useSession();
    const loading = status === 'loading';

    if (loading) {
        return <div className={styles.loading}>Loading...</div>;
    }

    if (session) {
        const userDisplayName = session.user.name ? session.user.name.split(' ')[0] : session.user.email;
        const isPremium = session.user.subscriptionTier === 'premium';

        return (
            <div className={styles.container}>
                <span className={styles.userInfo} title={session.user.email}>
                    Hi, {userDisplayName}
                    {isPremium && <span className={styles.premiumBadge}>Premium</span>}
                </span>
                <button onClick={() => signOut()} className={styles.button}>
                    Sign out
                </button>
            </div>
        );
    }

    return (
        <div className={styles.container}>
            <button
                onClick={() => signIn('google', { callbackUrl: '/' })} // Redirect to home after sign-in
                className={`${styles.button} ${styles.signInButton}`}
            >
                Sign in with Google
            </button>
        </div>
    );
}
