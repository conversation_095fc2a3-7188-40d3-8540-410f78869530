.editorContainer {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: var(--space-6);
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-6);
}

.editorSection {
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.editorSection:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

.editorHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) var(--space-5);
    background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
    border-bottom: 1px solid var(--secondary-200);
}

.headerLeft {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.editorTitle {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--secondary-900);
    margin: 0;
}

.titleIcon {
    color: var(--primary-600);
}

.stats {
    display: flex;
    gap: var(--space-3);
}

.stat {
    font-size: 0.875rem;
    color: var(--secondary-600);
    background: white;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.headerActions {
    display: flex;
    gap: var(--space-2);
}

.actionButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: white;
    border: 1px solid var(--secondary-300);
    border-radius: var(--radius-md);
    color: var(--secondary-600);
    cursor: pointer;
    transition: all var(--transition-fast);
    padding: 0;
}

.actionButton:hover {
    background: var(--primary-50);
    border-color: var(--primary-300);
    color: var(--primary-600);
    transform: translateY(-1px);
}

.editorWrapper {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 500px;
    transition: all var(--transition-normal);
}

.editorWrapper.focused {
    box-shadow: inset 0 0 0 2px var(--primary-200);
}

.textarea {
    width: 100%;
    height: 100%;
    min-height: 500px;
    padding: var(--space-5);
    border: none;
    font-family: var(--font-sans);
    font-size: 1rem;
    line-height: 1.7;
    resize: none;
    background: transparent;
    color: var(--secondary-800);
    transition: all var(--transition-fast);
}

.textarea:focus {
    outline: none;
}

.textarea::placeholder {
    color: var(--secondary-400);
    font-style: italic;
}

.textarea:disabled {
    background: var(--secondary-50);
    color: var(--secondary-500);
    cursor: not-allowed;
}

.textareaOutput {
    background: linear-gradient(135deg, var(--success-50), var(--primary-50));
    cursor: default;
}

.loadingOverlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
}

.loadingSpinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--secondary-200);
    border-top: 3px solid var(--primary-600);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.separator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4) 0;
}

.arrowContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
    border-radius: 50%;
    box-shadow: var(--shadow-lg);
    animation: pulse 2s infinite;
}

.arrow {
    color: white;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: var(--shadow-lg);
    }
    50% {
        transform: scale(1.05);
        box-shadow: var(--shadow-xl);
    }
}

.emptyState {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--secondary-400);
    pointer-events: none;
}

.emptyIcon {
    margin-bottom: var(--space-3);
    opacity: 0.5;
}

.emptyState p {
    font-style: italic;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .editorContainer {
        grid-template-columns: 1fr;
        gap: var(--space-4);
        padding: var(--space-4);
    }

    .separator {
        transform: rotate(90deg);
        padding: var(--space-2) 0;
    }

    .editorWrapper {
        min-height: 400px;
    }

    .textarea {
        min-height: 400px;
        padding: var(--space-4);
    }
}

@media (max-width: 768px) {
    .editorContainer {
        padding: var(--space-3);
    }

    .editorHeader {
        padding: var(--space-3) var(--space-4);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
    }

    .headerLeft {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
    }

    .stats {
        gap: var(--space-2);
    }

    .stat {
        font-size: 0.75rem;
    }

    .editorWrapper {
        min-height: 300px;
    }

    .textarea {
        min-height: 300px;
        padding: var(--space-3);
        font-size: 0.875rem;
    }
}
