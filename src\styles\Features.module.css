.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    min-height: calc(100vh - 200px);
}

.header {
    text-align: center;
    margin-bottom: 4rem;
}

.title {
    font-size: 3rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #0ea5e9, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.featuresGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.featureCard {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.featureCard:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.featureIcon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.featureTitle {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.featureDescription {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.benefitsList {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefit {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    color: #374151;
}

.checkmark {
    color: #10b981;
    font-weight: bold;
    margin-right: 0.5rem;
}

.ctaSection {
    text-align: center;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border-radius: 1rem;
    padding: 3rem 2rem;
    border: 1px solid #bae6fd;
}

.ctaTitle {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.ctaDescription {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.ctaButtons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.primaryButton {
    background: linear-gradient(135deg, #0ea5e9, #3b82f6);
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.primaryButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.secondaryButton {
    background: white;
    color: #0ea5e9;
    padding: 0.75rem 2rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid #0ea5e9;
}

.secondaryButton:hover {
    background: #0ea5e9;
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .featuresGrid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .featureCard {
        padding: 1.5rem;
    }
    
    .ctaSection {
        padding: 2rem 1rem;
    }
    
    .ctaButtons {
        flex-direction: column;
        align-items: center;
    }
    
    .primaryButton,
    .secondaryButton {
        width: 100%;
        max-width: 300px;
        text-align: center;
    }
}
