/**
 * Style Profile Creator Component
 * Allows users to upload writing samples and create personalized style profiles
 */

import { useState, useCallback } from 'react';
import { analyzeWritingStyle } from '../services/styleAnalysisService';
import styles from './StyleProfileCreator.module.css';

export default function StyleProfileCreator({ onProfileCreated, onClose }) {
    const [step, setStep] = useState(1); // 1: Upload, 2: Analysis, 3: Configuration
    const [writingSamples, setWritingSamples] = useState(['', '', '']);
    const [profileName, setProfileName] = useState('');
    const [profileDescription, setProfileDescription] = useState('');
    const [styleAnalysis, setStyleAnalysis] = useState(null);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [error, setError] = useState(null);
    const [defaultStrength, setDefaultStrength] = useState(50);

    const handleSampleChange = useCallback((index, value) => {
        const newSamples = [...writingSamples];
        newSamples[index] = value;
        setWritingSamples(newSamples);
    }, [writingSamples]);

    const addSample = useCallback(() => {
        if (writingSamples.length < 5) {
            setWritingSamples([...writingSamples, '']);
        }
    }, [writingSamples]);

    const removeSample = useCallback((index) => {
        if (writingSamples.length > 1) {
            const newSamples = writingSamples.filter((_, i) => i !== index);
            setWritingSamples(newSamples);
        }
    }, [writingSamples]);

    const analyzeStyle = useCallback(async () => {
        setIsAnalyzing(true);
        setError(null);

        try {
            // Filter out empty samples
            const validSamples = writingSamples.filter(sample => sample.trim().length > 50);
            
            if (validSamples.length === 0) {
                throw new Error('Please provide at least one writing sample with 50+ characters');
            }

            const analysis = analyzeWritingStyle(validSamples);
            setStyleAnalysis(analysis);
            setStep(2);
        } catch (err) {
            setError(err.message);
        } finally {
            setIsAnalyzing(false);
        }
    }, [writingSamples]);

    const saveProfile = useCallback(async () => {
        if (!profileName.trim()) {
            setError('Please provide a name for your style profile');
            return;
        }

        try {
            const profileData = {
                name: profileName,
                description: profileDescription,
                styleAnalysis,
                defaultStrength,
                sampleCount: writingSamples.filter(s => s.trim().length > 50).length,
                totalSampleWords: writingSamples.join(' ').split(/\s+/).length
            };

            // Call API to save profile
            const response = await fetch('/api/style-profiles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(profileData),
            });

            if (!response.ok) {
                throw new Error('Failed to save style profile');
            }

            const savedProfile = await response.json();
            onProfileCreated(savedProfile);
            onClose();
        } catch (err) {
            setError(err.message);
        }
    }, [profileName, profileDescription, styleAnalysis, defaultStrength, writingSamples, onProfileCreated, onClose]);

    const renderUploadStep = () => (
        <div className={styles.step}>
            <h3>Upload Your Writing Samples</h3>
            <p className={styles.instruction}>
                Provide 3-5 samples of your writing (emails, essays, articles, etc.) to create your personal style profile.
                Each sample should be at least 50 words long.
            </p>

            {writingSamples.map((sample, index) => (
                <div key={index} className={styles.sampleContainer}>
                    <div className={styles.sampleHeader}>
                        <label>Sample {index + 1}</label>
                        {writingSamples.length > 1 && (
                            <button
                                type="button"
                                onClick={() => removeSample(index)}
                                className={styles.removeButton}
                            >
                                Remove
                            </button>
                        )}
                    </div>
                    <textarea
                        value={sample}
                        onChange={(e) => handleSampleChange(index, e.target.value)}
                        placeholder={`Paste your writing sample here... (minimum 50 words)`}
                        className={styles.sampleTextarea}
                        rows={6}
                    />
                    <div className={styles.wordCount}>
                        Words: {sample.trim().split(/\s+/).filter(w => w.length > 0).length}
                    </div>
                </div>
            ))}

            {writingSamples.length < 5 && (
                <button
                    type="button"
                    onClick={addSample}
                    className={styles.addSampleButton}
                >
                    + Add Another Sample
                </button>
            )}

            <div className={styles.stepActions}>
                <button
                    onClick={analyzeStyle}
                    disabled={isAnalyzing || writingSamples.every(s => s.trim().length < 50)}
                    className={styles.primaryButton}
                >
                    {isAnalyzing ? 'Analyzing Style...' : 'Analyze Writing Style'}
                </button>
                <button onClick={onClose} className={styles.secondaryButton}>
                    Cancel
                </button>
            </div>
        </div>
    );

    const renderAnalysisStep = () => (
        <div className={styles.step}>
            <h3>Your Writing Style Analysis</h3>
            
            <div className={styles.analysisGrid}>
                <div className={styles.analysisCard}>
                    <h4>Sentence Patterns</h4>
                    <p>Average Length: {styleAnalysis.sentencePatterns.averageLength} words</p>
                    <p>Complexity: {styleAnalysis.sentencePatterns.complexity}</p>
                    <div className={styles.lengthDistribution}>
                        <div>Short: {Math.round(styleAnalysis.sentencePatterns.lengthDistribution.short * 100)}%</div>
                        <div>Medium: {Math.round(styleAnalysis.sentencePatterns.lengthDistribution.medium * 100)}%</div>
                        <div>Long: {Math.round(styleAnalysis.sentencePatterns.lengthDistribution.long * 100)}%</div>
                    </div>
                </div>

                <div className={styles.analysisCard}>
                    <h4>Vocabulary</h4>
                    <p>Complexity: {styleAnalysis.vocabularyComplexity.complexityLevel}</p>
                    <p>Lexical Diversity: {Math.round(styleAnalysis.vocabularyComplexity.lexicalDiversity * 100)}%</p>
                    <p>Avg Word Length: {styleAnalysis.vocabularyComplexity.averageWordLength}</p>
                </div>

                <div className={styles.analysisCard}>
                    <h4>Transition Phrases</h4>
                    <p>Total Used: {styleAnalysis.transitionPhrases.totalTransitions}</p>
                    <div className={styles.topTransitions}>
                        {styleAnalysis.transitionPhrases.mostUsed.slice(0, 3).map((transition, i) => (
                            <span key={i} className={styles.transitionTag}>
                                {transition.phrase} ({transition.count})
                            </span>
                        ))}
                    </div>
                </div>

                <div className={styles.analysisCard}>
                    <h4>Punctuation Style</h4>
                    <div className={styles.punctuationPrefs}>
                        {styleAnalysis.punctuationStyle.preferences.commaHeavy && <span className={styles.prefTag}>Comma Heavy</span>}
                        {styleAnalysis.punctuationStyle.preferences.dashUser && <span className={styles.prefTag}>Uses Dashes</span>}
                        {styleAnalysis.punctuationStyle.preferences.parentheticalUser && <span className={styles.prefTag}>Parenthetical</span>}
                        {styleAnalysis.punctuationStyle.preferences.exclamatory && <span className={styles.prefTag}>Exclamatory</span>}
                    </div>
                </div>

                <div className={styles.analysisCard}>
                    <h4>Personal Style</h4>
                    <p>Tone: {styleAnalysis.toneAnalysis.dominantTone}</p>
                    <p>Personal Markers: {styleAnalysis.personalExpressions.personalMarkers.length}</p>
                    <p>Writing Quirks: {styleAnalysis.writingQuirks.length}</p>
                </div>
            </div>

            <div className={styles.stepActions}>
                <button onClick={() => setStep(3)} className={styles.primaryButton}>
                    Configure Profile
                </button>
                <button onClick={() => setStep(1)} className={styles.secondaryButton}>
                    Back to Samples
                </button>
            </div>
        </div>
    );

    const renderConfigurationStep = () => (
        <div className={styles.step}>
            <h3>Configure Your Style Profile</h3>

            <div className={styles.configForm}>
                <div className={styles.formGroup}>
                    <label htmlFor="profileName">Profile Name *</label>
                    <input
                        id="profileName"
                        type="text"
                        value={profileName}
                        onChange={(e) => setProfileName(e.target.value)}
                        placeholder="e.g., Academic Writing, Blog Posts, Business Emails"
                        className={styles.textInput}
                    />
                </div>

                <div className={styles.formGroup}>
                    <label htmlFor="profileDescription">Description (Optional)</label>
                    <textarea
                        id="profileDescription"
                        value={profileDescription}
                        onChange={(e) => setProfileDescription(e.target.value)}
                        placeholder="Describe when you'd use this writing style..."
                        className={styles.textareaInput}
                        rows={3}
                    />
                </div>

                <div className={styles.formGroup}>
                    <label htmlFor="defaultStrength">Default Style Strength: {defaultStrength}%</label>
                    <input
                        id="defaultStrength"
                        type="range"
                        min="0"
                        max="100"
                        value={defaultStrength}
                        onChange={(e) => setDefaultStrength(parseInt(e.target.value))}
                        className={styles.rangeInput}
                    />
                    <div className={styles.strengthDescription}>
                        {defaultStrength < 25 && "Subtle style application"}
                        {defaultStrength >= 25 && defaultStrength < 75 && "Balanced style application"}
                        {defaultStrength >= 75 && "Strong style application"}
                    </div>
                </div>
            </div>

            <div className={styles.stepActions}>
                <button onClick={saveProfile} className={styles.primaryButton}>
                    Create Style Profile
                </button>
                <button onClick={() => setStep(2)} className={styles.secondaryButton}>
                    Back to Analysis
                </button>
            </div>
        </div>
    );

    return (
        <div className={styles.overlay}>
            <div className={styles.modal}>
                <div className={styles.header}>
                    <h2>Create Writing Style Profile</h2>
                    <button onClick={onClose} className={styles.closeButton}>×</button>
                </div>

                <div className={styles.progressBar}>
                    <div className={`${styles.progressStep} ${step >= 1 ? styles.active : ''}`}>1. Upload</div>
                    <div className={`${styles.progressStep} ${step >= 2 ? styles.active : ''}`}>2. Analysis</div>
                    <div className={`${styles.progressStep} ${step >= 3 ? styles.active : ''}`}>3. Configure</div>
                </div>

                {error && (
                    <div className={styles.error}>
                        {error}
                    </div>
                )}

                {step === 1 && renderUploadStep()}
                {step === 2 && renderAnalysisStep()}
                {step === 3 && renderConfigurationStep()}
            </div>
        </div>
    );
}
