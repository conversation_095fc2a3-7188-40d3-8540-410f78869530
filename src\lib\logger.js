import winston from 'winston';
import path from 'path';

// Define log levels
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each level
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which level to log based on environment
const level = () => {
  const env = process.env.NODE_ENV || 'development';
  const isDevelopment = env === 'development';
  return isDevelopment ? 'debug' : 'warn';
};

// Define different log formats
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} ${info.level}: ${info.message}`
  )
);

const fileLogFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Define transports
const transports = [
  // Console transport
  new winston.transports.Console({
    level: level(),
    format: logFormat,
  }),
];

// Add file transports only in production or when LOG_FILE_PATH is set
if (process.env.NODE_ENV === 'production' || process.env.LOG_FILE_PATH) {
  const logDir = process.env.LOG_FILE_PATH 
    ? path.dirname(process.env.LOG_FILE_PATH)
    : './logs';
  
  const logFile = process.env.LOG_FILE_PATH || path.join(logDir, 'app.log');
  const errorLogFile = path.join(path.dirname(logFile), 'error.log');

  transports.push(
    // File transport for all logs
    new winston.transports.File({
      filename: logFile,
      level: 'info',
      format: fileLogFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // Separate file for errors
    new winston.transports.File({
      filename: errorLogFile,
      level: 'error',
      format: fileLogFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );
}

// Create the logger
const logger = winston.createLogger({
  level: level(),
  levels,
  format: fileLogFormat,
  transports,
  exitOnError: false,
});

// Create a stream object for Morgan HTTP logging
logger.stream = {
  write: (message) => {
    logger.http(message.trim());
  },
};

// Helper functions for structured logging
logger.logError = (error, context = {}) => {
  logger.error({
    message: error.message,
    stack: error.stack,
    ...context,
  });
};

logger.logApiRequest = (req, res, responseTime) => {
  logger.http({
    method: req.method,
    url: req.url,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    userAgent: req.headers['user-agent'],
    ip: req.ip || req.connection.remoteAddress,
  });
};

logger.logUserAction = (userId, action, details = {}) => {
  logger.info({
    type: 'USER_ACTION',
    userId,
    action,
    ...details,
  });
};

logger.logPaymentEvent = (eventType, data = {}) => {
  logger.info({
    type: 'PAYMENT_EVENT',
    eventType,
    ...data,
  });
};

export default logger;
