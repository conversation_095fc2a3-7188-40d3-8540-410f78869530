import React, { useState, useEffect } from 'react';
import ViralCTA from './ViralCTA';
import ViralErrorBoundary from './ViralErrorBoundary';

const ClientOnlyViralCTA = (props) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Don't render on server to avoid hydration issues
  if (!isClient) {
    return (
      <div style={{ 
        minHeight: '200px', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        color: '#666',
        fontSize: '14px'
      }}>
        Loading viral content...
      </div>
    );
  }

  return (
    <ViralErrorBoundary>
      <ViralCTA {...props} />
    </ViralErrorBoundary>
  );
};

export default ClientOnlyViralCTA;
