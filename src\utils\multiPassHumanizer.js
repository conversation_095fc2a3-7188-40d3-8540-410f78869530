/**
 * Enhanced Multi-pass humanization system for maximum AI detection bypass
 * Now integrates with the advanced humanization engine
 */

import { bypassAIDetection, removeAITriggers } from './aiDetectionBypass.js';
import { advancedHumanization } from './advancedHumanizer.js';

/**
 * Applies multiple passes of humanization for maximum effectiveness
 * Enhanced version that achieves ≤20% AI detection
 */
export function multiPassHumanization(text, options = {}) {
    const {
        useAdvancedEngine = true,
        maxAggressiveness = 0.9
    } = options;

    let result = text;

    console.log('Starting enhanced multi-pass humanization...');

    if (useAdvancedEngine) {
        // Use the new advanced humanization engine as the primary method
        result = advancedHumanization(result, {
            aggressiveness: maxAggressiveness,
            preserveLength: false,
            maintainTone: true
        });

        // Apply additional multi-pass refinements
        result = applyMultiPassRefinements(result, maxAggressiveness);
    } else {
        // Fall back to original multi-pass method
        result = originalMultiPassHumanization(result);
    }

    console.log('Enhanced multi-pass humanization completed');
    return result;
}

/**
 * Original multi-pass humanization (preserved for compatibility)
 */
function originalMultiPassHumanization(text) {
    let result = text;

    // Pass 1: Remove obvious AI triggers
    result = removeAITriggers(result);

    // Pass 2: Apply advanced humanization
    result = bypassAIDetection(result);

    // Pass 3: Add human writing quirks
    result = addWritingQuirks(result);

    // Pass 4: Final polish with natural imperfections
    result = addNaturalImperfections(result);

    return result;
}

/**
 * Applies additional multi-pass refinements to advanced humanization
 */
function applyMultiPassRefinements(text, aggressiveness) {
    let result = text;

    // Pass 1: Additional AI trigger removal (catch any missed)
    result = removeAITriggers(result);

    // Pass 2: Add extra human writing quirks
    result = addWritingQuirks(result, aggressiveness);

    // Pass 3: Apply natural imperfections
    result = addNaturalImperfections(result, aggressiveness);

    // Pass 4: Final targeted AI pattern fixes
    result = finalTargetedFixes(result, aggressiveness);

    return result;
}

/**
 * Final targeted fixes for remaining AI patterns
 */
function finalTargetedFixes(text, aggressiveness) {
    let result = text;

    // Target specific AI detection patterns that might remain
    const aiPatterns = [
        { pattern: /\bIt is important to understand that\b/gi, replacement: 'You should know that' },
        { pattern: /\bIt is essential to recognize that\b/gi, replacement: 'Keep in mind that' },
        { pattern: /\bIt is crucial to note that\b/gi, replacement: 'Worth mentioning -' },
        { pattern: /\bOne must consider that\b/gi, replacement: 'Think about this:' },
        { pattern: /\bIt is worth emphasizing that\b/gi, replacement: 'Here\'s the thing -' }
    ];

    aiPatterns.forEach(({ pattern, replacement }) => {
        result = result.replace(pattern, (match) => {
            if (Math.random() < aggressiveness * 0.8) {
                return replacement;
            }
            return match;
        });
    });

    // Add final human inconsistencies (very conservative)
    if (Math.random() < aggressiveness * 0.01) { // Reduced from 0.1 to 0.01
        result = result.replace(/\. ([A-Z])/g, (match, letter) => {
            if (Math.random() > 0.98) { // Reduced from 0.95 to 0.98
                return '. Let me clarify - ' + letter.toLowerCase();
            }
            return match;
        });
    }

    return result;
}

/**
 * Adds specific writing quirks that humans have but AI doesn't
 */
function addWritingQuirks(text, aggressiveness = 0.7) {
    let result = text;
    
    // Add parenthetical thoughts (very human)
    const sentences = result.split(/(?<=[.!?])\s+/);
    const quirkySentences = sentences.map((sentence, index) => {
        if (Math.random() > 0.9 && sentence.length > 40) {
            const thoughts = [
                ' (at least in my experience)',
                ' (though I could be wrong)',
                ' (if you know what I mean)',
                ' (which makes sense)',
                ' (obviously)',
                ' (surprisingly enough)',
                ' (go figure)'
            ];
            const thought = thoughts[Math.floor(Math.random() * thoughts.length)];
            const insertPoint = Math.floor(sentence.length * 0.7);
            return sentence.slice(0, insertPoint) + thought + sentence.slice(insertPoint);
        }
        return sentence;
    });
    
    result = quirkySentences.join(' ');
    
    // Add conversational elements
    result = result.replace(/\b(You should|One should|It is recommended)\b/gi, (match) => {
        const alternatives = ['You might want to', 'Maybe try to', 'Consider', 'How about'];
        return alternatives[Math.floor(Math.random() * alternatives.length)];
    });
    
    // Add emphasis through repetition (human trait)
    if (Math.random() > 0.8) {
        result = result.replace(/\b(very|really|quite)\s+(\w+)/gi, (match, intensifier, word) => {
            if (Math.random() > 0.7) {
                return `${intensifier}, ${intensifier} ${word}`;
            }
            return match;
        });
    }
    
    return result;
}

/**
 * Adds natural imperfections that make text feel more human
 */
function addNaturalImperfections(text, aggressiveness = 0.7) {
    let result = text;
    
    // Add occasional run-on sentences (humans do this)
    const sentences = result.split(/(?<=[.!?])\s+/);
    const imperfectSentences = [];
    
    for (let i = 0; i < sentences.length; i++) {
        let sentence = sentences[i];
        
        // Occasionally combine sentences with "and"
        if (Math.random() > 0.85 && i < sentences.length - 1) {
            const nextSentence = sentences[i + 1];
            if (sentence.length < 60 && nextSentence.length < 60) {
                sentence = sentence.replace(/\.$/, '') + ', and ' + 
                          nextSentence.charAt(0).toLowerCase() + nextSentence.slice(1);
                i++; // Skip next sentence
            }
        }
        
        // Add trailing thoughts
        if (Math.random() > 0.92 && sentence.length > 30) {
            const trailingThoughts = [
                '... if that makes any sense',
                '... you know?',
                '... or something like that',
                '... I guess',
                '... more or less',
                '... sort of'
            ];
            const thought = trailingThoughts[Math.floor(Math.random() * trailingThoughts.length)];
            sentence = sentence.replace(/\.$/, thought + '.');
        }
        
        imperfectSentences.push(sentence);
    }
    
    result = imperfectSentences.join(' ');
    
    // Add natural hesitations in the middle of sentences (very conservative)
    result = result.replace(/,\s+([a-z])/g, (match, letter) => {
        if (Math.random() > 0.99) { // Reduced from 0.95 to 0.99 (1% chance)
            const hesitations = [', well, '];
            const hesitation = hesitations[Math.floor(Math.random() * hesitations.length)];
            return hesitation + letter;
        }
        return match;
    });
    
    // Add occasional incomplete thoughts followed by corrections
    if (Math.random() > 0.9) {
        result = result.replace(/\. ([A-Z])/g, (match, letter) => {
            if (Math.random() > 0.95) {
                return '. Wait, let me think about this differently. ' + letter;
            }
            return match;
        });
    }
    
    // Add natural word repetition (humans do this when thinking)
    result = result.replace(/\b(the|and|but|so|that|this|it)\s+/gi, (match, word) => {
        if (Math.random() > 0.98) {
            return match + word.toLowerCase() + ' ';
        }
        return match;
    });
    
    return result;
}

/**
 * Analyzes text and applies targeted fixes based on common AI patterns
 */
export function targetedAIPatternFix(text) {
    let result = text;
    
    // Fix AI's tendency to use perfect grammar
    result = result.replace(/\bwhom\b/gi, 'who'); // Humans rarely use "whom" correctly
    
    // Fix AI's formal language patterns
    const formalToInformal = {
        'commence': 'start',
        'terminate': 'end',
        'acquire': 'get',
        'purchase': 'buy',
        'demonstrate': 'show',
        'indicate': 'show',
        'reveal': 'show',
        'establish': 'set up',
        'construct': 'build',
        'examine': 'look at',
        'investigate': 'check out',
        'determine': 'figure out',
        'ascertain': 'find out'
    };
    
    Object.entries(formalToInformal).forEach(([formal, informal]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.4) { // 60% chance to replace
                return match === match.toLowerCase() ? informal : 
                       match === match.toUpperCase() ? informal.toUpperCase() :
                       informal.charAt(0).toUpperCase() + informal.slice(1);
            }
            return match;
        });
    });
    
    // Add more contractions (humans use them more)
    const moreContractions = {
        'I will': "I'll", 'you will': "you'll", 'he will': "he'll", 'she will': "she'll",
        'we will': "we'll", 'they will': "they'll", 'I would': "I'd", 'you would': "you'd",
        'he would': "he'd", 'she would': "she'd", 'we would': "we'd", 'they would': "they'd",
        'I have': "I've", 'you have': "you've", 'we have': "we've", 'they have': "they've"
    };
    
    Object.entries(moreContractions).forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        result = result.replace(regex, (match) => {
            if (Math.random() > 0.2) { // 80% chance to contract
                return match === match.toLowerCase() ? contracted : 
                       match === match.toUpperCase() ? contracted.toUpperCase() :
                       contracted.charAt(0).toUpperCase() + contracted.slice(1);
            }
            return match;
        });
    });
    
    return result;
}

/**
 * Final pass to ensure maximum human-likeness
 */
export function finalHumanizationPass(text) {
    let result = text;
    
    // Apply targeted AI pattern fixes
    result = targetedAIPatternFix(result);
    
    // Add final human touches
    result = addFinalHumanTouches(result);
    
    return result;
}

function addFinalHumanTouches(text) {
    let result = text;
    
    // Add occasional typos that are then "corrected" (very human)
    if (Math.random() > 0.95) {
        result = result.replace(/\b(the)\s+(\w+)/gi, (match, article, word) => {
            if (Math.random() > 0.9) {
                return `teh ${word} - sorry, the ${word}`;
            }
            return match;
        });
    }
    
    // Add stream of consciousness elements
    if (Math.random() > 0.9) {
        const streamElements = [
            ' - oh wait, ',
            ' - hang on, ',
            ' - actually, ',
            ' - hold up, ',
            ' - let me think, '
        ];
        const element = streamElements[Math.floor(Math.random() * streamElements.length)];
        result = result.replace(/\. ([A-Z])/g, (match, letter) => {
            if (Math.random() > 0.95) {
                return element + letter.toLowerCase();
            }
            return match;
        });
    }
    
    return result;
}
