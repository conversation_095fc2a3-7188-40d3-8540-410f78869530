/**
 * Test script to verify the "text.includes is not a function" fix
 */

import fetch from 'node-fetch';

const API_URL = 'http://localhost:3004/api/process';

// Test cases that might trigger the error
const testCases = [
    {
        name: "Valid string input",
        data: { text: "This is a test sentence that should be humanized properly." }
    },
    {
        name: "Empty string",
        data: { text: "" }
    },
    {
        name: "Whitespace only",
        data: { text: "   " }
    },
    {
        name: "Null text",
        data: { text: null }
    },
    {
        name: "Undefined text",
        data: { text: undefined }
    },
    {
        name: "Number instead of text",
        data: { text: 12345 }
    },
    {
        name: "Object instead of text",
        data: { text: { content: "test" } }
    },
    {
        name: "Array instead of text",
        data: { text: ["test", "content"] }
    }
];

async function testAPI() {
    console.log('🧪 TESTING API FIX FOR "text.includes is not a function" ERROR');
    console.log('═'.repeat(70));
    
    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`\n${i + 1}. Testing: ${testCase.name}`);
        console.log('─'.repeat(40));
        
        try {
            const response = await fetch(API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testCase.data)
            });
            
            const result = await response.json();
            
            if (response.ok) {
                console.log('✅ SUCCESS');
                console.log(`Status: ${response.status}`);
                if (result.modifiedText) {
                    console.log(`Output: "${result.modifiedText.substring(0, 100)}${result.modifiedText.length > 100 ? '...' : ''}"`);
                } else {
                    console.log('Output: No modified text (expected for invalid inputs)');
                }
            } else {
                console.log('⚠️  EXPECTED ERROR (handled gracefully)');
                console.log(`Status: ${response.status}`);
                console.log(`Message: ${result.message}`);
            }
            
        } catch (error) {
            if (error.message.includes('text.includes is not a function')) {
                console.log('❌ FAILED - Original error still occurs!');
                console.log(`Error: ${error.message}`);
            } else {
                console.log('⚠️  OTHER ERROR (may be expected)');
                console.log(`Error: ${error.message}`);
            }
        }
    }
    
    console.log('\n\n🎉 TEST COMPLETED');
    console.log('═'.repeat(70));
    console.log('\n📊 SUMMARY:');
    console.log('- If all tests show ✅ SUCCESS or ⚠️ EXPECTED ERROR, the fix is working');
    console.log('- If any test shows ❌ FAILED with "text.includes is not a function", the fix needs more work');
    console.log('\n💡 The API should gracefully handle invalid inputs without crashing');
}

// Test with a simple valid case first
async function quickTest() {
    console.log('🚀 QUICK TEST - Valid input');
    console.log('─'.repeat(30));
    
    try {
        const response = await fetch(API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: "The implementation of artificial intelligence systems requires careful consideration of multiple factors."
            })
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ API is working correctly!');
            console.log(`Original: "The implementation of artificial intelligence systems..."`);
            console.log(`Humanized: "${result.modifiedText.substring(0, 100)}..."`);
            console.log(`Processing time: ${result.processingTime || 'N/A'}ms`);
        } else {
            console.log('❌ API returned error:', result.message);
        }
        
    } catch (error) {
        console.log('❌ Connection error:', error.message);
        console.log('Make sure the development server is running on port 3004');
    }
}

// Run tests
async function runTests() {
    await quickTest();
    console.log('\n');
    await testAPI();
}

runTests().catch(console.error);
