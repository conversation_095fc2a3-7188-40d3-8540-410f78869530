import React from 'react';
import styles from '../../styles/HowToGuide.module.css';

const HowToGuide = ({ 
  title = "How to Make AI Text Undetectable in 3 Simple Steps",
  steps = []
}) => {
  const defaultSteps = [
    {
      number: 1,
      title: "Paste Your AI-Generated Text",
      description: "Copy and paste your ChatGPT, GPT-4, Claude, or any AI-generated content into our text editor. Our tool supports text up to 3000 words for premium users.",
      icon: "📝",
      details: [
        "Works with all AI models (ChatGPT, GPT-4, <PERSON>, <PERSON><PERSON>)",
        "Supports academic papers, blog posts, emails, and more",
        "No registration required for basic usage"
      ]
    },
    {
      number: 2,
      title: "Choose Humanization Level",
      description: "Select your preferred humanization intensity. Conservative mode makes minimal changes while Aggressive mode provides maximum detection bypass.",
      icon: "⚙️",
      details: [
        "Conservative: Minimal changes, preserves original style",
        "Balanced: Optimal mix of detection bypass and readability",
        "Aggressive: Maximum transformation for highest bypass rate"
      ]
    },
    {
      number: 3,
      title: "Get Undetectable Human-Like Text",
      description: "Click 'Humanize' and receive your transformed text in seconds. Our 95% success rate ensures your content bypasses AI detection tools.",
      icon: "✨",
      details: [
        "Processing completed in under 5 seconds",
        "95% success rate against AI detectors",
        "Download or copy your humanized text instantly"
      ]
    }
  ];

  const stepsToRender = steps.length > 0 ? steps : defaultSteps;

  return (
    <section className={styles.howToSection} id="how-to-guide">
      <div className={styles.container}>
        <header className={styles.header}>
          <h2 className={styles.title}>{title}</h2>
          <p className={styles.subtitle}>
            Transform your AI-generated content into undetectable, human-like text with our proven 3-step process
          </p>
        </header>

        <div className={styles.stepsContainer}>
          {stepsToRender.map((step, index) => (
            <div key={index} className={styles.step}>
              <div className={styles.stepHeader}>
                <div className={styles.stepNumber}>
                  <span className={styles.number}>{step.number}</span>
                  <span className={styles.icon}>{step.icon}</span>
                </div>
                <div className={styles.stepContent}>
                  <h3 className={styles.stepTitle}>{step.title}</h3>
                  <p className={styles.stepDescription}>{step.description}</p>
                </div>
              </div>
              
              {step.details && (
                <div className={styles.stepDetails}>
                  <ul className={styles.detailsList}>
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className={styles.detailItem}>
                        <span className={styles.checkmark}>✓</span>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              {index < stepsToRender.length - 1 && (
                <div className={styles.stepConnector}>
                  <div className={styles.connectorLine}></div>
                  <div className={styles.connectorArrow}>↓</div>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className={styles.cta}>
          <h3 className={styles.ctaTitle}>Ready to Make Your AI Text Undetectable?</h3>
          <p className={styles.ctaDescription}>
            Join thousands of users who trust GhostLayer for reliable AI detection bypass
          </p>
          <button 
            className={styles.ctaButton}
            onClick={() => document.querySelector('#text-input')?.focus()}
          >
            Start Humanizing Now - Free
          </button>
        </div>
      </div>
    </section>
  );
};

export default HowToGuide;
