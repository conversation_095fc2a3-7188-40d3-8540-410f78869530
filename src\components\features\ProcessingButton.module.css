.buttonContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-6);
}

.processButton {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4) var(--space-8);
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  border: none;
  border-radius: var(--radius-xl);
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  min-width: 200px;
  min-height: 56px;
}

.processButton:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.processButton:active:not(.disabled) {
  transform: translateY(0);
}

.processButton.disabled {
  background: var(--secondary-300);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

.processButton.loading {
  cursor: wait;
}

.buttonContent {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  position: relative;
  z-index: 2;
}

.buttonIcon {
  transition: transform var(--transition-fast);
}

.processButton:hover:not(.disabled) .buttonIcon {
  transform: scale(1.1);
}

.buttonText {
  font-weight: 600;
  letter-spacing: 0.025em;
}

.buttonBackground {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
}

.buttonBackground.hovered {
  opacity: 1;
}

.rippleContainer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  border-radius: var(--radius-xl);
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

.loadingSpinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.statusContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
}

.statusItem {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--secondary-200);
}

.statusIcon {
  color: var(--secondary-500);
}

.statusText {
  font-size: 0.875rem;
  color: var(--secondary-600);
  font-weight: 500;
}

.statusDot {
  width: 8px;
  height: 8px;
  background: var(--primary-500);
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

.stepsContainer {
  display: flex;
  gap: var(--space-6);
  padding: var(--space-4);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--secondary-200);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  text-align: center;
}

.stepDot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--secondary-300);
  transition: all var(--transition-fast);
}

.stepDot.active {
  background: var(--primary-500);
  animation: stepPulse 2s infinite;
}

@keyframes stepPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 var(--primary-500);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 4px rgba(14, 165, 233, 0.3);
  }
}

.stepText {
  font-size: 0.75rem;
  color: var(--secondary-600);
  font-weight: 500;
  max-width: 80px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .buttonContainer {
    padding: var(--space-4);
  }
  
  .processButton {
    padding: var(--space-3) var(--space-6);
    font-size: 1rem;
    min-width: 180px;
    min-height: 48px;
  }
  
  .stepsContainer {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .step {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
  }
  
  .stepText {
    max-width: none;
  }
}

@media (max-width: 480px) {
  .processButton {
    padding: var(--space-3) var(--space-4);
    min-width: 160px;
  }
  
  .buttonText {
    font-size: 0.875rem;
  }
  
  .buttonIcon {
    width: 18px;
    height: 18px;
  }
}
