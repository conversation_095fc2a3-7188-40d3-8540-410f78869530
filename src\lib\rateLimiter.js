import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';
import { RateLimitError } from './errorHandler';
import logger from './logger';

// Configuration
const RATE_LIMIT_CONFIG = {
  // General API rate limiting
  api: {
    points: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // Number of requests
    duration: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900, // Per 15 minutes (900 seconds)
  },
  // Stricter limits for text processing
  textProcessing: {
    points: 10, // 10 requests
    duration: 300, // Per 5 minutes
  },
  // Authentication attempts
  auth: {
    points: 5, // 5 attempts
    duration: 900, // Per 15 minutes
  },
  // Stripe webhook (more lenient)
  webhook: {
    points: 1000, // 1000 requests
    duration: 60, // Per minute
  },
};

// Create rate limiters
const createRateLimiter = (config, keyPrefix) => {
  const options = {
    keyPrefix,
    points: config.points,
    duration: config.duration,
    blockDuration: config.duration, // Block for the same duration
  };

  // Use Redis in production if available, otherwise use memory
  if (process.env.REDIS_URL && process.env.NODE_ENV === 'production') {
    try {
      const redis = require('redis');
      const redisClient = redis.createClient({
        url: process.env.REDIS_URL,
        legacyMode: true,
      });
      
      return new RateLimiterRedis({
        ...options,
        storeClient: redisClient,
      });
    } catch (error) {
      logger.warn('Redis not available, falling back to memory rate limiter');
    }
  }

  return new RateLimiterMemory(options);
};

// Initialize rate limiters
const rateLimiters = {
  api: createRateLimiter(RATE_LIMIT_CONFIG.api, 'api_rl'),
  textProcessing: createRateLimiter(RATE_LIMIT_CONFIG.textProcessing, 'text_rl'),
  auth: createRateLimiter(RATE_LIMIT_CONFIG.auth, 'auth_rl'),
  webhook: createRateLimiter(RATE_LIMIT_CONFIG.webhook, 'webhook_rl'),
};

// Helper function to get client identifier
const getClientId = (req) => {
  // Try to get user ID from session first
  if (req.user?.id) {
    return `user_${req.user.id}`;
  }
  
  // Fall back to IP address
  return req.ip || req.connection.remoteAddress || 'unknown';
};

// Rate limiting middleware factory
export const createRateLimit = (limiterType = 'api') => {
  const limiter = rateLimiters[limiterType];
  
  if (!limiter) {
    throw new Error(`Unknown rate limiter type: ${limiterType}`);
  }

  return async (req, res, next) => {
    const clientId = getClientId(req);
    
    try {
      const result = await limiter.consume(clientId);
      
      // Add rate limit headers
      res.setHeader('X-RateLimit-Limit', limiter.points);
      res.setHeader('X-RateLimit-Remaining', result.remainingPoints);
      res.setHeader('X-RateLimit-Reset', new Date(Date.now() + result.msBeforeNext));
      
      // Log rate limit usage for monitoring
      if (result.remainingPoints < limiter.points * 0.1) { // Less than 10% remaining
        logger.warn('Rate limit nearly exceeded', {
          clientId,
          limiterType,
          remainingPoints: result.remainingPoints,
          totalPoints: limiter.points,
        });
      }
      
      next();
    } catch (rejRes) {
      // Rate limit exceeded
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      
      res.setHeader('Retry-After', secs);
      res.setHeader('X-RateLimit-Limit', limiter.points);
      res.setHeader('X-RateLimit-Remaining', 0);
      res.setHeader('X-RateLimit-Reset', new Date(Date.now() + rejRes.msBeforeNext));
      
      logger.warn('Rate limit exceeded', {
        clientId,
        limiterType,
        retryAfter: secs,
        url: req.url,
        method: req.method,
      });
      
      throw new RateLimitError(`Too many requests. Try again in ${secs} seconds.`);
    }
  };
};

// Specific rate limiters for common use cases
export const apiRateLimit = createRateLimit('api');
export const textProcessingRateLimit = createRateLimit('textProcessing');
export const authRateLimit = createRateLimit('auth');
export const webhookRateLimit = createRateLimit('webhook');

// Advanced rate limiting for premium users
export const createPremiumRateLimit = (premiumMultiplier = 10) => {
  return async (req, res, next) => {
    const clientId = getClientId(req);
    const isUserPremium = req.user?.subscriptionTier?.includes('premium');
    
    // Use different limits for premium users
    const limiterType = isUserPremium ? 'api' : 'textProcessing';
    const limiter = rateLimiters[limiterType];
    
    // Adjust points for premium users
    const effectivePoints = isUserPremium 
      ? limiter.points * premiumMultiplier 
      : limiter.points;
    
    try {
      const result = await limiter.consume(clientId, isUserPremium ? 1 : 1);
      
      res.setHeader('X-RateLimit-Limit', effectivePoints);
      res.setHeader('X-RateLimit-Remaining', result.remainingPoints);
      res.setHeader('X-RateLimit-Reset', new Date(Date.now() + result.msBeforeNext));
      
      next();
    } catch (rejRes) {
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      
      res.setHeader('Retry-After', secs);
      res.setHeader('X-RateLimit-Limit', effectivePoints);
      res.setHeader('X-RateLimit-Remaining', 0);
      res.setHeader('X-RateLimit-Reset', new Date(Date.now() + rejRes.msBeforeNext));
      
      logger.warn('Premium rate limit exceeded', {
        clientId,
        isUserPremium,
        retryAfter: secs,
        url: req.url,
        method: req.method,
      });
      
      throw new RateLimitError(`Too many requests. Try again in ${secs} seconds.`);
    }
  };
};

export default rateLimiters;
