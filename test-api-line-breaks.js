/**
 * Test API-level line break preservation
 */

import { balancedHumanization } from './src/utils/balancedHumanizer.js';

const testText = `This is very important content that shows significant benefits for organizations.

The implementation of artificial intelligence systems requires careful planning and substantial investment. Many businesses are finding that the results justify the costs.

This technology is also beneficial and provides good advantages for modern companies.`;

console.log('=== API-LEVEL LINE BREAK TEST ===');
console.log('Original:');
console.log(JSON.stringify(testText));
console.log('\nOriginal (formatted):');
console.log(testText);

// Test with the same parameters as the API
const result = balancedHumanization(testText, null, 0, {
    useAdvanced: true,
    aggressiveness: 0.7,
    maintainTone: true
});

console.log('\nAPI-level Humanized:');
console.log(JSON.stringify(result));
console.log('\nAPI-level Humanized (formatted):');
console.log(result);

console.log('\n=== DETAILED ANALYSIS ===');
console.log('Original line count:', testText.split('\n').length);
console.log('Humanized line count:', result.split('\n').length);
console.log('Line breaks preserved:', testText.split('\n').length === result.split('\n').length ? '✅' : '❌');

// Check for paragraph breaks specifically
const originalParagraphs = testText.split('\n\n');
const humanizedParagraphs = result.split('\n\n');
console.log('Original paragraphs:', originalParagraphs.length);
console.log('Humanized paragraphs:', humanizedParagraphs.length);
console.log('Paragraph breaks preserved:', originalParagraphs.length === humanizedParagraphs.length ? '✅' : '❌');

// Show each line individually
console.log('\n=== LINE BY LINE COMPARISON ===');
const originalLines = testText.split('\n');
const humanizedLines = result.split('\n');

console.log('Original lines:');
originalLines.forEach((line, i) => {
    console.log(`  ${i}: "${line}"`);
});

console.log('\nHumanized lines:');
humanizedLines.forEach((line, i) => {
    console.log(`  ${i}: "${line}"`);
});

// Test with different text that should trigger more changes
console.log('\n=== TESTING WITH MORE AGGRESSIVE CONTENT ===');
const aggressiveText = `This is very important and shows significant benefits.

The technology is also good and provides excellent advantages.

These solutions are very effective and demonstrate substantial improvements.`;

console.log('Aggressive test original:');
console.log(aggressiveText);

const aggressiveResult = balancedHumanization(aggressiveText, null, 0, {
    useAdvanced: true,
    aggressiveness: 0.8,
    maintainTone: true
});

console.log('\nAggressive test result:');
console.log(aggressiveResult);
console.log('Aggressive paragraph breaks preserved:', aggressiveText.split('\n\n').length === aggressiveResult.split('\n\n').length ? '✅' : '❌');
