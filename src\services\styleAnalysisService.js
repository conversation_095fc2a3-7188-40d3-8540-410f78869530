/**
 * Writing Style Cloning Engine
 * Analyzes user writing samples and applies personal style to humanized text
 */

/**
 * Analyzes writing samples to extract style patterns
 * @param {string[]} writingSamples - Array of user writing samples
 * @returns {Object} Style profile object
 */
export function analyzeWritingStyle(writingSamples) {
    if (!writingSamples || writingSamples.length === 0) {
        throw new Error('At least one writing sample is required');
    }

    const combinedText = writingSamples.join(' ');
    
    return {
        sentencePatterns: analyzeSentencePatterns(combinedText),
        vocabularyComplexity: analyzeVocabularyComplexity(combinedText),
        transitionPhrases: extractTransitionPhrases(combinedText),
        punctuationStyle: analyzePunctuationStyle(combinedText),
        personalExpressions: extractPersonalExpressions(combinedText),
        writingQuirks: identifyWritingQuirks(combinedText),
        toneAnalysis: analyzeTone(combinedText)
    };
}

/**
 * Analyzes sentence length patterns and structure preferences
 */
function analyzeSentencePatterns(text) {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const lengths = sentences.map(s => s.trim().length);
    
    // Calculate statistics
    const avgLength = lengths.reduce((a, b) => a + b, 0) / lengths.length;
    const shortSentences = lengths.filter(l => l < 15).length;
    const mediumSentences = lengths.filter(l => l >= 15 && l <= 25).length;
    const longSentences = lengths.filter(l => l > 25).length;
    
    // Analyze sentence starters
    const starters = sentences.map(s => {
        const words = s.trim().split(/\s+/);
        return words.slice(0, 2).join(' ').toLowerCase();
    });
    
    const starterFrequency = {};
    starters.forEach(starter => {
        starterFrequency[starter] = (starterFrequency[starter] || 0) + 1;
    });
    
    return {
        averageLength: Math.round(avgLength),
        lengthDistribution: {
            short: shortSentences / sentences.length,
            medium: mediumSentences / sentences.length,
            long: longSentences / sentences.length
        },
        preferredStarters: Object.entries(starterFrequency)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([starter]) => starter),
        complexity: avgLength > 20 ? 'complex' : avgLength > 15 ? 'moderate' : 'simple'
    };
}

/**
 * Analyzes vocabulary complexity and word choice patterns
 */
function analyzeVocabularyComplexity(text) {
    const words = text.toLowerCase().match(/\b[a-z]+\b/g) || [];
    const uniqueWords = new Set(words);
    const wordFrequency = {};
    
    words.forEach(word => {
        wordFrequency[word] = (wordFrequency[word] || 0) + 1;
    });
    
    // Analyze word length distribution
    const wordLengths = words.map(w => w.length);
    const avgWordLength = wordLengths.reduce((a, b) => a + b, 0) / wordLengths.length;
    
    // Identify sophisticated vocabulary
    const sophisticatedWords = words.filter(word => 
        word.length > 7 && !isCommonWord(word)
    );
    
    // Calculate lexical diversity (Type-Token Ratio)
    const lexicalDiversity = uniqueWords.size / words.length;
    
    return {
        averageWordLength: Math.round(avgWordLength * 10) / 10,
        lexicalDiversity: Math.round(lexicalDiversity * 100) / 100,
        sophisticatedWordRatio: sophisticatedWords.length / words.length,
        preferredWords: Object.entries(wordFrequency)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 20)
            .filter(([word]) => !isCommonWord(word))
            .slice(0, 10)
            .map(([word]) => word),
        complexityLevel: lexicalDiversity > 0.6 ? 'high' : lexicalDiversity > 0.4 ? 'medium' : 'low'
    };
}

/**
 * Extracts commonly used transition phrases and connectors
 */
function extractTransitionPhrases(text) {
    const transitionPatterns = [
        /\b(however|nevertheless|furthermore|moreover|additionally|consequently|therefore|thus|hence|meanwhile|subsequently|ultimately|essentially|basically|actually|obviously|clearly|certainly|definitely|probably|perhaps|maybe|possibly|interestingly|surprisingly|unfortunately|fortunately|specifically|particularly|especially|notably|importantly|significantly)\b/gi,
        /\b(in fact|in other words|for example|for instance|that is|in particular|in addition|on the other hand|as a result|in conclusion|to summarize|in summary|all in all|by the way|speaking of|with regard to|in terms of|as far as|as long as|as soon as)\b/gi,
        /\b(first|second|third|finally|lastly|next|then|after that|before that|previously|earlier|later|afterwards|meanwhile|simultaneously|at the same time|in the meantime)\b/gi
    ];
    
    const foundTransitions = [];
    transitionPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        foundTransitions.push(...matches.map(m => m.toLowerCase()));
    });
    
    const transitionFrequency = {};
    foundTransitions.forEach(transition => {
        transitionFrequency[transition] = (transitionFrequency[transition] || 0) + 1;
    });
    
    return {
        mostUsed: Object.entries(transitionFrequency)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([phrase, count]) => ({ phrase, count })),
        totalTransitions: foundTransitions.length,
        transitionDensity: foundTransitions.length / (text.split(/[.!?]+/).length || 1)
    };
}

/**
 * Analyzes punctuation usage patterns
 */
function analyzePunctuationStyle(text) {
    const punctuationCounts = {
        periods: (text.match(/\./g) || []).length,
        commas: (text.match(/,/g) || []).length,
        semicolons: (text.match(/;/g) || []).length,
        colons: (text.match(/:/g) || []).length,
        exclamations: (text.match(/!/g) || []).length,
        questions: (text.match(/\?/g) || []).length,
        dashes: (text.match(/[-—]/g) || []).length,
        parentheses: (text.match(/[()]/g) || []).length / 2,
        quotations: (text.match(/["'"]/g) || []).length / 2
    };
    
    const totalSentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
    
    return {
        usage: punctuationCounts,
        preferences: {
            commaHeavy: punctuationCounts.commas / totalSentences > 2,
            dashUser: punctuationCounts.dashes > 0,
            parentheticalUser: punctuationCounts.parentheses > 0,
            exclamatory: punctuationCounts.exclamations / totalSentences > 0.1,
            inquisitive: punctuationCounts.questions / totalSentences > 0.1
        },
        density: Object.values(punctuationCounts).reduce((a, b) => a + b, 0) / text.length
    };
}

/**
 * Extracts personal expressions and unique phrases
 */
function extractPersonalExpressions(text) {
    // Look for personal markers
    const personalMarkers = text.match(/\b(I think|I believe|I feel|I guess|I suppose|in my opinion|personally|from my perspective|it seems to me|I would say|I tend to|I usually|I often|I always|I never)\b/gi) || [];
    
    // Look for hedging language
    const hedgingPhrases = text.match(/\b(kind of|sort of|pretty much|more or less|to some extent|somewhat|rather|quite|fairly|relatively|apparently|seemingly|presumably|arguably|supposedly)\b/gi) || [];
    
    // Look for intensifiers
    const intensifiers = text.match(/\b(really|very|extremely|incredibly|absolutely|totally|completely|entirely|perfectly|utterly|quite|rather|pretty|fairly|somewhat|slightly)\b/gi) || [];
    
    return {
        personalMarkers: [...new Set(personalMarkers.map(m => m.toLowerCase()))],
        hedgingLanguage: [...new Set(hedgingPhrases.map(h => h.toLowerCase()))],
        intensifiers: [...new Set(intensifiers.map(i => i.toLowerCase()))],
        personalityScore: personalMarkers.length / (text.split(/[.!?]+/).length || 1)
    };
}

/**
 * Identifies unique writing quirks and patterns
 */
function identifyWritingQuirks(text) {
    const quirks = [];
    
    // Check for repetitive patterns
    const words = text.toLowerCase().match(/\b[a-z]+\b/g) || [];
    const wordFreq = {};
    words.forEach(word => wordFreq[word] = (wordFreq[word] || 0) + 1);
    
    const overusedWords = Object.entries(wordFreq)
        .filter(([word, count]) => count > 3 && !isCommonWord(word))
        .map(([word]) => word);
    
    if (overusedWords.length > 0) {
        quirks.push({ type: 'word_repetition', words: overusedWords });
    }
    
    // Check for specific patterns
    if (text.includes('...')) quirks.push({ type: 'ellipsis_user' });
    if ((text.match(/--/g) || []).length > 0) quirks.push({ type: 'double_dash_user' });
    if ((text.match(/\([^)]*\)/g) || []).length > 2) quirks.push({ type: 'parenthetical_heavy' });
    
    return quirks;
}

/**
 * Analyzes overall tone and emotional patterns
 */
function analyzeTone(text) {
    const positiveWords = text.match(/\b(good|great|excellent|amazing|wonderful|fantastic|awesome|brilliant|perfect|love|like|enjoy|happy|pleased|satisfied|excited|thrilled)\b/gi) || [];
    const negativeWords = text.match(/\b(bad|terrible|awful|horrible|hate|dislike|annoying|frustrating|disappointed|upset|angry|sad|worried|concerned)\b/gi) || [];
    const neutralWords = text.match(/\b(okay|fine|alright|normal|standard|typical|usual|regular|average|moderate)\b/gi) || [];
    
    const totalEmotionalWords = positiveWords.length + negativeWords.length + neutralWords.length;
    
    return {
        sentiment: {
            positive: positiveWords.length / totalEmotionalWords || 0,
            negative: negativeWords.length / totalEmotionalWords || 0,
            neutral: neutralWords.length / totalEmotionalWords || 0
        },
        emotionalDensity: totalEmotionalWords / (text.split(/\s+/).length || 1),
        dominantTone: positiveWords.length > negativeWords.length ? 'positive' : 
                     negativeWords.length > positiveWords.length ? 'negative' : 'neutral'
    };
}

/**
 * Helper function to check if a word is common
 */
function isCommonWord(word) {
    const commonWords = new Set([
        'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on', 'with',
        'he', 'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her',
        'she', 'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what', 'so', 'up',
        'out', 'if', 'about', 'who', 'get', 'which', 'go', 'me', 'when', 'make', 'can', 'like', 'time',
        'no', 'just', 'him', 'know', 'take', 'people', 'into', 'year', 'your', 'good', 'some', 'could',
        'them', 'see', 'other', 'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over', 'think',
        'also', 'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first', 'well', 'way', 'even',
        'new', 'want', 'because', 'any', 'these', 'give', 'day', 'most', 'us'
    ]);
    return commonWords.has(word.toLowerCase());
}
