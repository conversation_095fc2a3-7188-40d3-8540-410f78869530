/**
 * Test script for quality fixes - specifically testing the problematic cases
 * Run with: node test-quality-fixes.js
 */

// Import the enhanced humanization functions
async function loadModules() {
    const balancedModule = await import('./src/utils/balancedHumanizer.js');
    const advancedModule = await import('./src/utils/advancedHumanizer.js');
    const contentModule = await import('./src/utils/contentAnalyzer.js');
    
    return {
        balancedHumanization: balancedModule.balancedHumanization,
        advancedHumanization: advancedModule.advancedHumanization,
        analyzeDocumentStructure: contentModule.analyzeDocumentStructure
    };
}

// Test cases that were problematic before the fixes
const problematicTestCases = [
    {
        name: "Formal Document with Headings and Line Breaks",
        text: `I. Introduction: The Power of Words in the Age of AI

Hook: Start with a compelling statistic or anecdote about AI-generated content.

Thesis: While AI can produce text efficiently, human creativity and emotional intelligence remain irreplaceable in crafting truly impactful communication.

II. The Rise of AI in Content Creation

A. Current capabilities of AI writing tools
B. Industries adopting AI-generated content
C. Benefits and limitations

III. The Human Element in Communication

A. Emotional intelligence in writing
B. Cultural context and nuance
C. Creative problem-solving

Conclusion: Summarize the complementary relationship between AI efficiency and human creativity.`
    },
    {
        name: "Technical Documentation with Section Numbers",
        text: `1. System Requirements

The application requires the following components:

1.1 Hardware Requirements
- Minimum 8GB RAM
- 2GB available disk space
- Network connectivity

1.2 Software Dependencies
- Node.js version 18.x or higher
- npm package manager
- Git version control

2. Installation Process

Step 1: Clone the repository
Step 2: Install dependencies
Step 3: Configure environment variables

3. Configuration

A. Database Setup
B. API Key Configuration
C. Security Settings`
    },
    {
        name: "Academic Paper Structure",
        text: `Abstract

This study examines the effectiveness of AI detection algorithms in identifying machine-generated text. The research methodology involved testing multiple AI detection tools against various text samples.

Introduction

Artificial intelligence has revolutionized content creation across multiple domains. However, the proliferation of AI-generated text has raised concerns about authenticity and academic integrity.

Methodology

The experimental design included three phases:
• Phase 1: Data collection
• Phase 2: Algorithm testing  
• Phase 3: Results analysis

Results

The findings demonstrate significant variations in detection accuracy across different AI detection tools.

Conclusion

These results suggest that current AI detection methods require further refinement to achieve reliable accuracy rates.`
    },
    {
        name: "Business Report with Bullet Points",
        text: `Executive Summary

This quarterly report analyzes market performance and strategic initiatives implemented during Q3 2024.

Key Findings:
• Revenue increased by 15% compared to Q2
• Customer acquisition costs decreased by 8%
• Market share expanded in three key regions

Strategic Recommendations:
1. Increase investment in digital marketing
2. Expand operations to emerging markets
3. Develop new product lines

Financial Overview

Q3 Performance Metrics:
- Total Revenue: $2.4M
- Operating Expenses: $1.8M
- Net Profit: $600K

Market Analysis

The competitive landscape shows continued growth opportunities in the technology sector.`
    }
];

async function testQualityFixes() {
    console.log('🔧 Testing Quality Fixes for Enhanced Humanization\n');
    console.log('=' .repeat(80));
    
    // Load the modules
    const { balancedHumanization, advancedHumanization, analyzeDocumentStructure } = await loadModules();
    
    for (const testCase of problematicTestCases) {
        console.log(`\n📋 Testing: ${testCase.name}`);
        console.log('-'.repeat(60));
        
        console.log('\n📄 Original Text:');
        console.log(testCase.text);
        
        // Analyze document structure
        console.log('\n🔍 Document Analysis:');
        const analysis = analyzeDocumentStructure(testCase.text);
        console.log(`- Has Headings: ${analysis.hasHeadings}`);
        console.log(`- Has Section Numbers: ${analysis.hasSectionNumbers}`);
        console.log(`- Has List Items: ${analysis.hasListItems}`);
        console.log(`- Has Formal Structure: ${analysis.hasFormalStructure}`);
        console.log(`- Preserve Formatting: ${analysis.preserveFormatting}`);
        console.log(`- Formal Elements: ${analysis.formalElements.length}`);
        
        // Test with different aggressiveness levels
        const aggressivenessLevels = [
            { level: 0.5, name: 'Conservative' },
            { level: 0.7, name: 'Balanced' },
            { level: 0.9, name: 'Aggressive' }
        ];
        
        for (const { level, name } of aggressivenessLevels) {
            console.log(`\n✨ Enhanced Humanization (${name} - ${level}):`)
            try {
                const result = balancedHumanization(testCase.text, null, 0, {
                    useAdvanced: true,
                    aggressiveness: level,
                    maintainTone: true
                });
                
                console.log(result);
                
                // Check for quality issues
                const qualityIssues = checkQualityIssues(testCase.text, result);
                if (qualityIssues.length > 0) {
                    console.log(`\n⚠️  Quality Issues Detected:`);
                    qualityIssues.forEach(issue => console.log(`   - ${issue}`));
                } else {
                    console.log(`\n✅ No quality issues detected`);
                }
                
            } catch (error) {
                console.error(`❌ Error with ${name} humanization:`, error.message);
            }
        }
        
        console.log('\n' + '='.repeat(80));
    }
    
    console.log('\n🎯 Quality Fix Validation Summary');
    console.log('-'.repeat(50));
    console.log('✅ Expected Improvements:');
    console.log('• Hesitation markers reduced to ≤5% frequency');
    console.log('• No inappropriate hesitations in headings/formal content');
    console.log('• Original formatting and line breaks preserved');
    console.log('• Document structure maintained');
    console.log('• Professional tone preserved');
    console.log('• AI detection bypass still effective (≤20% target)');
}

/**
 * Checks for specific quality issues in the output
 */
function checkQualityIssues(original, processed) {
    const issues = [];
    
    // Check for excessive hesitation markers
    const hesitationMarkers = ['listen,', 'look,', 'well,', 'so,', 'actually,'];
    let hesitationCount = 0;
    hesitationMarkers.forEach(marker => {
        const matches = processed.toLowerCase().match(new RegExp(`\\b${marker}`, 'g'));
        if (matches) hesitationCount += matches.length;
    });
    
    const sentences = processed.split(/(?<=[.!?])\s+/).length;
    const hesitationRatio = hesitationCount / sentences;
    
    if (hesitationRatio > 0.05) {
        issues.push(`Excessive hesitation markers: ${(hesitationRatio * 100).toFixed(1)}% (should be ≤5%)`);
    }
    
    // Check for inappropriate hesitations in formal content
    const inappropriateHesitations = [
        /\b(listen|look),\s*[IVX]+\./gi,  // Before roman numerals
        /\b(listen|look),\s*[A-Z]\./gi,   // Before letter markers
        /\b(listen|look),\s*\d+\./gi,     // Before number markers
        /\b(listen|look),\s*introduction:/gi,
        /\b(listen|look),\s*conclusion:/gi,
        /\b(listen|look),\s*abstract:/gi
    ];
    
    inappropriateHesitations.forEach((pattern, index) => {
        if (pattern.test(processed)) {
            issues.push(`Inappropriate hesitation in formal content (pattern ${index + 1})`);
        }
    });
    
    // Check for line break destruction
    const originalLines = original.split('\n').length;
    const processedLines = processed.split('\n').length;
    
    if (originalLines > 5 && processedLines < originalLines * 0.7) {
        issues.push(`Line break destruction: ${processedLines} lines vs ${originalLines} original`);
    }
    
    // Check for heading/section destruction
    const originalHeadings = (original.match(/^[IVX]+\.|^\d+\.|^[A-Z]\./gm) || []).length;
    const processedHeadings = (processed.match(/^[IVX]+\.|^\d+\.|^[A-Z]\./gm) || []).length;
    
    if (originalHeadings > 0 && processedHeadings < originalHeadings) {
        issues.push(`Section marker destruction: ${processedHeadings} vs ${originalHeadings} original`);
    }
    
    // Check for inappropriate modifications to formal markers
    const formalMarkers = ['Introduction:', 'Conclusion:', 'Abstract:', 'Summary:', 'Hook:', 'Thesis:'];
    formalMarkers.forEach(marker => {
        if (original.includes(marker)) {
            const modifiedMarker = processed.match(new RegExp(`\\b\\w+,\\s*${marker.toLowerCase()}`, 'i'));
            if (modifiedMarker) {
                issues.push(`Inappropriate modification of formal marker: ${marker}`);
            }
        }
    });
    
    return issues;
}

// Run the test
if (require.main === module) {
    testQualityFixes().catch(console.error);
}

module.exports = { testQualityFixes };
