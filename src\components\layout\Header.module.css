.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--secondary-200);
  position: sticky;
  top: 0;
  z-index: 50;
  transition: all var(--transition-normal);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-weight: 600;
  font-size: 1.25rem;
  color: var(--secondary-900);
  text-decoration: none;
  transition: transform var(--transition-fast);
}

.logo:hover {
  transform: scale(1.02);
  text-decoration: none;
}

.logoIcon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logoText {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Desktop Navigation */
.desktopNav {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.navLink {
  font-weight: 500;
  color: var(--secondary-700);
  text-decoration: none;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  position: relative;
}

.navLink:hover {
  color: var(--primary-600);
  background: var(--primary-50);
  text-decoration: none;
}

.navLink::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--primary-600);
  transition: all var(--transition-fast);
  transform: translateX(-50%);
}

.navLink:hover::after {
  width: 80%;
}

/* User Actions */
.userActions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.loadingSpinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--secondary-200);
  border-top: 2px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* User Menu */
.userMenu {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.userInfo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.userAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid var(--primary-200);
  object-fit: cover;
}

.userDetails {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.userName {
  font-weight: 500;
  color: var(--secondary-900);
  font-size: 0.875rem;
}

.userTier {
  font-size: 0.75rem;
  color: var(--primary-600);
  background: var(--primary-50);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-weight: 500;
}

/* Auth Buttons */
.authButtons {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.signInButton {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: white;
  color: var(--secondary-700);
  border: 1px solid var(--secondary-300);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.signInButton:hover {
  background: var(--secondary-50);
  border-color: var(--secondary-400);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.googleIcon {
  width: 20px;
  height: 20px;
}

.signOutButton {
  background: var(--error-500);
  color: white;
  border: none;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.signOutButton:hover {
  background: var(--error-600);
}

/* Mobile Menu Button */
.mobileMenuButton {
  display: none;
  background: none;
  border: none;
  padding: var(--space-2);
  cursor: pointer;
}

.hamburger {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  position: relative;
}

.hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background: var(--secondary-700);
  border-radius: 1px;
  transition: all var(--transition-fast);
  position: absolute;
}

.hamburger span:nth-child(1) {
  top: 0;
}

.hamburger span:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}

.hamburger span:nth-child(3) {
  bottom: 0;
}

.hamburger.open span:nth-child(1) {
  transform: rotate(45deg);
  top: 50%;
}

.hamburger.open span:nth-child(2) {
  opacity: 0;
}

.hamburger.open span:nth-child(3) {
  transform: rotate(-45deg);
  bottom: 50%;
}

/* Mobile Navigation */
.mobileNav {
  display: none;
  flex-direction: column;
  gap: var(--space-2);
  padding: var(--space-4) 0;
  border-top: 1px solid var(--secondary-200);
  background: white;
}

.mobileNav.open {
  display: flex;
}

.mobileNavLink {
  padding: var(--space-3) var(--space-4);
  color: var(--secondary-700);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-weight: 500;
}

.mobileNavLink:hover {
  background: var(--primary-50);
  color: var(--primary-600);
  text-decoration: none;
}

.mobileSignInButton {
  margin: var(--space-2) var(--space-4);
  background: var(--primary-600);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .desktopNav {
    display: none;
  }
  
  .mobileMenuButton {
    display: block;
  }
  
  .userDetails {
    display: none;
  }
  
  .signInButton {
    display: none;
  }
}
