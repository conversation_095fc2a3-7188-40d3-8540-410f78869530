// src/services/gptzeroClient.js

/**
 * Checks the given document text with the GPTZero API.
 * @param {string} documentText - The text to be analyzed.
 * @returns {Promise<object>} A promise that resolves to the API response or an error object.
 */
export async function checkWithGPTZero(documentText) {
    const apiKey = process.env.GPTZERO_API_KEY;
    // According to documentation, the latest endpoint for text prediction is:
    // https://api.gptzero.me/v2/predict/text
    // For files: https://api.gptzero.me/v2/predict/files (would require different handling)
    const apiUrl = 'https://api.gptzero.me/v2/predict/text';

    if (!apiKey) {
        console.warn('GPTZero API key is missing. AI detection will be skipped.');
        return {
            error: false,
            message: 'AI detection skipped - API key not configured',
            status: "Skipped",
            score: null,
            details: {
                note: "Text processing completed successfully. AI detection requires GPTZero API key configuration."
            }
        };
    }

    if (!documentText || typeof documentText !== 'string' || !documentText.trim()) {
        return {
            error: true,
            message: 'Input text cannot be empty.',
            status: "Error",
            score: null
        };
    }

    try {
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Api-Key': apiKey, // Official header name from GPTZero docs
                'Accept': 'application/json',
            },
            body: JSON.stringify({ document: documentText }),
        });

        if (!response.ok) {
            let errorData;
            try {
                errorData = await response.json();
            } catch (e) {
                errorData = { message: `API Error: ${response.status} ${response.statusText}. Could not parse error response.` };
            }
            console.error('GPTZero API Error:', response.status, errorData);
            return {
                error: true,
                message: errorData.message || `GPTZero API request failed with status ${response.status}.`,
                status: "API Error",
                score: null,
                details: errorData
            };
        }

        const data = await response.json();

        // Extracting key information based on typical GPTZero v2 response structure.
        // The actual structure might vary slightly or have more details.
        // `documents` is an array, typically with one entry for a single text submission.
        const mainDocument = data?.documents?.[0];

        if (!mainDocument) {
            console.error('GPTZero API Error: Response structure does not contain expected document data.', data);
            return {
                error: true,
                message: 'Failed to parse GPTZero response: Unexpected data structure.',
                status: "API Error",
                score: null,
                rawResponse: data // Include raw response for debugging
            };
        }

        return {
            error: false,
            status: mainDocument.class || "N/A", // e.g., "HUMAN_WRITTEN", "AI_GENERATED", "MIXED"
            score: mainDocument.completely_generated_prob, // Probability of being AI generated
            average_generated_prob: mainDocument.average_generated_prob,
            overall_burstiness: mainDocument.overall_burstiness,
            sentences: mainDocument.sentences?.map(s => ({
                sentence: s.sentence,
                ai_prob: s.generated_sentence_prob,
                quality: s.quality,
            })),
            details: "Successfully processed by GPTZero.", // Or could pass the full `mainDocument` or `data`
            rawResponse: data // Keep raw response for potential further use or debugging
        };

    } catch (error) {
        console.error('Error calling GPTZero API:', error);
        return {
            error: true,
            message: error.message || 'An unexpected error occurred while contacting GPTZero.',
            status: "Network Error",
            score: null
        };
    }
}
