// Test script to verify paragraph formatting is preserved
const testText = `This is the first paragraph of the test. It contains multiple sentences to verify that the humanization process works correctly. Furthermore, it demonstrates the typical AI writing patterns that need to be humanized.

This is the second paragraph. Additionally, it shows how the system should handle paragraph breaks. Moreover, it tests whether the formatting is preserved during the text processing pipeline.

This is the third and final paragraph. It is important to note that this paragraph should remain separate from the others. In conclusion, the paragraph structure must be maintained throughout the entire humanization process.`;

async function testParagraphFormatting() {
    try {
        console.log('🧪 Testing Paragraph Formatting Preservation...');
        console.log('📝 Original text:');
        console.log(testText);
        console.log('\n⏳ Processing...\n');

        const response = await fetch('http://localhost:3003/api/process/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ text: testText }),
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        
        console.log('✅ API Response received!');
        console.log('🔄 Humanized text:');
        console.log(result.modifiedText);
        
        // Check if paragraph breaks are preserved
        const originalParagraphs = testText.split(/\n\s*\n/).length;
        const resultParagraphs = result.modifiedText.split(/\n\s*\n/).length;
        
        console.log('\n📊 Paragraph Analysis:');
        console.log(`Original paragraphs: ${originalParagraphs}`);
        console.log(`Result paragraphs: ${resultParagraphs}`);
        
        if (originalParagraphs === resultParagraphs) {
            console.log('🎉 SUCCESS: Paragraph structure preserved!');
        } else {
            console.log('❌ FAILURE: Paragraph structure was not preserved!');
        }
        
        // Check for text modifications
        if (result.modifiedText !== testText) {
            console.log('✅ Text was successfully humanized with modifications');
        } else {
            console.log('⚠️  Text was not modified (may be normal for some inputs)');
        }

    } catch (error) {
        console.error('❌ ERROR:', error.message);
    }
}

// Run the test
testParagraphFormatting();
