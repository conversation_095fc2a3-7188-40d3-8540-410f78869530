// src/components/ads/AdBanner.js
import React, { useEffect, useRef, memo } from 'react';
import styles from './AdBanner.module.css';

const AdBanner = ({
    className,
    style,
    // --- AdSense Specific Props ---
    dataAdClient, // Your AdSense Publisher ID, e.g., "ca-pub-XXXXXXXXXXXXXXXX"
    dataAdSlot,   // The ID of the specific ad unit from AdSense
    // Optional AdSense props (though often 'auto' and 'true' are good defaults)
    dataAdFormat = "auto",
    dataFullWidthResponsive = "true",

    // --- Generic Ad Block Props ---
    // Use this if your ad provider gives a single block of HTML/JS
    adHtmlBlock,

    // --- Fallback Content ---
    // Optional: Custom content to show if no ad props are provided (mainly for dev placeholder)
    placeholderText,
}) => {
    const adContainerRef = useRef(null);
    const adPushedRef = useRef(false); // Ref to track if adsbygoogle.push has been called for this instance

    useEffect(() => {
        // This effect attempts to re-initialize ads if the ad library (like AdSense)
        // dynamically scans the page or needs a push after content changes.
        // This is a common pattern for AdSense.
        // We only try to push if an AdSense ad is likely configured (dataAdSlot exists).
        if (dataAdSlot && !adPushedRef.current) { // Check if push hasn't been called for this slot yet
            try {
                if (typeof window !== 'undefined' && window.adsbygoogle) {
                    console.log('AdSense: Pushing ads for slot:', dataAdSlot);
                    (window.adsbygoogle = window.adsbygoogle || []).push({});
                    adPushedRef.current = true; // Mark as pushed
                } else {
                    // console.warn('AdSense script (adsbygoogle) not found when component mounted for slot:', dataAdSlot);
                }
            } catch (e) {
                console.error('AdSense: Error pushing to adsbygoogle for slot:', dataAdSlot, e);
            }
        }
        // Dependency array: re-run if dataAdSlot changes (though usually it's fixed per component instance).
        // Adding a cleanup function might be necessary if ads need to be destroyed,
        // but for AdSense, it's often not required for simple banner placements.
    }, [dataAdSlot]);

    let adContentToRender;

    if (dataAdClient && dataAdSlot) { // AdSense specific structure
        adContentToRender = (
            <ins className="adsbygoogle" // AdSense requires this class
                 style={{ display: 'block', textAlign: 'center', ...style }} // Recommended AdSense styles
                 data-ad-client={dataAdClient}
                 data-ad-slot={dataAdSlot}
                 data-ad-format={dataAdFormat}
                 data-full-width-responsive={dataFullWidthResponsive}></ins>
        );
    } else if (adHtmlBlock) { // Generic HTML block
        // Using dangerouslySetInnerHTML requires trusting the adHtmlBlock content.
        // Ad provider snippets are generally trusted, but be mindful.
        adContentToRender = <div dangerouslySetInnerHTML={{ __html: adHtmlBlock }} />;
    } else {
        // Fallback or placeholder if no ad content is provided
        if (process.env.NODE_ENV === 'development') {
            return (
                <div className={`${styles.adBanner} ${styles.adPlaceholder} ${className || ''}`} style={style}>
                    <p>{placeholderText || `Ad Placeholder (Slot: ${dataAdSlot || 'N/A - Generic'})`}</p>
                </div>
            );
        }
        return null; // Don't render anything in production if not configured
    }

    return (
        <div
            ref={adContainerRef}
            className={`${styles.adBanner} ${className || ''}`}
            // Keying the div by adSlotId or a combination of props can help React
            // re-mount the component if the ad unit changes, triggering useEffect correctly.
            // This is more relevant if the same AdBanner instance is re-used with different ad slots.
            key={dataAdSlot || (typeof adHtmlBlock === 'string' ? adHtmlBlock.substring(0,30) : 'adbanner')}
        >
            {adContentToRender}
        </div>
    );
};

// Memoize the component to prevent unnecessary re-renders if props haven't changed,
// which can be important for performance and to avoid disrupting ad scripts.
export default memo(AdBanner);
