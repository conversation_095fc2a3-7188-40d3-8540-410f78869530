/**
 * Test for paragraph break preservation
 */

import { advancedHumanization } from './src/utils/advancedHumanizer.js';

const testText = `This is very important content that shows significant benefits for organizations.

The implementation of artificial intelligence systems requires careful planning and substantial investment. Many businesses are finding that the results justify the costs.

This technology is also beneficial and provides good advantages for modern companies.`;

// Test with higher aggressiveness to force changes
console.log('=== TESTING WITH AGGRESSIVE HUMANIZATION ===');
const aggressiveResult = advancedHumanization(testText, { aggressiveness: 0.9 });
console.log('Aggressive result:');
console.log(aggressiveResult);
console.log('Aggressive paragraph breaks preserved:', testText.split('\n\n').length === aggressiveResult.split('\n\n').length ? '✅' : '❌');

console.log('=== PARAGRAPH BREAK TEST ===');
console.log('Original:');
console.log(JSON.stringify(testText));
console.log('\nOriginal (formatted):');
console.log(testText);

const result = advancedHumanization(testText, { aggressiveness: 0.7 });

console.log('\nHumanized:');
console.log(JSON.stringify(result));
console.log('\nHumanized (formatted):');
console.log(result);

console.log('\n=== DETAILED ANALYSIS ===');
console.log('Original line count:', testText.split('\n').length);
console.log('Humanized line count:', result.split('\n').length);
console.log('Line breaks preserved:', testText.split('\n').length === result.split('\n').length ? '✅' : '❌');

// Check for paragraph breaks specifically
const originalParagraphs = testText.split('\n\n');
const humanizedParagraphs = result.split('\n\n');
console.log('Original paragraphs:', originalParagraphs.length);
console.log('Humanized paragraphs:', humanizedParagraphs.length);
console.log('Paragraph breaks preserved:', originalParagraphs.length === humanizedParagraphs.length ? '✅' : '❌');

// Show each line individually
console.log('\n=== LINE BY LINE COMPARISON ===');
const originalLines = testText.split('\n');
const humanizedLines = result.split('\n');

console.log('Original lines:');
originalLines.forEach((line, i) => {
    console.log(`  ${i}: "${line}"`);
});

console.log('\nHumanized lines:');
humanizedLines.forEach((line, i) => {
    console.log(`  ${i}: "${line}"`);
});
