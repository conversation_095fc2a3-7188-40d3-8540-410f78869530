/**
 * Enhanced Humanization Test Suite
 * Tests the improved TextHumanizer integration with contextual hesitation markers
 * and enhanced protection patterns
 */

import { advancedHumanization } from './src/utils/advancedHumanizer.js';
import { checkWithGPTZero } from './src/services/gptzeroClient.js';

// Test cases covering different document types and structures
const testCases = [
    {
        name: "Formal Document with Structure",
        text: `I. Introduction: The Power of Words in the Age of AI

Hook: Start with a compelling statistic or quote about AI's growing influence on content creation.

II. The Rise of AI Detection

A. Current landscape of AI detection tools
B. Why businesses and institutions are concerned

III. Understanding the Challenge

The challenge is significant. AI-generated content often exhibits patterns that detection tools can identify. These patterns include consistent sentence structure, predictable word choices, and lack of natural human hesitation or variation.

IV. Strategic Solutions

A. Content diversification approaches
B. Natural language enhancement techniques
C. Quality assurance protocols`,
        expectedBehavior: "Should preserve all headings, section numbers, and formal structure while applying subtle humanization to paragraph content only"
    },
    {
        name: "Technical Content",
        text: `API Integration Guide

This document explains how to integrate with our REST API endpoints. The API uses JSON for data exchange and supports HTTP methods including GET, POST, PUT, and DELETE.

Authentication is handled via OAuth 2.0 tokens. Each request must include a valid bearer token in the Authorization header.

Rate limiting is enforced at 1000 requests per hour per API key.`,
        expectedBehavior: "Should protect technical terms, API references, and formal documentation structure"
    },
    {
        name: "Regular Content",
        text: `The benefits of artificial intelligence in modern business are very significant. Companies are utilizing AI to improve their operations and enhance customer experiences. This technology shows great promise for the future.

However, there are also challenges that organizations must consider. The implementation of AI systems requires careful planning and substantial investment. Many businesses are finding that the results justify the costs.`,
        expectedBehavior: "Should apply contextual hesitation markers, word variations, and structural changes while maintaining professional tone"
    },
    {
        name: "Mixed Content",
        text: `Executive Summary: AI Implementation Strategy

1. Current State Analysis
   - Existing technology infrastructure
   - Staff capabilities and training needs
   - Budget considerations

The analysis reveals that our organization is well-positioned to implement AI solutions. The current infrastructure provides a solid foundation for integration. Staff members have expressed enthusiasm for learning new technologies.

2. Recommended Approach
   - Phase 1: Pilot program implementation
   - Phase 2: Gradual rollout across departments
   - Phase 3: Full integration and optimization

This phased approach will minimize disruption while maximizing the benefits of AI adoption.`,
        expectedBehavior: "Should protect formal headings and numbered lists while humanizing paragraph content"
    }
];

/**
 * Run comprehensive tests on the enhanced humanization system
 */
async function runEnhancedHumanizationTests() {
    console.log('🚀 Starting Enhanced Humanization Test Suite\n');
    
    const results = [];
    
    for (const testCase of testCases) {
        console.log(`📝 Testing: ${testCase.name}`);
        console.log(`Expected: ${testCase.expectedBehavior}\n`);
        
        try {
            // Test with different aggressiveness levels
            const testResults = await testMultipleSettings(testCase);
            results.push({
                name: testCase.name,
                results: testResults
            });
            
        } catch (error) {
            console.error(`❌ Error testing ${testCase.name}:`, error.message);
            results.push({
                name: testCase.name,
                error: error.message
            });
        }
        
        console.log('─'.repeat(80) + '\n');
    }
    
    // Generate summary report
    generateSummaryReport(results);
}

/**
 * Test humanization with multiple settings
 */
async function testMultipleSettings(testCase) {
    const settings = [
        { aggressiveness: 0.5, label: "Conservative" },
        { aggressiveness: 0.7, label: "Balanced" },
        { aggressiveness: 0.9, label: "Aggressive" }
    ];
    
    const results = [];
    
    for (const setting of settings) {
        console.log(`  🔧 Testing ${setting.label} (${setting.aggressiveness})...`);
        
        const startTime = Date.now();
        const humanizedText = advancedHumanization(testCase.text, {
            aggressiveness: setting.aggressiveness,
            maintainTone: true
        });
        const processingTime = Date.now() - startTime;
        
        // Analyze the results
        const analysis = analyzeHumanizationQuality(testCase.text, humanizedText);
        
        // Test AI detection if GPTZero is available
        let detectionScore = null;
        try {
            const detectionResult = await checkWithGPTZero(humanizedText);
            detectionScore = detectionResult.ai_probability || null;
        } catch (error) {
            console.log(`    ⚠️  AI detection test skipped: ${error.message}`);
        }
        
        const result = {
            setting: setting.label,
            aggressiveness: setting.aggressiveness,
            processingTime,
            analysis,
            detectionScore,
            originalLength: testCase.text.length,
            humanizedLength: humanizedText.length,
            lengthChange: ((humanizedText.length - testCase.text.length) / testCase.text.length * 100).toFixed(1)
        };
        
        results.push(result);
        
        // Display results
        console.log(`    ✅ Processing time: ${processingTime}ms`);
        console.log(`    📊 Length change: ${result.lengthChange}%`);
        console.log(`    🎯 Hesitation markers: ${analysis.hesitationCount}`);
        console.log(`    🛡️  Protected elements: ${analysis.protectedElements}`);
        if (detectionScore !== null) {
            console.log(`    🤖 AI detection: ${(detectionScore * 100).toFixed(1)}%`);
        }
        console.log(`    📝 Sample output: "${humanizedText.substring(0, 100)}..."\n`);
    }
    
    return results;
}

/**
 * Analyze the quality of humanization
 */
function analyzeHumanizationQuality(original, humanized) {
    const analysis = {
        hesitationCount: 0,
        protectedElements: 0,
        wordVariations: 0,
        structuralChanges: 0,
        preservedFormatting: true
    };
    
    // Count hesitation markers
    const hesitationMarkers = [
        'actually,', 'listen,', 'well,', 'you know,', 'i mean,',
        'to be honest,', 'frankly,', 'look,', 'honestly,', 'so,',
        'however,', 'meanwhile,', 'furthermore,', 'additionally,',
        'notably,', 'importantly,', 'significantly,', 'remarkably,'
    ];
    
    hesitationMarkers.forEach(marker => {
        const regex = new RegExp(`\\b${marker}`, 'gi');
        const matches = humanized.match(regex);
        if (matches) {
            analysis.hesitationCount += matches.length;
        }
    });
    
    // Check for protected elements preservation
    const protectedPatterns = [
        /^[IVX]+\./gm,
        /^[A-Z]\./gm,
        /^\d+\./gm,
        /^#+\s/gm,
        /^\s*[-•*]\s/gm
    ];
    
    protectedPatterns.forEach(pattern => {
        const originalMatches = original.match(pattern) || [];
        const humanizedMatches = humanized.match(pattern) || [];
        if (originalMatches.length === humanizedMatches.length) {
            analysis.protectedElements += originalMatches.length;
        }
    });
    
    // Check for word variations
    const commonWords = ['very', 'also', 'but', 'because', 'shows', 'important'];
    commonWords.forEach(word => {
        const originalCount = (original.match(new RegExp(`\\b${word}\\b`, 'gi')) || []).length;
        const humanizedCount = (humanized.match(new RegExp(`\\b${word}\\b`, 'gi')) || []).length;
        if (humanizedCount < originalCount) {
            analysis.wordVariations += (originalCount - humanizedCount);
        }
    });
    
    return analysis;
}

/**
 * Generate a comprehensive summary report
 */
function generateSummaryReport(results) {
    console.log('📊 ENHANCED HUMANIZATION TEST SUMMARY');
    console.log('═'.repeat(80));
    
    let totalTests = 0;
    let successfulTests = 0;
    let avgDetectionScore = 0;
    let detectionTests = 0;
    
    results.forEach(result => {
        console.log(`\n📋 ${result.name}:`);
        
        if (result.error) {
            console.log(`   ❌ Error: ${result.error}`);
            return;
        }
        
        result.results.forEach(test => {
            totalTests++;
            successfulTests++;
            
            console.log(`   ${test.setting}: ${test.processingTime}ms, ${test.lengthChange}% length change`);
            console.log(`     Hesitation: ${test.analysis.hesitationCount}, Protected: ${test.analysis.protectedElements}`);
            
            if (test.detectionScore !== null) {
                const score = test.detectionScore * 100;
                console.log(`     AI Detection: ${score.toFixed(1)}% ${score <= 20 ? '✅' : '⚠️'}`);
                avgDetectionScore += score;
                detectionTests++;
            }
        });
    });
    
    console.log('\n🎯 OVERALL RESULTS:');
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Successful: ${successfulTests}`);
    console.log(`   Success Rate: ${((successfulTests / totalTests) * 100).toFixed(1)}%`);
    
    if (detectionTests > 0) {
        const avgScore = avgDetectionScore / detectionTests;
        console.log(`   Average AI Detection: ${avgScore.toFixed(1)}%`);
        console.log(`   Target Achievement: ${avgScore <= 20 ? '✅ PASSED' : '❌ NEEDS IMPROVEMENT'}`);
    }
    
    console.log('\n✨ Enhanced humanization testing completed!');
}

// Run the tests
if (import.meta.url === `file://${process.argv[1]}`) {
    runEnhancedHumanizationTests().catch(console.error);
}

export { runEnhancedHumanizationTests, analyzeHumanizationQuality };
