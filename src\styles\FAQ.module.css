.faqSection {
  padding: 4rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: #1e293b;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.faqList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.faqItem {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.faqItem:hover {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.faqQuestion {
  width: 100%;
  padding: 1.5rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.faqQuestion:hover {
  background: #f8fafc;
}

.faqQuestion.active {
  background: #f1f5f9;
  color: #6366f1;
}

.icon {
  font-size: 1.5rem;
  font-weight: 300;
  color: #6366f1;
  transition: transform 0.3s ease;
}

.faqQuestion.active .icon {
  transform: rotate(180deg);
}

.faqAnswer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faqAnswer.open {
  max-height: 500px;
}

.answerContent {
  padding: 0 1.5rem 1.5rem;
  color: #475569;
  line-height: 1.7;
}

.answerContent p {
  margin: 0 0 1rem 0;
}

.answerContent p:last-child {
  margin-bottom: 0;
}

.answerContent ul {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.answerContent li {
  margin-bottom: 0.5rem;
}

.answerContent strong {
  color: #1e293b;
  font-weight: 600;
}

.answerContent code {
  background: #f1f5f9;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.9rem;
  color: #6366f1;
}

@media (max-width: 768px) {
  .title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
  
  .faqQuestion {
    padding: 1rem;
    font-size: 1rem;
  }
  
  .answerContent {
    padding: 0 1rem 1rem;
  }
}
