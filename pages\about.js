import React from 'react';
import Head from 'next/head';
import Layout from '../src/components/layout/Layout';
import SEOHead from '../src/components/seo/SEOHead';
import InternalLinks from '../src/components/seo/InternalLinks';
import { getOrganizationSchema, getArticleSchema } from '../src/utils/structuredData';
import styles from '../src/styles/About.module.css';

export default function About() {
    return (
        <>
            <SEOHead
                title="About GhostLayer | Leading AI Text Humanization Technology"
                description="Learn about Ghost<PERSON><PERSON><PERSON>'s mission to revolutionize AI content creation. Our advanced algorithms make AI-generated text undetectable while preserving quality and meaning."
                keywords="about Ghost<PERSON>ayer, AI text humanization company, AI detection bypass technology, undetectable AI content, AI writing tools company"
                canonicalUrl="/about"
                structuredData={[
                    getOrganizationSchema(),
                    getArticleSchema(
                        "About GhostLayer | Leading AI Text Humanization Technology",
                        "Learn about Ghost<PERSON><PERSON><PERSON>'s mission to revolutionize AI content creation",
                        "2024-01-01",
                        new Date().toISOString().split('T')[0]
                    )
                ]}
            />

            <Layout>
                <div className={styles.container}>
                    <header className={styles.header}>
                        <h1 className={styles.title}>About GhostLayer</h1>
                        <p className={styles.subtitle}>
                            Making AI-generated content indistinguishable from human writing
                        </p>
                    </header>

                    <section className={styles.missionSection}>
                        <div className={styles.content}>
                            <h2 className={styles.sectionTitle}>Our Mission</h2>
                            <p className={styles.text}>
                                GhostLayer was created to bridge the gap between AI-generated content and natural human writing. 
                                As AI tools become more prevalent, we recognized the need for a solution that could transform 
                                robotic, detectable AI text into natural, engaging content that resonates with human readers.
                            </p>
                            <p className={styles.text}>
                                Our advanced algorithms analyze text patterns, sentence structures, and linguistic nuances to 
                                create content that not only bypasses AI detection tools but also improves readability and 
                                maintains the original meaning and intent.
                            </p>
                        </div>
                    </section>

                    <section className={styles.valuesSection}>
                        <h2 className={styles.sectionTitle}>Our Values</h2>
                        <div className={styles.valuesGrid}>
                            <div className={styles.valueCard}>
                                <div className={styles.valueIcon}>🎯</div>
                                <h3 className={styles.valueTitle}>Quality First</h3>
                                <p className={styles.valueDescription}>
                                    We prioritize content quality and meaning preservation above all else.
                                </p>
                            </div>
                            <div className={styles.valueCard}>
                                <div className={styles.valueIcon}>🔒</div>
                                <h3 className={styles.valueTitle}>Privacy Focused</h3>
                                <p className={styles.valueDescription}>
                                    Your content is never stored or shared. Complete privacy guaranteed.
                                </p>
                            </div>
                            <div className={styles.valueCard}>
                                <div className={styles.valueIcon}>⚡</div>
                                <h3 className={styles.valueTitle}>Speed & Efficiency</h3>
                                <p className={styles.valueDescription}>
                                    Fast processing without compromising on quality or accuracy.
                                </p>
                            </div>
                            <div className={styles.valueCard}>
                                <div className={styles.valueIcon}>🌟</div>
                                <h3 className={styles.valueTitle}>Innovation</h3>
                                <p className={styles.valueDescription}>
                                    Continuously improving our algorithms to stay ahead of detection tools.
                                </p>
                            </div>
                        </div>
                    </section>

                    <section className={styles.teamSection}>
                        <h2 className={styles.sectionTitle}>Our Team</h2>
                        <div className={styles.teamContent}>
                            <p className={styles.text}>
                                GhostLayer is developed by a team of AI researchers, linguists, and software engineers 
                                passionate about natural language processing and content creation. Our diverse backgrounds 
                                in machine learning, computational linguistics, and user experience design enable us to 
                                create tools that are both powerful and user-friendly.
                            </p>
                            <p className={styles.text}>
                                We&apos;re committed to ethical AI use and believe that technology should enhance human
                                creativity rather than replace it. GhostLayer empowers content creators to leverage 
                                AI assistance while maintaining authenticity and originality.
                            </p>
                        </div>
                    </section>

                    <section className={styles.contactSection}>
                        <h2 className={styles.sectionTitle}>Get in Touch</h2>
                        <p className={styles.text}>
                            Have questions, feedback, or suggestions? We&apos;d love to hear from you.
                        </p>
                        <div className={styles.contactInfo}>
                            <div className={styles.contactItem}>
                                <strong>Email:</strong> <EMAIL>
                            </div>
                            <div className={styles.contactItem}>
                                <strong>GitHub:</strong> 
                                <a href="https://github.com/HectorTa1989/stealthwriter-ai" target="_blank" rel="noopener noreferrer">
                                    HectorTa1989/stealthwriter-ai
                                </a>
                            </div>
                        </div>
                    </section>

                    {/* Internal Links for SEO */}
                    <InternalLinks currentPage="about" />
                </div>
            </Layout>
        </>
    );
}
