.internalLinks {
  background: #f8fafc;
  padding: 3rem 0;
  margin-top: 3rem;
}

.sectionTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 2rem;
  text-align: center;
}

/* Main Navigation Links */
.mainLinks {
  max-width: 1200px;
  margin: 0 auto 4rem auto;
  padding: 0 1rem;
}

.linkGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.mainLink {
  display: block;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-decoration: none;
  color: inherit;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mainLink:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.mainLink::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.linkContent {
  position: relative;
}

.linkTitle {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.linkDescription {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.linkKeywords {
  font-size: 0.8rem;
  color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  display: inline-block;
  margin-bottom: 1rem;
}

.linkArrow {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  color: #6366f1;
  transition: transform 0.3s ease;
}

.mainLink:hover .linkArrow {
  transform: translateX(5px);
}

/* Related Topics */
.relatedTopics {
  max-width: 1200px;
  margin: 0 auto 4rem auto;
  padding: 0 1rem;
}

.topicsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.topicGroup {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.topicTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.topicLinks {
  list-style: none;
  padding: 0;
  margin: 0;
}

.topicLinks li {
  margin-bottom: 0.5rem;
}

.topicLink {
  color: #6366f1;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  display: block;
  padding: 0.25rem 0;
}

.topicLink:hover {
  color: #4f46e5;
  text-decoration: underline;
}

/* Breadcrumbs */
.breadcrumbs {
  max-width: 1200px;
  margin: 0 auto 3rem auto;
  padding: 0 1rem;
}

.breadcrumbNav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #64748b;
}

.breadcrumbLink {
  color: #6366f1;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumbLink:hover {
  color: #4f46e5;
  text-decoration: underline;
}

.breadcrumbSeparator {
  color: #cbd5e1;
}

.breadcrumbCurrent {
  color: #1e293b;
  font-weight: 500;
}

/* Footer Links */
.footerLinks {
  background: #1e293b;
  color: white;
  padding: 3rem 0;
}

.footerGrid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.footerColumn {
  
}

.footerTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #334155;
}

.footerList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footerList li {
  margin-bottom: 0.5rem;
}

.footerLink {
  color: #cbd5e1;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
  display: block;
  padding: 0.25rem 0;
}

.footerLink:hover {
  color: white;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .internalLinks {
    padding: 2rem 0;
  }
  
  .linkGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .mainLink {
    padding: 1.5rem;
  }
  
  .linkTitle {
    font-size: 1.1rem;
  }
  
  .linkDescription {
    font-size: 0.9rem;
  }
  
  .topicsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .topicGroup {
    padding: 1rem;
  }
  
  .footerGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .sectionTitle {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .mainLink {
    padding: 1rem;
  }
  
  .linkArrow {
    position: static;
    margin-top: 1rem;
    display: block;
  }
  
  .breadcrumbNav {
    font-size: 0.8rem;
  }
  
  .footerLinks {
    padding: 2rem 0;
  }
}
