# PEGASUS Paraphrasing Service Integration Guide

## 1. Overview

This guide details the integration of the `tuner007/pegasus_paraphrase` model, which provides advanced paraphrasing capabilities for the AI Text Modifier application.

The PEGASUS model runs as a separate Python Flask microservice. The main Next.js backend application communicates with this Python service via HTTP requests to get paraphrased text. This separation allows for dedicated management of the Python environment and the resource-intensive machine learning model.

## 2. Python Paraphrasing Service (`python_services/paraphrase_pegasus/app.py`)

This service exposes an API endpoint that accepts text and returns its paraphrased version using the PEGASUS model.

```python
from flask import Flask, request, jsonify
from transformers import PegasusForConditionalGeneration, PegasusTokenizer
import torch
import os

# Initialize Flask App
app = Flask(__name__)

# --- Model Loading ---
MODEL_NAME = 'tuner007/pegasus_paraphrase'
tokenizer = None
model = None
device = None  # Will be set to 'cuda' or 'cpu'

def load_model():
    global tokenizer, model, device
    try:
        print(f"Loading tokenizer for {MODEL_NAME}...")
        # Ensure sentencepiece is installed if not already: pip install sentencepiece
        tokenizer = PegasusTokenizer.from_pretrained(MODEL_NAME)
        print(f"Loading model {MODEL_NAME}...")
        model = PegasusForConditionalGeneration.from_pretrained(MODEL_NAME)

        if torch.cuda.is_available():
            device = torch.device("cuda")
            model.to(device)
            print("Model moved to GPU.")
        else:
            device = torch.device("cpu")
            model.to(device)  # Explicitly move to CPU if not already
            print("Model using CPU.")

        model.eval()  # Set to evaluation mode
        print("Model and tokenizer loaded successfully.")
    except Exception as e:
        print(f"Error loading model or tokenizer: {e}")
        # Allow app to start, but endpoint will fail.
        # For a critical model, you might want to raise an error to prevent app start:
        # raise RuntimeError(f"Failed to load model: {e}") from e

# Call load_model() when the application starts.
# This ensures the model is loaded once, not on every request.
load_model()

# --- API Endpoint ---
@app.route('/paraphrase', methods=['POST'])
def paraphrase_text_endpoint(): # Renamed to avoid conflict with any potential 'paraphrase_text' utility function
    if not model or not tokenizer:  # Check if model loading failed
        return jsonify({"error": "Model not loaded. Check server logs."}), 500

    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Invalid JSON payload"}), 400

        input_text = data.get('text')

        if not input_text:
            return jsonify({"error": "No text provided in JSON payload (expected key 'text')"}), 400

        if not isinstance(input_text, str):
            return jsonify({"error": "Field 'text' must be a string"}), 400

        # Prepare the text for the model
        # Pegasus expects a list of strings for batching, even if it's just one.
        batch = tokenizer(
            [input_text],
            truncation=True,      # Truncate to max model length if input is too long
            padding='longest',    # Pad to the longest sequence in the batch (or max_length)
            max_length=128,       # Max input length for Pegasus for this task (can be tuned)
            return_tensors="pt"   # Return PyTorch tensors
        ).to(device)              # Move batch to the same device as the model

        # Generate paraphrased text
        # Adjust generation parameters as needed for desired output
        generated_ids = model.generate(
            **batch,
            max_length=128,          # Max length of the generated paraphrase
            num_beams=5,             # Number of beams for beam search. Higher can improve quality but is slower.
            num_return_sequences=1,  # We only want one best paraphrase here
            temperature=1.5,         # Controls randomness. Higher values (e.g., >1) make output more random.
                                     # Lower values (e.g., <1) make it more deterministic.
            # early_stopping=True    # Can be useful to stop generation when end-of-sentence token is produced
        )

        # Decode the generated IDs to text
        paraphrased_texts = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)

        # Since num_return_sequences=1, we expect one result in the list
        result_text = paraphrased_texts[0] if paraphrased_texts else "No paraphrase generated."

        return jsonify({"paraphrased_text": result_text})

    except Exception as e:
        app.logger.error(f"Error during paraphrasing: {e}", exc_info=True) # Log the full exception info
        return jsonify({"error": "An unexpected error occurred during paraphrasing."}), 500

# --- Run Flask App ---
if __name__ == '__main__':
    # Port can be configured via an environment variable or default to 5001
    port = int(os.environ.get('FLASK_PORT', 5001))
    # Note: debug=True is for development. Set to False in production.
    # Use a production-ready WSGI server like Gunicorn or uWSGI in production.
    app.run(host='0.0.0.0', port=port, debug=True)
```

## 3. Python Environment Setup

*   **Prerequisites:** Python 3.8+ is recommended.
*   **Dependencies:**
    *   `flask`: For creating the web server.
    *   `transformers`: Hugging Face library for using the PEGASUS model.
    *   `torch`: PyTorch library, the backend for the model. (If you have a compatible NVIDIA GPU and want to use it, ensure you install a CUDA-enabled version of PyTorch).
    *   `sentencepiece`: Required by the PEGASUS tokenizer.
    *   `accelerate`: Recommended by Hugging Face for optimizing model performance on various hardware setups.
*   **Installation:**
    It's highly recommended to use a Python virtual environment to manage dependencies for this service.
    ```bash
    # Navigate to the root of your project or where you want to create the service environment
    # Create a virtual environment (e.g., in the python_services directory)
    python -m venv .venv

    # Activate the virtual environment
    # On Windows:
    # .venv\Scripts\activate
    # On macOS/Linux:
    source .venv/bin/activate

    # Install the required packages
    pip install flask transformers torch sentencepiece accelerate

    # Optional: For a CPU-only version of PyTorch (if GPU is not available or intended):
    # pip install flask transformers torch --index-url https://download.pytorch.org/whl/cpu sentencepiece accelerate
    ```
*   **Model Downloading:**
    The first time the Python service (`app.py`) runs, the `transformers` library will automatically download the `tuner007/pegasus_paraphrase` model files from the Hugging Face Hub. This model is several hundred megabytes, so the initial startup might take some time depending on your internet connection. The model will be cached locally for subsequent runs (usually in `~/.cache/huggingface/hub/` or a similar directory).

## 4. Running the Python Service

1.  **Navigate to the service directory:**
    ```bash
    cd python_services/paraphrase_pegasus
    ```
2.  **Ensure your virtual environment is activated** (if you created one).
3.  **Run the Flask application:**
    ```bash
    python app.py
    ```
4.  **Expected Output:** You should see logs similar to this in your console:
    ```
    Loading tokenizer for tuner007/pegasus_paraphrase...
    Loading model tuner007/pegasus_paraphrase...
    Model using CPU. (or Model moved to GPU.)
    Model and tokenizer loaded successfully.
     * Serving Flask app 'app'
     * Debug mode: on
    WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
     * Running on http://0.0.0.0:5001
    Press CTRL+C to quit
    ```
5.  **Specifying a Different Port:**
    You can run the service on a different port by setting the `FLASK_PORT` environment variable before running the script:
    ```bash
    # On macOS/Linux
    FLASK_PORT=5002 python app.py

    # On Windows (PowerShell)
    # $env:FLASK_PORT="5002"; python app.py
    # On Windows (Command Prompt)
    # set FLASK_PORT=5002 && python app.py
    ```

## 5. Next.js Backend Integration (`src/services/paraphraseService.js`)

The Next.js backend communicates with the Python Flask service using the `fetch` API. The logic for this communication is encapsulated in `src/services/paraphraseService.js`.

```javascript
// src/services/paraphraseService.js

// Default URL for the Python paraphrasing service
// This can be overridden by setting the NEXT_PUBLIC_PARAPHRASE_API_URL environment variable
const PARAPHRASE_API_URL = process.env.NEXT_PUBLIC_PARAPHRASE_API_URL || 'http://localhost:5001/paraphrase';

/**
 * Calls the external Python service to paraphrase text using the PEGASUS model.
 * @param {string} textToParaphrase - The text string to be paraphrased.
 * @returns {Promise<object>} A promise that resolves to an object.
 * On success: { paraphrased_text: "...", error: false }
 * On failure: { error: true, message: "Error message details" }
 */
export async function paraphraseWithPegasus(textToParaphrase) {
    if (!textToParaphrase || typeof textToParaphrase !== 'string' || !textToParaphrase.trim()) {
        // This basic validation can also be done by the caller, but good to have defensively.
        return { error: true, message: 'Input text is required and must be a non-empty string.' };
    }

    console.log(`Attempting to paraphrase with PEGASUS service at: ${PARAPHRASE_API_URL}`);

    try {
        const response = await fetch(PARAPHRASE_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json', // Explicitly accept JSON
            },
            body: JSON.stringify({ text: textToParaphrase }),
            // Consider adding a timeout for the request if the service might hang
            // signal: AbortSignal.timeout(10000) // Example: 10 second timeout (requires Node.js 17.3+ or polyfill)
        });

        if (!response.ok) {
            let errorMessage = `Paraphrasing service responded with status: ${response.status}`;
            try {
                const errorData = await response.json();
                // Use error message from service if available, otherwise use status
                errorMessage = errorData.error || errorData.message || errorMessage;
            } catch (e) {
                // If parsing error response fails, stick with the status message
                console.warn('Could not parse error response from paraphrase service:', e.message);
            }
            console.error('Paraphrase service error:', errorMessage);
            return { error: true, message: errorMessage };
        }

        const data = await response.json();

        // Check if the Python service itself returned an error in its JSON response
        if (data.error) {
            console.error('Paraphrase service returned an application error:', data.error);
            return { error: true, message: data.error };
        }

        if (!data.paraphrased_text) {
            console.error('Paraphrase service response missing "paraphrased_text" field.');
            return { error: true, message: 'Paraphrase service response was invalid.' };
        }

        return { paraphrased_text: data.paraphrased_text, error: false };

    } catch (error) {
        // This catches network errors, DNS resolution errors, fetch exceptions, etc.
        console.error('Network or other exception calling paraphrase service:', error.message);
        let userFriendlyMessage = 'Failed to connect to the paraphrasing service.';
        if (error.name === 'AbortError') {
            userFriendlyMessage = 'The paraphrasing request timed out.';
        } else if (error.cause && error.cause.code === 'ECONNREFUSED') {
             userFriendlyMessage = `Connection refused by the paraphrasing service at ${PARAPHRASE_API_URL}. Is it running?`;
        }
        return { error: true, message: userFriendlyMessage };
    }
}
```

*   **Configuration:** The URL of the Python service can be configured in your Next.js application by creating a `.env.local` file in the root of your Next.js project and adding the following line:
    ```
    NEXT_PUBLIC_PARAPHRASE_API_URL=http://localhost:5001/paraphrase
    ```
    Replace `http://localhost:5001/paraphrase` if your Python service runs on a different URL or port.

## 6. Workflow Summary

1.  A user inputs text into the Next.js frontend (e.g., on the `src/pages/index.js` page).
2.  The frontend makes an API call to the Next.js backend API endpoint (`/api/process`).
3.  The `src/pages/api/process.js` handler receives the request.
4.  This handler calls the `paraphraseWithPegasus` function from `src/services/paraphraseService.js`.
5.  `paraphraseService.js` makes an HTTP `POST` request to the `/paraphrase` endpoint of the Python Flask service.
6.  The Python Flask service receives the text, processes it using the PEGASUS model, and returns the paraphrased text in a JSON response.
7.  The `paraphraseService.js` function receives this response and passes the paraphrased text (or an error) back to the `/api/process` handler.
8.  The `/api/process` handler may perform further modifications or checks (like AI detection) and then sends the final result back to the Next.js frontend to be displayed to the user.

## 7. Troubleshooting & Considerations

*   **Resource Usage:** The PEGASUS model is large and computationally intensive.
    *   **RAM:** Expect significant RAM usage (potentially 4GB+ when the model is loaded).
    *   **CPU:** Paraphrasing will be CPU-intensive. Performance will be noticeably slower without a GPU.
    *   **GPU:** For better performance, running the Python service on a machine with a compatible NVIDIA GPU (and correctly installed CUDA-enabled PyTorch) is highly recommended, especially for production or frequent use.
*   **Python Service Not Running:** If the Next.js application reports errors like "Failed to connect" or "Connection refused," ensure:
    *   The Python Flask service (`app.py`) is running.
    *   It's accessible on the URL and port configured in `NEXT_PUBLIC_PARAPHRASE_API_URL` (default `http://localhost:5001/paraphrase`).
    *   Check firewall settings if the Next.js application and the Python service are running on different machines or in different Docker containers.
*   **Model Download Issues:** On the first run of the Python service, if the model fails to download:
    *   Verify your internet connection.
    *   Check the Hugging Face Hub status page for any reported outages.
    *   Ensure you have enough disk space in the cache directory.
*   **Error Messages:**
    *   Check the console output of the Python Flask service for any error messages related to model loading, dependency issues, or runtime errors during paraphrasing.
    *   Check the console output of your Next.js backend (where `process.js` runs) for errors related to the `fetch` call or response handling.
*   **Commercial Use of PEGASUS Model:**
    *   The `tuner007/pegasus_paraphrase` model is licensed under `Apache 2.0`, which is generally permissive for commercial use.
    *   However, it's always good practice to review the licenses of all models and their dependencies if you intend to use them in a commercial product.
*   **Development vs. Production for Python Service:**
    *   The Flask development server (`app.run(debug=True)`) is not suitable for production. For deploying this Python service in a production environment, use a production-grade WSGI server like Gunicorn or uWSGI.

This guide should provide a solid foundation for setting up and using the PEGASUS paraphrasing service in conjunction with your Next.js application.
